<template>
  <div class="relative">
    <div class="md:hidden sticky z-[55] top-0 left-0 w-full font-secondaryFont uppercase rtl:font-notoSans text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
      {{ locale === 'en' ? pagesContent?.contact?.title : pagesContent?.contact?.title_ar }}
    </div>
    <div class="mt-[60px] hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="bg-cartColor rounded-2xl p-5 sm:p-10 text-black">
          <div class="flex md:gap-20 gap-4 justify-between flex-col md:flex-row items-center">
            <div class="font-secondaryFont text-sm sm:text-lg text-primary rtl:font-notoSans">
              {{ locale === 'en' ? pagesContent?.contact?.page_banner_desc : pagesContent?.contact?.page_banner_desc_ar }}
            </div>
            <UButton
              class="
                rounded-full
                flex
                items-center
                justify-center
                font-bold
                sm:text-xl
                py-3
                sm:px-20
                px-14
                font-thirdFont
                text-thirdColor
                uppercase
                bg-orangeColor
                border-2
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                transition-all
                duration-500
                ease-in-out"
                :to="localePath(`/${pagesContent?.contact?.banner_link_slug}`)"
            >
              {{ locale === 'en' ? pagesContent?.contact?.banner_link_text : pagesContent?.contact?.banner_link_text_ar }}
            </UButton>
          </div>

        </div>
      </div>
    </div>
    <div class="md:mt-[60px] mt-5">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="md:mt-14 font-secondaryFont md:font-primaryFont rtl:font-notoSans text-primary md:text-lg text-center md:w-[75%] mx-auto mb-14">
          {{ $t("If you're having any issues with your order or have a general inquiry, feel free to send us a message using the form shown below. We’d love to hear from you.") }}
          <div class="mt-8 contact-responsive">
            <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">

              <!-- Subject field -->
              <div class="text-field-nobg-13 relative">
                <UFormGroup name="subject">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      py-2
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0"
                    variant="outline"
                    v-model="state.subject"
                    required
                    placeholder=" "
                    aria-label="Subject"
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-primaryFont
                      font-semibold
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      rtl:font-notoSans
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-13' : state.subject,
                      }"
                    >
                      {{ $t('Subject') }}
                  </span>
                </UFormGroup>
              </div>

              <!-- Order ID field (optional) -->
              <div class="text-field-nobg-13 relative">
                <UFormGroup name="order_id">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      py-2
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    variant="outline"
                    v-model="state.order_id"
                    type="number"
                    placeholder=" "
                    aria-label="Order ID"
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-primaryFont
                      font-semibold
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      rtl:font-notoSans
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-13' : state.order_id,
                      }"
                    >
                      {{ $t('Order ID (Optional)') }}
                  </span>
                </UFormGroup>
              </div>


              <!-- Message field -->
              <div class="text-field-nobg-13 relative">
                <UFormGroup name="description">
                  <textarea
                    v-model="state.description"
                    class="
                      text-lg
                      w-full
                      text-primary
                      font-bold
                      font-secondaryFont
                      py-3
                      px-4
                      border-2
                      border-primary
                      rounded-lg
                      focus:outline-none
                      min-h-[150px]
                    "
                    placeholder=" "
                    required
                    aria-label="Your message"
                  ></textarea>
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-primaryFont
                      font-semibold
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      rtl:font-notoSans
                      md:top-[13px]
                      top-[20px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-13' : state.description,
                      }"
                    >
                      {{ $t('YOUR MESSAGE') }}
                  </span>
                </UFormGroup>
              </div>
              <!-- Submit button -->
              <div class="flex justify-center">
                <UButton
                  type="submit"
                  class="
                    font-thirdFont
                    font-bold
                    text-lg
                    text-thirdColor
                    py-2
                    md:px-32
                    px-14
                    md:mt-5
                    rounded-full
                    uppercase
                    border
                    md:border-orangeColor
                    md:bg-orangeColor
                    bg-primary
                    border-primary
                    hover:border-orangeColor
                    hover:bg-orangeColor
                    ease-in-out
                    duration-500
                    rtl:font-notoSans
                    disabled:opacity-70
                    disabled:cursor-not-allowed
                  "
                  variant="solid"
                  :disabled="loading"
                >
                  <span v-if="loading">{{ $t('SENDING...') }}</span>
                  <span v-else>{{ $t('SUBMIT') }}</span>
                </UButton>
              </div>
            </UForm>
          </div>
        </div>
      </div>
    </div>
    <div class="px-[20px] max-w-[1280px] mx-auto mb-[100px]">
      <div class="grid grid-cols-1 sm:grid-cols-2 border-2 border-primary rounded-[6px] md:p-20 p-6">
        <div
          v-for="(item, index) in pagesContent?.contact?.contact_info ?? []"
          :key="index"
          :class="[
            'py-6',
            { 'sm:border-e sm:border-primary sm:pe-8 md:pe-32': index % 2 === 0 },
            { 'sm:ps-8 md:ps-32': index % 2 !== 0 },
            { 'border-b border-primary sm:border-b-0': index < pagesContent?.contact?.contact_info.length - 1 }
          ]"
        >
          <h2 class="font-bold text-xl md:text-2xl font-primaryFont text-primary">{{ item?.contact_title }}</h2>
          <div class="font-semibold font-primaryFont text-orangeColor text-lg md:text-xl mt-2 md:mt-4">
            {{ item?.contact_country }}
          </div>
          <div class="font-[500] font-primaryFont text-orangeColor text-base md:text-lg">
            {{ item?.contact_city }}
          </div>
          <div class="font-primaryFont text-primary text-sm md:text-[15px] mt-2 md:mt-4">
            {{ item?.contact_address }}
          </div>
          <div class="font-primaryFont text-primary text-sm md:text-[15px]">
            {{ item?.contact_mobile }}
          </div>
          <div class="font-primaryFont text-primary text-sm md:text-[15px] font-[500]">
            {{ item?.contact_email }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// Define FormSubmitEvent type since we can't import from #ui/types
interface FormSubmitEvent<T> {
  data: T;
  event: Event;
}
import { z } from 'zod'
import { ElNotification } from 'element-plus'
import type { PagesContent } from '~/types/pagesContent'
import type { UserData } from '~/types/user'

/**
 * Interface for contact form data
 */
interface ContactFormData {
  subject: string;
  order_id?: number;
  user_id?: number;
  description: string;
}

/**
 * Interface for API response
 */
interface ContactFormResponse {
  success: boolean;
  message: string;
}

const { locale, t } = useI18n()
const localePath = useLocalePath()


// Get page content from global state
const pagesContent = useState<PagesContent>("pagesContent", () => ({} as PagesContent))
const userData = useState<UserData>("userData", () => ({} as UserData))
const loading = ref<boolean>(false)

// Form validation schema using Zod
const schema = z.object({
  subject: z.string()
    .min(3, t('Subject must be at least 3 characters'))
    .max(100, t('Subject must be less than 100 characters')),
  order_id: z.number().optional(),
  description: z.string()
    .min(6, t('Message must be at least 6 characters'))
    .max(1000, t('Message must be less than 1000 characters')),
})

// Type for form data based on schema
type Schema = z.output<typeof schema>

// Form state
const state = reactive<ContactFormData>({
  subject: '',
  order_id: undefined,
  user_id: userData.value?.id,
  description: '',
})

/**
 * Show success notification
 */
const showSuccessNotification = (): void => {
  ElNotification({
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Your message has been sent successfully!'),
    position: 'top-right',
  })
}

/**
 * Show error notification
 */
const showErrorNotification = (message: string): void => {
  ElNotification({
    message: h('i', { style: 'color: #FF671F; font-weight: bold;' }, message || 'An error occurred. Please try again.'),
    position: 'top-right',
  })
}

/**
 * Handle form submission
 */
async function onSubmit(_event: FormSubmitEvent<Schema>): Promise<void> {
  // try {
  //   loading.value = true;

  //   // Prepare form data
  //   const formData: ContactFormData = {
  //     subject: state.subject,
  //     order_id: state.order_id,
  //     user_id: userData.value?.id,
  //     description: state.description,
  //   };

  //   // Send form data to API
  //   const response = await $fetch<ContactFormResponse>('/api/support/sendSupport', {
  //     method: 'post',
  //     body: formData
  //   });

  //   // Show success message
  //   if (response.success) {
  //     showSuccessNotification();

  //     // Reset form
  //     state.subject = '';
  //     state.order_id = undefined;
  //     state.description = '';
  //   } else {
  //     showErrorNotification(response.message);
  //   }
  // } catch (error) {
  //   console.error('Error submitting contact form:', error);
  //   showErrorNotification('Failed to send your message. Please try again later.');
  // } finally {
  //   loading.value = false;
  // }
}
</script>
<style>
@media (max-width: 768px) {
  .contact-responsive .custom-field-input input {
    background-color: #E9EAC8 !important;
    border-radius: 5165231px !important;
    padding: 10px 20px !important;
    font-size: 14px;
  }
  .contact-responsive .custom-field-input {
    margin-bottom: 0 !important;
  }
  .contact-responsive .custom-field-input input::placeholder,
  .contact-responsive .custom-field-select textarea::placeholder {
    color: #1D3C34CC !important;
    font-family: Oxygen, sans-serif;
  }
  .contact-responsive .custom-field-select textarea {
    background-color: #E9EAC8 !important;
    padding: 10px 20px !important;
    font-size: 14px;
    border-radius: 20px;
    border: 1px solid #1D3C34;
  }
}
</style>