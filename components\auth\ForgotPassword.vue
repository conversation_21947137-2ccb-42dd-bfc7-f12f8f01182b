<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="pt-10 relative sm:max-w-[60%] mx-auto border-b border-primary pb-8">
      <div class="md:mt-5">
        <!-- Title -->
        <div class="text-center mb-12">
          <h1 class="font-secondaryFont font-bold text-primary text-xl md:text-2xl mb-2">
            {{ $t('Forgot Your Password?') }}
          </h1>
          <p class="font-secondaryFont font-bold text-primary text-xl md:text-2xl mb-2">
            {{ $t("Don't worry—it happens to the best of us.") }}
          </p>
        </div>
        <!-- Form -->
        <UForm
          ref="form"
          :schema="schema"
          :state="state"
          class="space-y-4"
          @submit="onSubmit"
          aria-label="Forgot password form"
        >
          <div class="w-full mx-auto flex flex-col gap-6">
            <!-- Phone number field -->
            <div class="text-field-bg-13 relative login-register">
              <UFormGroup name="phone_number" label-class="sr-only">
                <label for="phone-input" class="sr-only">{{ $t('Registered Phone Number') }}</label>
                <input
                  id="phone-input"
                  class="w-full border border-primary rounded-full !bg-thirdColor py-2 px-5 md:py-3 md:px-5 md:rounded-lg focus:outline-0"
                  type="tel"
                  inputmode="numeric"
                  pattern="[0-9]*"
                  autocomplete="tel"
                  v-model="state.phone_number"
                  required
                  placeholder=" "
                  :disabled="!!tokenCookie"
                  aria-label="Registered Phone Number"
                >
                
                <span
                  class="uppercase absolute text-primary font-semibold md:text-base text-[13px] z-[4] rtl:right-[22px] ltr:left-[22px] md:top-[13px] top-[11px] transition-all duration-100 custom-auth-style ease-in-out"
                  :class="{ 'placeholder-animation-bg-13': state.phone_number }"
                >
                  {{ $t('Registered Phone Number') }}
                </span>
              </UFormGroup>
            </div>

            <!-- Submit button -->
            <div class="flex justify-center">
              <UButton
                type="submit"
                class="
                  font-thirdFont
                  font-bold
                  text-lg
                  text-thirdColor
                  py-3
                  px-12
                  mt-4
                  rounded-full
                  border
                  bg-primary
                  border-primary
                  hover:border-orangeColor
                  hover:bg-orangeColor
                  ease-in-out
                  duration-500
                  uppercase
                  w-full
                  md:w-auto
                  min-w-[200px]
                  flex
                  items-center
                  justify-center
                  relative
                "
                variant="solid"
                :disabled="isLoading"
              >
                <span :class="{ 'opacity-0': isLoading }">{{ tokenCookie ? $t('Confirm') : $t('Verify Phone Number') }}</span>
                <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center">
                  <div class="w-5 h-5 border-2 border-thirdColor border-t-transparent rounded-full animate-spin"></div>
                </div>
              </UButton>
            </div>
          </div>
        </UForm>

        <!-- Register/Login links -->
        <div class="text-center mt-8 mb-2" v-if="!tokenCookie">
          <div class="flex items-center justify-center gap-2 text-primary text-sm md:text-base">
            <ULink
              :to="localePath('/auth/register')"
              class="underline hover:text-orangeColor transition-colors duration-300"
              aria-label="Register for new account"
            >
              {{ $t('Register') }}
            </ULink>
            <span>|</span>
            <ULink
              :to="localePath('/auth/login')"
              class="underline hover:text-orangeColor transition-colors duration-300"
              aria-label="Login to existing account"
            >
              {{ $t('Login') }}
            </ULink>
          </div>
        </div>
      </div>
    </div>

    <!-- Social login options -->
    <AuthSocialLogin v-if="!tokenCookie"/>
    <ModalsAuthSocialVerify v-if="showModal" />
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import { ElNotification } from 'element-plus'
import { persistentCookieOptions } from '~/utils/cookieOptions'
import type { UserData } from '~/types/user'

const tokenCookie = useCookie('token', persistentCookieOptions)
const showModal: any = useState("showModal", () => false)
const phoneNumber: any = useState("phoneNumber", () => '')
const isLoading = ref<boolean>(false)
const urlError = useState<string>("urlError", () => '')
const titleError = useState<string>("titleError", () => '')
const userData = useState<UserData>("userData", () => ({} as UserData))

const { t } = useI18n()
const localePath = useLocalePath()
// Reset error states
urlError.value = ''
titleError.value = ''

/**
 * Form validation schema
 */
const schema = z.object({
  phone_number: z.string()
    .min(11, t('Phone number must be at least 11 characters'))
    .regex(/^\d+$/, t('Phone number must contain only digits'))
})

type Schema = z.output<typeof schema>

/**
 * Form state
 */
const state = reactive({
  phone_number: ''
})

/**
 * Notification helper
 */
const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  ElNotification({
    message: h('i', {
      style: `color: ${type === 'success' ? '#1D3C34' : '#ff4949'}; font-weight: bold;`
    }, message),
    position: 'top-right',
    duration: 3000,
    type
  })
}
// If user is logged in, set their phone number
onMounted(() => {
  if (tokenCookie.value && userData.value?.username) {
    phoneNumber.value = userData.value.username
    state.phone_number = userData.value.username
  }
})

/**
 * Handle form submission
 */
async function onSubmit() {
  try {
    isLoading.value = true
    
    // First, check if the phone number exists
    const response: any = await $fetch('/api/newBranch/forgotPassword/validate', {
      method: 'POST',
      body: {
        phone: state.phone_number
      }
    })

    if (response.error) {
      showNotification(response.error, 'error')
      return
    }

    if (response.success) {
      showNotification(`${t('Choose the method to send the code to')} ${state.phone_number}`, 'success')
      phoneNumber.value = state.phone_number
      // TODO: Proceed with sending verification code
      showModal.value = true
    }
  } catch (error: any) {
    console.error('Error:', error)
    showNotification(error.message || 'An error occurred', 'error')
  } finally {
    isLoading.value = false
  }
}
</script>