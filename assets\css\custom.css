
.box-shadow {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.button-shadow {
  box-shadow: 0px 2px 4px 0px #00000040;
}
.custom-padding {
  padding: 18px 20px !important;
  height: 32px !important;
}

.lines-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.lines-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}
.lines-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.vh--image{
  max-width: none!important;
}
.custom-rating .el-rate  {
  gap: 10px;
}
.custom-rating .el-icon svg {
  width: 40px;
  height: 40px;
}

.card-rating .el-rate  {
  height: 20px;
  --el-rate-disabled-void-color: #0f030338;
  --el-rate-icon-size: 22px;
  --el-rate-icon-margin: 0;
}

/* Register login */
.information .el-select__wrapper {
  padding: 1rem !important;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-family: Oxygen, sans-serif;
  font-weight: bold;
  --tw-border-opacity: 1;
  border-color: rgb(var(--color-primary-DEFAULT) / var(--tw-border-opacity));
  border-width: 2px;
  border-radius: 0.5rem;
  height: 52px;
  box-shadow: 0px 2px 4px 0px #00000040;
}
.information .el-select__wrapper.is-focused {
  box-shadow: unset;
}
.information .el-select__placeholder {
  color: #1D3C34;
}
.information .el-select__caret {
  color: #1D3C34;
  font-size: 16px;
}
/* //// */

/* Cars */
.select-cars .el-select__wrapper {
  color: #1D3C34;
  font-weight: bold;
  background-color: #fff;
  font-size: 16px;
  padding: 15px 15px !important;
  border-radius: 7px !important;
  border: 2px solid #1D3C34;
  outline: none;
  text-transform: uppercase;
}
.select-cars .el-select__placeholder.is-transparent {
  color: #1D3C34;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
}
.select-cars .el-select__caret {
  font-size: 18px;
  color: #1D3C34;
  background-color: #fff;
  font-weight: bold;
}
.select-cars .el-select__wrapper.is-focused {
  box-shadow: none;
}
@media (max-width: 640px) {
  .select-cars .el-select__wrapper {
    padding: 10px 15px !important;
    border-radius: 7px !important;
    border: 2px solid #1D3C34;
    font-size: 14px;
  }
  .select-cars .el-select__placeholder.is-transparent {
    color: #1D3C34;
    font-size: 14px;
    font-weight: bold;
  }
}
/* ///// */


/* Cart */
/* .cartTable tr:nth-child(even) {
  background-color: #F5F5F5;
} */
.cartCheckBox .el-checkbox__inner:after {
  height: 13px;
  width: 6px;
  left: 9px;
  top: 1px;
  font-weight: bold;
}
.cartCheckBox .el-checkbox.el-checkbox--large .el-checkbox__inner {
  height: 25px;
  width: 25px;
}
.cartCheckBox .el-checkbox__input.is-checked .el-checkbox__inner {
  border: 1px solid #1D3C34 !important;
  background-color: #1D3C34 !important;
}
.cartCheckBox .el-checkbox__inner {
  border: 1px solid #1D3C34 !important;
}
.table-responsive {
  overflow-x: auto;  
  -webkit-overflow-scrolling: touch;  
}
.cartTable {
  width: 100%; 
  border-collapse: collapse;  
}


@media (max-width: 768px) {
  .toHidden {
    display: none;
  }

  .cartTable td {
    display: block;  /* Make table rows display as block */
    width: 100%;  /* Full width for each cell */
  }

  .forResponsive {
    flex-wrap: wrap;
    justify-content: center;
  }

  .cartTable td:before {
    content: attr(data-label);  /* Use data-label attribute as content */
    position: absolute;
    left: 0;
    width: 50%;
    padding-left: 15px;
    font-weight: bold;
    text-align: left;
  }

  .cartTable tr {
    margin-bottom: 10px;  /* Add spacing between rows */
  }

  .cartCheckBox {
    width: 10%;  /* Adjust image size for mobile */
  }
  .cartTable img {
    width: 70%;  /* Adjust image size for mobile */
  }
}
/* ////// */

/* Filters */
.select-filter .el-select__wrapper,
.el-select__wrapper.is-disabled {
  color: #1D3C34 !important;
  font-weight: bold !important;
  background-color: #E9EAC8 !important;
  font-size: 16px;
  padding: 15px 15px !important;
  border-radius: 7px !important;
  border: 1px solid #1D3C34;
  outline: none;
  font-style: italic;
  text-transform: uppercase;
}
.el-select__wrapper.is-disabled {
  opacity: 0.8 !important;
}
.select-filter .el-select__placeholder.is-transparent {
  color: #1D3C34;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
}
.select-filter .el-select__caret {
  font-size: 18px;
  color: #1D3C34;
  background-color: #E9EAC8;
  font-weight: bold;
}
@media (max-width: 640px) {
  .select-filter .el-select__wrapper {
    padding: 10px 15px !important;
    border-radius: 7px !important;
    border: 1px solid #1D3C34;
    font-size: 14px;
  }
  .select-filter .el-select__placeholder.is-transparent {
    color: #1D3C34;
    font-size: 14px;
    font-weight: bold;
  }
}
/* ////// */

/* Pagination */
.pagination-button {
  width: 40px;
  height: 40px;
  border: 2px solid #1D3C34;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.pagination-button.active {
  background-color: #1D3C34;
  color: white;
}

.pagination-button:hover {
  background-color: #1D3C34;
  color: white;
}
/* ////// */

/* ADS */
.ads-slider .swiper-pagination-bullets.swiper-pagination-horizontal {
  transform: translateY(15px);
  height: 40px;
  position: relative;
  display: flex;
  width: 100%;
  text-align: center;
  justify-content: center;
  bottom: -5px;
}
.ads-slider .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet {
  box-shadow: 0px 4px 4px 0px #00000040 !important;
  border: 2px solid #000 !important;
  background: rgb(48 44 44) !important;
  width: 15px;
  height: 15px;
}
.ads-slider .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #1D3C34 !important;
}
/* ////// */

/* TIPS */
.title-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px; /* Adjust height as needed */
  background-color: black;
  opacity: 1; /* Adjust opacity for transparency */
}
/* ////// */

/* Header */
.select-ui .el-select__wrapper {
  width: 100% !important;
  padding: 25px 32px !important;
  border-radius: 50px !important;
  height: 40px;
  background: #E9EAC8 !important;
}
.select-ui .el-select__placeholder.is-transparent {
  color: #1D3C34 !important;
  font-family: "Oxygen", sans-serif !important;
}
@media (max-width: 768px) {
  .select-ui .el-select__wrapper {
    padding: 10px 14px !important;
    max-height: 40px;
    font-size: 12px;
  }
}
.stickyu {
  position: sticky;
  top: 70px;
  left: 0;
  right: 0;
  z-index: 123123123;
  width: 100%;
}
.el-dropdown-menu__item {
  font-weight: bold;
}
/* ////// */




/* Footer */
.footer-input .el-input__wrapper {
  padding: 10px 20px;
  border-radius: 50px;
  border: 1px solid #1D3C34;
  outline: none;
  background-color: #E9EAC8 !important;
  color: #1D3C34 !important;
}

.footer-input .el-input__inner::placeholder {
  color: #1D3C34 !important;
}

@media (max-width: 640px) {
  .footer-input .el-input__wrapper {
    padding: 0 15px;
    font-size: 12px;
  }
}
/* ////// */
/* Overight */
.el-input__wrapper.is-focus {
  box-shadow: 0px 2px 4px 0px #00000040;
}
/* //// */


/* CUSTOM SELECTTTTTTTTTTT */
.custom-field-select {
  position: relative !important;
  display: inline-block !important;
  --field-padding: 12px !important;
  width: 100% !important;
  margin-bottom: 25px !important;
}

.custom-field-select .el-select__selected-item.el-select__placeholder.is-transparent {
  display: none !important;
}
.custom-field-select .el-select__selected-item.el-select__placeholder {
  color: #1D3C34 !important;
  font-family: "Montserrat", sans-serif !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

.custom-field-select .el-select__wrapper.is-focused, .el-select__wrapper {
  border: 1px solid #1D3C34 !important;
  padding: 15px !important;
  border-radius: 8px;
}
.custom-field-select .el-icon.el-select__caret.el-select__icon {
  color: #1D3C34 !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

.custom-field-select .placeholder {
  position: absolute !important;
  left: var(--field-padding) !important;
  width: fit-content !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  top: 0 !important;
  background-color: #fff !important;
  line-height: 100% !important;
  transform: translateY(-50%) !important;
  font-family: "Oxygen", sans-serif !important;
  color: #1D3C34 !important;
  font-size: 12px !important;
  padding: 0 10px !important;
}

/* //////////////////// */








































.el-select__popper.el-popper {
  background-color: #E9EAC8 !important;
  border: 1px solid #263238 !important;
  box-shadow: 0px 2px 2px 0px #00000040 !important;
}
.el-popper__arrow {
  display: none !important;
}
.el-select-dropdown__item {
  font-weight: 600 !important;
  font-family: "Montserrat", sans-serif !important;
  font-style: italic !important;
  font-size: 16px !important;
  padding: 0 8px 0 8px !important;
  border-bottom: 1px solid #263238 !important;
}
.el-select-dropdown__list {
  padding: 6px 15px !important;
}
.el-select-dropdown__item.is-hovering {
  background-color: #E9EAC8 !important;
  color: #FF671F !important;
}
.el-select__selected-item.el-select__placeholder {
  color: #FF671F !important;
  font-weight: 600 !important;
  font-family: "Montserrat", sans-serif !important;
  font-style: italic !important;
  font-size: 16px !important;
}
.el-select__selected-item.el-select__placeholder.is-transparent {
  color: #1D3C34 !important;
  font-weight: 600 !important;
  font-family: "Montserrat", sans-serif !important;
  font-style: italic !important;
  font-size: 16px !important;
}
.el-select__wrapper.is-focused {
  box-shadow: 0px 2px 2px 0px #00000040 !important;
}

.el-select-dropdown__item:last-child {
  border-bottom: none !important;
} 
.custom-field-input-third {
  position: relative !important;
  display: inline-block !important;
  --field-padding: 12px !important;
  width: 100% !important;
  margin-bottom: 15px !important;
}
.custom-field-input-third input {
  background-color: #E9EAC8 !important;
}

.custom-field-input-third .information .el-select__wrapper {
  background-color: #E9EAC8 !important;
}
.custom-field-input-third .placeholder {
  position: absolute !important;
  left: var(--field-padding) !important;
  width: auto !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  top: 0 !important;
  background-color: #E9EAC8 !important;
  line-height: 100% !important;
  transform: translateY(-50%) !important;
  font-family: "Oxygen", sans-serif !important;
  color: #1D3C34 !important;
  font-size: 12px !important;
  padding: 0 10px !important;
  z-index: 1;
}

.border-none-custom:last-child {
  border-bottom: none !important;
}
.el-dropdown__popper.el-popper,
.el-dropdown__popper.el-popper * {
  background-color: #E9EAC8 !important;
  color: #1D3C34 !important;
}
@media (max-width: 768px) {
  .el-dropdown__popper.el-popper,
  .el-dropdown__popper.el-popper * {
    display: none;
  } 
}


.custom-field-input {
  position: relative !important;
  display: inline-block !important;
  --field-padding: 12px !important;
  width: 100% !important;
  margin-bottom: 15px !important;
}
.custom-field-input input {
  background-color: #fff !important;
}

.custom-field-input .placeholder {
  position: absolute !important;
  left: var(--field-padding) !important;
  width: auto !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  top: 0 !important;
  background-color: #fff !important;
  line-height: 100% !important;
  transform: translateY(-50%) !important;
  font-family: "Oxygen", sans-serif !important;
  color: #1D3C34 !important;
  font-size: 12px !important;
  padding: 0 10px !important;
  z-index: 1;
}

.text-events {
  pointer-events: none;
}



















@media (max-width: 768px) {
  .select-status-responsive.support-select-status {
    width: 130px !important;
  }
  .select-status-responsive.support-select-status .el-select__wrapper {
    background-color: #FF671F !important;
    color: #E9EAC8 !important;
    border: none !important;
    border-radius: 12312312px;
  }
  .select-status-responsive.support-select-status .el-select__wrapper .el-select__selected-item.el-select__placeholder {
    color: #E9EAC8 !important;
    font-weight: bold !important;
    font-style: normal !important;
    font-size: 12px !important;
  }
  .select-status-responsive.support-select-status .el-select__caret {
    color: #E9EAC8 !important;
  }
  .faq-content-container .el-collapse-item__header {
    padding: 0 !important;
    overflow: hidden;
  }
}

.el-notification {
  background-color: #E9EAC8 !important;
  border: 2px solid #FF671F !important;
}
.el-notification * {
  color: #FF671F !important;
  text-align: center !important;
  font-style: normal !important;
  font-size: 16px !important;
  font-weight: bold !important;
  font-family: "Montserrat", sans-serif !important;
  width: 100%;
  text-align: center;
}






















/* Animation placeholder */
.text-field-nobg-mob span,
.text-field span,
.text-field-nobg span,
.text-field-nobg-13 span {
  pointer-events: none;
  opacity: 0.5;
}
.text-field input,
.text-field textarea {
  color: #1D3C34;
}

.text-field input:focus:invalid+span,
.text-field input:not(:placeholder-shown):invalid+span,
.text-field textarea:focus:invalid+span,
.text-field textarea:not(:placeholder-shown):invalid+span {
  top: -8px;
  background-color: #E9EAC8;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}

.placeholder-animation-with-bg {
  top: -8px;
  background-color: #E9EAC8;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}

.text-field-nobg-13 input:focus:invalid+span,
.text-field-nobg-13 input:not(:placeholder-shown):invalid+span,
.text-field-nobg-13 textarea:focus:invalid+span,
.text-field-nobg-13 textarea:not(:placeholder-shown):invalid+span {
  top: -13px;
  background-color: #fff;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}
.placeholder-animation-13 {
  top: -13px;
  background-color: #fff;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}


.text-field-bg-13 input:focus:invalid+span,
.text-field-bg-13 input:not(:placeholder-shown):invalid+span,
.text-field-bg-13 textarea:focus:invalid+span,
.text-field-bg-13 textarea:not(:placeholder-shown):invalid+span {
  top: -13px;
  background-color: #E9EAC8;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}
.placeholder-animation-bg-13 {
  top: -13px;
  background-color: #E9EAC8;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}



/* For Mobile */
.text-field-nobg-mob input:focus:invalid+span,
.text-field-nobg-mob input:not(:placeholder-shown):invalid+span,
.text-field-nobg-mob textarea:focus:invalid+span,
.text-field-nobg-mob textarea:not(:placeholder-shown):invalid+span {
  top: -9px;
  font-size: 12px;
  background-color: #fff;
  padding: 1px 5px;
  opacity: 1 !important;
}

.placeholder-animation-mob {
  top: -9px;
  background-color: #fff;
  font-size: 12px;
  padding: 1px 5px;
  opacity: 1 !important;
}
.text-field-nobg-mob .el-select__wrapper {
  height: 40px;
  border-radius: 99999px;
}

@media (max-width: 768px) {
  .text-field-nobg-13 input:focus:invalid+span,
  .text-field-nobg-13 input:not(:placeholder-shown):invalid+span,
  .text-field-nobg-13 textarea:focus:invalid+span,
  .text-field-nobg-13 textarea:not(:placeholder-shown):invalid+span {
    top: -8px;
  }
  .placeholder-animation-13 {
    top: -8px;
  }
}

.custom-auth-style {
  opacity: .5;
  pointer-events: none;
}


/* Variation */
.variations-filter .uppercase.absolute.text-primary.font-primaryFont.font-semibold.opacity-1.font-base.italic.transition-all.duration-100.ease-in-out{
  pointer-events: none;
}