<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="pt-10 relative mx-auto mb-[80px]">
        <div>
          <div class="text-primary font-bold font-secondaryFont text-[15px] text-center rtl:font-notoSans">{{ $t('Create A New Account!') }}</div>
          <div class="mt-5">
            <div v-if="serverStatus" class="mb-2 text-red-700 font-bold text-[15px] rtl:font-notoSans">{{ serverErrors === $t('Sorry, that username already exists!') ? $t('Sorry, that Phone Number already exists!') : serverErrors }}</div>
              <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
                <div class="w-full mx-auto flex flex-col gap-6">
                  <div class="text-field-nobg-mob relative">
                  <UFormGroup name="first_name">
                    <input
                      class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0 relative"
                      variant="outline"
                      v-model="state.first_name"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        text-[14px]
                        ltr:left-[25px]
                        rtl:right-[25px]
                        rtl:font-notoSans
                        top-[10px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-mob' : state.first_name,
                        }"
                      >
                        {{ $t('First Name') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-nobg-mob relative">
                  <UFormGroup name="last_name">
                    <input
                      class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                      variant="outline"
                      v-model="state.last_name"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        text-[14px]
                        ltr:left-[25px]
                        rtl:right-[25px]
                        rtl:font-notoSans
                        top-[10px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-mob' : state.last_name,
                        }"
                      >
                        {{ $t('Last Name') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-nobg-mob relative">
                  <UFormGroup name="username">
                    <input
                      class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                      variant="outline"
                      v-model="state.username"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        text-[14px]
                        ltr:left-[25px]
                        rtl:right-[25px]
                        rtl:font-notoSans
                        top-[10px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-mob' : state.username,
                        }"
                      >
                        {{ $t('Phone Number') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-nobg-mob relative">
                  <UFormGroup name="email">
                    <input
                      class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                      variant="outline"
                      v-model="state.email"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        text-[14px]
                        ltr:left-[25px]
                        rtl:right-[25px]
                        rtl:font-notoSans
                        top-[10px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-mob' : state.email,
                        }"
                      >
                        {{ $t('Email') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-nobg-mob relative">
                  <UFormGroup name="password">
                    <input
                      class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                      variant="outline"
                      v-model="state.password"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        text-[14px]
                        ltr:left-[25px]
                        rtl:right-[25px]
                        rtl:font-notoSans
                        top-[10px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-mob' : state.password,
                        }"
                      >
                        {{ $t('Password') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-nobg-mob relative">
                  <UFormGroup name="password_confirm">
                    <input
                      class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                      variant="outline"
                      v-model="state.password_confirm"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        text-[14px]
                        ltr:left-[25px]
                        rtl:right-[25px]
                        rtl:font-notoSans
                        top-[10px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-mob' : state.password_confirm,
                        }"
                      >
                        {{ $t('Confirm Password') }}
                    </span>
                  </UFormGroup>
                </div>
              </div>
              <div class="text-center !mt-8 flex gap-5 justify-center items-center">
                <UButton
                  type="submit"
                  class="
                    text-thirdColor
                    font-bold
                    font-thirdFont
                    uppercase
                    rounded-full
                    py-3
                    border
                    border-primary
                    bg-primary
                    shadow-unset
                    lg:px-[150px]
                    md:px-[100px]
                    rtl:font-notoSans
                    px-[90px]
                    hover:scale-[1.1]
                    ease-in-out
                    duration-500
                  "
                  variant="solid"
                  :loading="isLoading"
                >
                  <div v-if="!isLoading">{{ $t('REGISTER') }}</div>
                </UButton>
              </div>
            </UForm>
          </div>
        </div>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123] md:hidden">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans flex justify-center gap-1 items-center">
        <span class="rtl:mt-0.5">{{ $t('YOUR CART') }} ({{ totalItems || '0' }})</span> |
        <span class="rtl:flex rtl:gap-2 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span>
        </span>
      </div>
      <div class="flex justify-center">
        <UButton
          class="
            rounded-full
            flex
            items-center
            justify-center
            font-bold
            text-[15px]
            w-full
            py-2
            font-thirdFont
            text-thirdColor
            uppercase
            bg-orangeColor
            border-2
            rtl:font-notoSans
            border-orangeColor
            hover:bg-primary
            hover:border-primary
            focus:bg-primary
            focus:border-primary
            transition-all
            duration-500
            ease-in-out"
            :to="localePath('/cart/mobile/userinfo')"
        >
          {{ $t('BACK TO GUEST') }}
        </UButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const router = useRouter()
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => {})

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

const { t } = useI18n()
const localePath = useLocalePath()

const isLoading = ref<boolean>(false)
const mobileCart: any = useState("mobileCart", () => true)
const cartFulltem: any = useState("cartFulltem", () => {})
const codeStatus: any = useState("codeStatus", () => false)
const phoneNumber: any = useState("phoneNumber", () => '')
const formStatus: any = useState("formStatus", () => null)

const showVerificationModal: any = useState("showVerificationModal", () => false)

const schema = z.object({
  email: z.string().email(t('Invalid email')),
  username: z.string()
    .min(11, t('Phone must be at least 11 characters')),
    password: z.string()
    .min(6, t('Password must be at least 6 characters')),
    password_confirm: z.string()
    .min(6, t('Password confirm must be at least 6 characters')),
    first_name: z.string()
    .min(1, t('First name must be at least 6 characters')),
    last_name: z.string()
    .min(1, t('Last name must be at least 6 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  username: undefined,
  email: undefined,
  password: undefined,
  password_confirm: undefined,
  first_name: undefined,
  last_name: undefined,
  roles: 'customer'
})


Object.assign(state, {
  username: undefined,
  email: undefined,
  password: undefined,
  password_confirm: undefined,
  first_name: undefined,
  last_name: undefined,
  roles: 'customer'
})

const serverErrors = ref('')
const serverStatus = ref(false)

async function onSubmit (event: FormSubmitEvent<Schema>) {
  isLoading.value = true
  serverStatus.value = false
  
  try {
    const response: any = await $fetch('/api/register', {
      method: 'post',
      body: state
    })

    if(response.statusCode === 500) {
      serverStatus.value = true
      serverErrors.value = response.error.message
      window.scrollTo({ top: 0, behavior: 'smooth' })
      isLoading.value = false
      return
    }

    // Handle cart functionality after successful registration
    const orders = ref()
    const completeOrder = localStorage.getItem("completeOrder")
    orders.value = completeOrder ? JSON.parse(completeOrder) : null

    const { items } = await fetchHock(`orders/${Number(response.user.data.ID)}?status=pending-cart`)
    if (items?.value?.single[0]?.id) {
      await $fetch(`/api/orders/delete/${items?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any

      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    } else {
      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    }

    // Load user data
    const { items: usersData } = await fetchHock(`shipping/${Number(response.user.data.ID)}`)
    userData.value = usersData.value

    // Set cookies
    tokenCookie.value = response.user.data.user_pass
    userIdCookie.value = response.user.data.ID

    // Redirect to mobile checkout
    router.push(localePath('/cart/mobile/checkout'))
    
  } catch (err) {
    // Handle error
    serverStatus.value = true
    serverErrors.value = t('Registration failed. Please try again.')
    window.scrollTo({ top: 0, behavior: 'smooth' })
    isLoading.value = false
  }
}


const totalItems = ref(0)

totalItems.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)
</script>