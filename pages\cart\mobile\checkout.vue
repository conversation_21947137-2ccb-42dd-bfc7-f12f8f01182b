<template>
  <div>
    <div class="text-center rtl:font-notoSans py-5 border-b border-primary font-secondaryFont font-bold text-[15px] text-primary">
      {{ $t('Place Your Order') }}
    </div>
    <div class="bg-thirdColor">
      <div class="px-[30px] border-b border-primary pb-3">
        <div class="font-primaryFont text-[11px] text-primary pt-3 mb-1 rtl:font-notoSans">{{ $t('Shipping information') }}</div>
        <div class="flex justify-between gap-10">
          <div class="font-medium text-[12px] font-primaryFont text-primary rtl:font-notoSans">
            {{ (locale === 'en' ? selectedAddressData?.address_1 : selectedAddressData?.address_1_ar) || (locale === 'en' ? userData?.shipping?.address_1 : userData?.shipping?.address_1_ar) }},
            {{ (locale === 'en' ? selectedAddressData?.first_name : selectedAddressData?.first_name_ar) || (locale === 'en' ? userData?.shipping?.first_name : userData?.shipping?.first_name_ar) }},
            {{ (locale === 'en' ? selectedAddressData?.last_name : selectedAddressData?.last_name_ar) || (locale === 'en' ? userData?.shipping?.last_name : userData?.shipping?.last_name_ar) }},
            {{ (locale === 'en' ? selectedAddressData?.address_2 : selectedAddressData?.address_2_ar) || (locale === 'en' ? userData?.shipping?.address_2 : userData?.shipping?.address_2_ar) }},
            {{ (locale === 'en' ? selectedAddressData?.city : selectedAddressData?.city_ar) || (locale === 'en' ? userData?.shipping?.city : userData?.shipping?.city_ar) }}
          </div>
          <div>
            <NuxtLink :to="localePath('/cart/mobile/address')">
              <ClientOnly>
                <font-awesome-icon class="text-primary" icon="fa-solid fa-pen-to-square" />
              </ClientOnly>
            </NuxtLink>
          </div>
        </div>
      </div>
      <div class="px-[30px] border-b border-primary pb-3">
        <div class="font-primaryFont text-[11px] text-primary pt-3 mb-1 rtl:font-notoSans">{{ $t('payment option') }}</div>
        <div class="flex justify-between gap-10">
          <div class="font-medium text-[12px] font-primaryFont text-primary">
            {{ selectedPaymentMethod === 'visa' ? $t('Bank Card') : $t(selectedPaymentMethod) }}
          </div>
          <div>
            <NuxtLink :to="localePath('/cart/mobile/payment')">
              <ClientOnly>
                <font-awesome-icon class="text-primary" icon="fa-solid fa-pen-to-square" />
              </ClientOnly>
            </NuxtLink>
          </div>
        </div>
      </div>
      <div class="px-[30px] border-b border-primary pb-3">
        <div class="font-primaryFont text-[11px] text-primary pt-3 mb-1 rtl:font-notoSans">{{ $t('Order Summary') }}</div>
        <div class="flex justify-between gap-10">
          <div class="text-primary text-[12px] font-medium font-primaryFont rtl:font-notoSans">
            <h2 class="mb-1">{{ $t('Items') }}</h2>
            <h2 class="mb-1">{{ $t('Sub Total') }}</h2>
            <!-- <h2 class="mb-1">VAT 14%</h2> -->
            <h2 class="font-bold text-[12px]">{{ $t('TOTAL') }}</h2>
          </div>
          <div class="text-primary text-[12px] font-medium font-primaryFont">
            <h2 class="mb-1 text-end">{{ totalItems }}</h2>
            <h2 class="mb-1 text-end rtl:flex rtl:flex-row-reverse rtl:gap-2"><span class="rtl:font-notoSans">{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span></h2>
            <!-- <h2 class="mb-1">VAT 14%</h2> -->
            <h2 class="font-bold text-[12px] text-end rtl:flex rtl:flex-row-reverse rtl:gap-2"><span class="rtl:font-notoSans">{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span></h2>
          </div>
        </div>
      </div>
      <div class="bg-thirdColor pt-6 px-[30px] min-h-[200px]">
        <div class="font-secondaryFont font-bold text-primary text-[12px] rtl:font-notoSans">{{ $t('Do you have a coupon code?') }}</div>
        <div class="mt-2 relative pb-8">
          <input
            class="
              text-[13px]
              w-full
              font-secondaryFont
              py-2
              px-4
              border
              bg-thirdColor
              border-primary
              rounded-full
              focus:outline-none
            "
            :placeholder="$t('ENTER PROMO CODE')"
          />
          <UButton
            class="
              text-thirdColor
              font-bold
              font-thirdFont
              text-[13px]
              rounded-l-none
              rounded-r-full
              px-6
              py-2
              border
              border-primary
              bg-primary
              uppercase
              rtl:font-notoSans
              shadow-unset
              ease-in-out
              duration-500
              absolute
              right-0
              top-0
            "
            variant="solid"
          >
            {{ $t('REDEEM') }}
          </UButton>
        </div>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123] md:hidden">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans">
        {{ $t('TOTAL: EGP') }} {{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}
      </div>
      <div class="flex justify-center">
        <UButton
          class="
            rounded-full
            flex
            items-center
            justify-center
            font-bold
            text-[15px]
            w-full
            py-2
            font-thirdFont
            text-thirdColor
            uppercase
            bg-orangeColor
            border-2
            border-orangeColor
            rtl:font-notoSans
            transition-all
            duration-500
            ease-in-out"
            @click="placeOrder"
            :loading="loading"
          >
           {{ $t('place order') }}
        </UButton>
      </div>
    </div>
    <ModalsCheckoutVerify />
  </div>

</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const router = useRouter()
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => ({}))
const selectedPaymentMethod: any = useState("selectedPaymentMethod", () => '')
const loading = ref(false)
const tokenCookie = useCookie('token', persistentCookieOptions)
const { locale, t } = useI18n()
const localePath = useLocalePath()

// Get selected address data from localStorage
const selectedAddressData = ref<any>(null)

// Additional state variables from PlaceOrder.vue
const orders: any = useState("orders", () => [])
const submitedButton = useState("submitedButton", () => false)
const cartFulltemNew = ref()
const user = ref()
const oldOrderId = ref()
const realOrderId = ref()

const totalItems = ref(0)

totalItems.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)


const successPopUp = () => {
  ElNotification({
    // title: 'Create order',
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('Order has been created successfully')),
    // type: 'success',
    position: 'top-right',
  })
}

// Payment modal functions from PlaceOrder.vue
const onSuccess = async (result: any) => {
  try {
    // First, make the API request and wait for it to complete
    const { items: pendingOrder } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending`)
    
    // Ensure the data is fully loaded by waiting a bit
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // Delete Old Order
    await $fetch(`/api/orders/delete/${oldOrderId.value}`, {
      method: 'delete',
    }) as any

    const data = ref({
      order_id: null,
      transaction_id: result.orderId
    })
    
    // Now check the conditions with the properly awaited data
    if (pendingOrder?.value?.single?.length) {
      data.value.order_id = pendingOrder.value.single[0]?.id
    } 
    if (pendingOrder?.value?.grouped?.length) {
      data.value.order_id = pendingOrder.value.grouped[0]?.group
    }

    const getOrderId = await $fetch('/api/newBranch/payment/save', {
      method: 'post',
      body: data.value
    }) as any

    submitedButton.value = false
    successPopUp()

    localStorage.removeItem("completeOrder")
    localStorage.removeItem("selectedAddressData")
    localStorage.removeItem("selectedPaymentMethod")

    // Prepare the redirect URL first
    let cartUrl = ''
    if(realOrderId.value.type === 'single') {
      cartUrl = `/thanks/${realOrderId.value.created_orders[0]}`
    } else {
      cartUrl = `/thanks/${realOrderId.value.grouped_order_id}`
    }

    loading.value = false
    router.push(localePath(cartUrl))
  } catch (error) {
    console.error('Error in onSuccess function:', error)
    loading.value = false
    ElNotification({
      message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('An error occurred while processing your payment')),
      type: 'error',
      position: 'top-right',
    })
  }
}

const onError = async (error: any) => {
  if(realOrderId.value.type === 'single') {
    await $fetch(`/api/orders/delete/${realOrderId.value.created_orders[0]}`, {
      method: 'delete',
    }) as any
    loading.value = false
  } else {
    await $fetch(`/api/orders/delete/${realOrderId.value.grouped_order_id}`, {
      method: 'delete',
    }) as any
    loading.value = false
  }
}

const onCancel = async () => {
  if(realOrderId.value.type === 'single') {
    await $fetch(`/api/orders/delete/${realOrderId.value.created_orders[0]}`, {
      method: 'delete',
    }) as any
    loading.value = false
  } else {
    await $fetch(`/api/orders/delete/${realOrderId.value.grouped_order_id}`, {
      method: 'delete',
    }) as any
    loading.value = false
  }
}

const paymentModal = async (sessionId: string) => {
  // initialize the payment object
  const payment = new (window as any).GeideaCheckout(onSuccess, onError, onCancel);

  // start the payment
  payment.startPayment(sessionId);
}

// Load orders data
const loadOrdersData = async () => {
  const { items: newItem } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
  orders.value = newItem?.value?.single[0]?.items
  cartItems.value = newItem?.value
  cartFulltemNew.value = newItem?.value?.single[0]
}

onMounted(async () => {
  const completeOrder = localStorage.getItem("completeOrder")
  if (completeOrder) {
    orders.value = JSON.parse(completeOrder)
  }

  
  const storedPaymentMethod = localStorage.getItem("selectedPaymentMethod")
  selectedPaymentMethod.value = storedPaymentMethod
  
  // Load selected address data from localStorage
  const storedAddressData = localStorage.getItem("selectedAddressData")
  if (storedAddressData) {
    selectedAddressData.value = JSON.parse(storedAddressData)
  }
  
  // Load user data if token exists
  if (tokenCookie.value) {
    const { items } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
    user.value = items.value
  }
  
  // Load orders data
  await loadOrdersData()
})

// Watch for userData changes
watch(userData, async (current) => {
  if (tokenCookie.value) {
    const { items } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
    user.value = items.value
  }
})
const showVerificationModal: any = useState("showVerificationModal", () => false)

// Place Order
const placeOrder = async () => {
  loading.value = true

  const { items: pendingOrder } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending`)

  if(pendingOrder?.value?.single?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.single[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any
    }
  } else if(pendingOrder?.value?.grouped?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.grouped[0]?.orders[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.grouped[0]?.group}`, {
        method: 'delete',
      }) as any
    }
  }

  const { items: verify } = await fetchHock(`profile/verify/${Number(userData?.value?.id)}`)

  if (!verify.value.verified) {
    ElNotification({
      message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('Please verify your account first')),
      type: 'error',
      position: 'top-right',
    })
    showVerificationModal.value = true
    loading.value = false
    return
  }

  const { items } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
  const { items: allCartItems } = await fetchHock(`orders/${Number(userData?.value?.id)}`)

  // Use selected address data or fallback to default shipping address
  const shippingAddress = selectedAddressData.value || items.value.shipping

  const transformedItemsForOrder = orders.value.map((item: any) => ({
    id: item.id,
    line_item_id: item.line_item_id,
    extra_variation_data: item.extra_variation_data,
    product_id: item.product_id,
    variation_id: item.variation_id,
    product_name: item.product_name,
    quantity: item.quantity,
    subtotal: item.subtotal,
    subtotal_tax: item.subtotal_tax,
    discount: item.discount,
    total: item.total,
    total_tax: item.total_tax,
    taxes: item.taxes,
    price: item.price,
    display_price: item.display_price,
    single_price: item.single_price,
    total_incl_tax: item.total_incl_tax,
    image: item.image,
    meta_data: item.meta_data,
    brand_logo: item.brand_logo,
    excerpt: item.excerpt,
    reviews: item.reviews,
    name: item.name,
    parent_name: item.parent_name,
    sku: item.sku,
    tax_class: item.tax_class,
    variation_excerpt: item.variation_excerpt,
  }))

  const data = ref<any>({
    customer_id: items.value.id,
    status: 'confirmation',
    line_items: transformedItemsForOrder,
    shipping: shippingAddress,
    billing: shippingAddress,
    payment_method: "cod",
    payment_method_title: "Cash On Delivery",
    coupon_lines: [],
    total: cartFulltemNew?.value?.total,
    subtotal: cartFulltemNew?.value?.subtotal,
    tax_lines: cartFulltemNew?.value?.tax_lines,
    total_tax: cartFulltemNew?.value?.total_tax,
    total_discount: cartFulltemNew?.value?.total_discount,
  })

  const getCoupons = allCartItems?.value?.single[0].coupon_lines?.map((coupon: any) => ({
    code: coupon.code
  })) || []

  data.value.coupon_lines = getCoupons

  oldOrderId.value = allCartItems?.value?.single[0].id

  if (selectedPaymentMethod.value === 'visa') {
    data.value.payment_method = 'geidea_card'
    data.value.payment_method_title = 'Bank Card'
    data.value.status = 'pending'

    // Create the order
    const getOrderId = await $fetch('/api/orders/createRealOrder', {
      method: 'post',
      body: data.value
    }) as any
    
    realOrderId.value = getOrderId

    if(getOrderId.type === 'single') {
      const { items: paymentSession } = await fetchHock(`newBranch/payment?language=${locale.value}&order_id=${getOrderId.created_orders[0]}`)
      paymentModal(paymentSession.value.session_id)
    } else {
      const { items: paymentSession } = await fetchHock(`newBranch/payment?language=${locale.value}&order_id=${getOrderId.grouped_order_id}`)
      paymentModal(paymentSession.value.session_id)
    }
  } else {
    const getOrderId = await $fetch('/api/orders/createRealOrder', {
      method: 'post',
      body: data.value
    }) as any

    // Delete Old Order
    await $fetch(`/api/orders/delete/${oldOrderId.value}`, {
      method: 'delete',
    }) as any

    submitedButton.value = false
    successPopUp()

    localStorage.removeItem("completeOrder")
    localStorage.removeItem("selectedAddressData")
    localStorage.removeItem("selectedPaymentMethod")

    // Prepare the redirect URL first
    let cartUrl = ''
    if(getOrderId.type === 'single') {
      cartUrl = `/thanks-cod/${getOrderId.created_orders[0]}`
      loading.value = false
    } else {
      cartUrl = `/thanks-cod/${getOrderId.grouped_order_id}`
      loading.value = false
    }

    router.push(localePath(cartUrl))
  }
}
</script>

<style>
</style>