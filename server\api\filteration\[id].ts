// import OAuth from 'oauth-1.0a'
// import crypto from 'crypto'
import config from '../../../config.json'

export default defineEventHandler(async (event) => {
   try {
      const queryId = event.context.params.id
      
      const response = await $fetch(`${process.env.WC_API_URL}attributes/list?category=${queryId}`, {
         method: 'GET',
         headers: {
            'Authorization': `Basic ${Buffer.from(`ck_da29de315a5e4c5d1873adfd87a3d30db0fce742:cs_0088f2bd53cb7d47c8758217a122f4c24e1c4130`).toString('base64')}`,
            'Content-Type': 'application/json', // or 'text/plain', depending on your API
         },
      
      })
      const data = await response
      return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
 })