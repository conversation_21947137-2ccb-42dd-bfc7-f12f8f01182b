---
description: Base Guidelines for GitHub Copilot in Nuxt 3
globs: *,**/*
---

# ---------------------------------------------------------------
# DESCRIPTION:
# This rule helps to better control GitHub Copilot in a Nuxt 3 project.
# It ensures Copilot follows best practices and avoids generating unnecessary files or code.
#
# ❗ ATTENTION:  
# 1. This does not work as strictly in large codebases.
# 2. Remove or modify any rules you do not need.
# 3. Follow Nuxt 3 coding conventions and best practices.
# ---------------------------------------------------------------

# Instructions

1. Always check existing files before generating new components, composables, or API routes.  
2. Always verify system files (`pages/`, `components/`, `server/`) before duplicating functionality.  
3. Maintain Nuxt 3 best practices:
   - Use **auto-imported composables** (e.g., `useState`, `useFetch`).
   - Prefer **server routes (`server/api/`)** over in-component API calls.
   - Follow Nuxt’s **file-based routing** (avoid manual Vue Router setups).
   - Use **`defineProps()` and `defineEmits()`** properly in components.

# Optional

- **Respect coding style:** Follow `.eslintrc`, `.prettierrc`, or other formatting configs.  
- **Do not include explanations or filler comments** unless explicitly requested.  
- **Follow Nuxt 3’s async handling:** Use `useAsyncData` instead of `onMounted()`.  
- **Adhere to folder structure:** Components go in `components/`, shared logic in `composables/`, and server logic in `server/api/`.  
- **Generate only what is necessary:** Do not add extra features beyond the requested scope.  
- **If multiple steps are required, follow them in order** without deviation.  
- **Pay attention to stated constraints:** Performance, reactivity, and best practices should not be ignored.  
- **Only modify relevant parts of the codebase:** Do not introduce unnecessary dependencies or breaking changes.  
- **Do not generate private or sensitive data** (API keys, secrets).  
- **Ensure compatibility with project dependencies** (e.g., Tailwind, Nuxt UI, Element Plus).  

---

## ⚙️ **Customizing Copilot Behavior in VS Code**
To fine-tune Copilot’s behavior for Nuxt 3:  
1. Open **VS Code Settings (`Ctrl + ,`)**  
2. Search for **"Copilot"**  
3. Adjust the following:  
   - `"github.copilot.enable": {"vue": true, "javascript": true, "typescript": true}`  
   - `"github.copilot.editor.suggestionBehavior": "matchExistingPatterns"`  
   - `"github.copilot.editor.enableAutoCompletions": false` (if too distracting)  

---