export interface FooterLink {
  name?: string;
  name_ar?: string;
  slug?: string;
  placeholder?: string;
  placeholder_ar?: string;
  button?: string;
  button_ar?: string;
  desc?: string;
  desc_ar?: string;
}

export interface SocialMediaLink {
  platform: string;
  url: string;
  icon: string;
}

export interface FooterSection {
  section_title?: string;
  section_title_ar?: string;
  sction_title_ar?: string;
  links: FooterLink[];
}

export interface BottomFooter {
  copyrights: string;
  copyrights_ar: string;
  socia_media: SocialMediaLink[];
}

export interface Footer {
  section_left: FooterSection;
  section_middle: FooterSection;
  section_right: FooterSection;
  bottom_footer: BottomFooter;
}