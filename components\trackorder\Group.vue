<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto mb-10 hidden md:block">
      <div class="text-primary font-bold text-2xl mt-14 mb-14 font-secondaryFont uppercase rtl:font-notoSans">{{ $t('TRACK OF group ORDER') }} #{{ productId }}</div>
    </div>
    <div class="px-[20px] max-w-[1280px] mx-auto mb-10 hidden md:block">
      <div class="pb-20">
        <div>
          <div
            v-for="(items, index) in trackOrderData?.orders ?? []"
            :key="items.id"
            class="w-full border border-primary"
            :class="{ 'border-t': index === 0, 'border-t-0': index !== 0 }"
          >
            <div
              class="flex justify-between items-center px-14 py-6 bg-thirdColor border-b border-primary"
            >
              <div class="font-bold text-2xl text-primary font-secondaryFont uppercase rtl:font-notoSans">{{ $t('order') }} #{{ items.id }}</div>
              <div class="bg-orangeColor py-3 px-14 text-thirdColor uppercase font-bold rounded-full rtl:font-notoSans">{{ items.status }}</div>
            </div>
            <div class="px-20 py-4">
              <div
                v-for="(item, i) in items?.items ?? []"
                :key="i"
                class="border-none-custom border-b border-primary"
              >
                <div>
                  <div class="mt-[20px] flex items-center justify-between h-fit w-full pb-5">
                    <div class="flex items-center gap-10 md:w-[65%] lg:w-[55%]">
                      <div class="relative w-[125px] h-[110px] flex items-center cursor-pointer" @click="navigateToProduct(item)">
                        <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary" :src="item?.brand_logo"/>
                        <img class="absolute top-0 ltr:left-[35px] rtl:right-[35px]" :src="item?.image"/>
                      </div>
                      <div class="cursor-pointer" @click="navigateToProduct(item)">
                        <div class="font-primaryFont text-xl italic font-semibold text-primary rtl:font-notoSans">{{ locale === 'en' ? item?.product_name : item?.product_name_ar }}</div>
                        <div class="font-primaryFont text-lg italic font-semibold text-orangeColor">
                          {{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}
                        </div>
                        <template v-if="Array.isArray(item?.meta_data)">
                          <div class="flex gap-4 mt-2">
                            <div
                              class="flex flex-col ltr:italic font-medium font-primaryFont text-primary border-primary"
                              v-for="(varData, index) in item?.meta_data ?? []"
                              :key="index"
                            >
                              <span class="text-[14px] rtl:font-notoSans">{{ locale === 'en' ? varData?.name : varData?.name_ar }}: </span>
                              <span class="text-[15px] font-bold mt-[-4px]">{{ varData?.value }} </span>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                    <div class="font-primaryFont font-extrabold text-primary text-2xl">
                      <span class="text-base">X</span>{{ item.quantity }}
                    </div>
                    <div class="text-primary text-center font-bold text-2xl font-primaryFont">
                      <div class="rtl:font-notoSans">{{ $t('EGP') }}</div>
                      <div>{{ formatPrice(Number(item.display_price)) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <div class="text-primary bg-thirdColor border-b border-e border-s border-primary flex">
            <div class="w-[50%]" v-if="userData?.id">
              <div class="py-8 px-6 border-b border-primary">
                <div class="font-secondaryFont font-bold text-lg rtl:font-notoSans">{{ $t('SHIPPING INFORMATION') }}</div>
                <div class="font-primaryFont font-medium mt-3" v-if="trackOrderData.orders?.[0]?.shipping?.last_name">{{ trackOrderData.orders?.[0]?.shipping?.first_name }} {{ trackOrderData.orders?.[0]?.shipping?.last_name }}, {{ trackOrderData.orders?.[0]?.shipping?.address_2 }}, {{ trackOrderData.orders?.[0]?.shipping?.city }}, {{ trackOrderData.orders?.[0]?.billing?.phone }}</div>
                <div class="font-primaryFont font-medium mt-3" v-else>{{ trackOrderData.orders?.[0]?.shipping?.first_name }}</div>
              </div>
              <div class="flex">
                <div class="border-e border-primary py-8 px-6">
                  <div class="font-secondaryFont font-bold text-lg mb-3 rtl:font-notoSans">
                    {{ $t('PAYMENT METHOD') }}
                  </div>
                  <div class="font-primaryFont font-medium rtl:font-notoSans">
                    {{ locale === 'en' ? trackOrderData.orders?.[0]?.payment_method : trackOrderData.orders?.[0]?.payment_method_ar }}
                  </div>
                </div>
                <div class="py-8 px-6">
                  <div class="font-secondaryFont font-bold text-lg mb-3 rtl:font-notoSans">
                    {{ $t('COURIER TRACK LINK') }}
                  </div>
                  <div class="font-primaryFont font-medium rtl:font-notoSans">
                    {{ locale === 'en' ? trackOrderData.orders?.[0]?.courier_track_link : trackOrderData.orders?.[0]?.courier_track_link_ar }}
                  </div>
                </div>
              </div>
            </div>
            <div class="w-[50%] border-s border-primary pt-8 pb-7 px-12 text-primary flex flex-col justify-between">
              <div>
                <div v-if="trackOrderData?.coupon_lines?.length" class="flex justify-between w-full items-center gap-14 rtl:font-notoSans">
                  <div class="font-secondaryFont font-bold text-lg">{{ $t('PROMO CODE:') }}</div>
                  <div
                    class="font-primaryFont font-bold text-2xl"
                    v-for="(i, index) in trackOrderData.coupon_lines || []"
                    :key="index"
                    >
                      {{ i.code }}
                  </div>
                </div>
                <div class="flex justify-between w-full items-center gap-14">
                  <div class="font-secondaryFont font-bold text-lg rtl:font-notoSans">{{ $t('SUB TOTAL:') }}</div>
                  <div class="font-primaryFont font-bold text-2xl flex gap-1 rtl:flex-row-reverse">
                    <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                    <span>{{ formatPrice(Number(trackOrderData?.subtotal)) }}</span>
                  </div>
                </div>
                <div class="flex justify-between w-full items-center gap-14 mt-1" v-if="trackOrderData?.total_discount">
                  <div class="font-secondaryFont font-bold text-lg">{{ $t('DISCOUNT:') }}</div>
                  <div class="font-primaryFont font-bold text-2xl flex gap-1 rtl:flex-row-reverse">
                    <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                    <span>{{ formatPrice(Number(trackOrderData?.total_discount)) }}</span>
                  </div>
                </div>
                <div v-for="(item, index) in trackOrderData.tax_lines || []" :key="index" class="flex justify-between w-full items-center gap-14 mt-1">
                  <div class="font-secondaryFont font-bold text-lg rtl:font-notoSans">{{ locale === 'en' ? item?.label : item?.label_ar }} <span>({{ item?.tax_rate }}):</span></div>
                  <div class="font-primaryFont font-bold text-2xl flex gap-1 rtl:flex-row-reverse">
                    <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                    <span>{{ formatPrice(Number(trackOrderData?.total_tax)) }}</span>
                  </div>
                </div>
              </div>
              <div class="flex justify-between w-full items-center gap-14 mt-8">
                <div class="font-secondaryFont font-bold text-3xl rtl:font-notoSans">{{ $t('TOTAL') }}</div>
                <div class="font-primaryFont font-bold text-[35px] flex gap-1 rtl:flex-row-reverse">
                  <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                  <span>{{ formatPrice(Number(trackOrderData?.total)) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="md:hidden">
      <div v-for="(items, index) in trackOrderData?.orders ?? []" :key="items.id" class="w-full border border-primary" :class="{ 'border-t': index === 0, 'border-t-0': index !== 0 }">
        <div class="flex justify-between items-center px-4 py-3 bg-thirdColor border-b border-primary">
          <div class="font-bold text-base text-primary font-secondaryFont uppercase rtl:font-notoSans">{{ $t('order') }} #{{ items.id }}</div>
          <div class="bg-orangeColor py-2 px-4 text-thirdColor uppercase font-bold text-[12px] md:text-sm rounded-full rtl:font-notoSans">{{ locale === 'en' ? items?.status : items?.status_ar }}</div>
        </div>
        <div class="px-[30px]">
          <div
            v-for="(item, i) in items?.items ?? []"
            :key="i"
            class="border-b border-primary border-none-custom"
          >
            <div class="mt-[20px] flex items-center justify-between h-fit w-full">
              <div class="w-full">
                <div class="flex gap-2 items-center cursor-pointer" @click="navigateToProduct(item)">
                  <div class="relative">
                    <img class="mx-auto w-[200px] md:w-full" :src="item?.image" :alt="item?.product_name" />
                    <img class="absolute bottom-0 ltr:left-[25%] rtl:right-[25%] w-[60px] h-[60px] rounded-full border border-primary" :src="item?.brand_logo" :alt="locale === 'en' ? item?.product_name : item?.product_name_ar" />
                  </div>
                  <div>
                    <h2 class="text-primary italic font-semibold text-[15px] font-primaryFont rtl:font-notoSans">{{ locale === 'en' ? item?.product_name : item?.product_name_ar }}</h2>
                    <div class="text-orangeColor italic font-semibold text-sm font-primaryFont mt-[-5px]">{{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}</div>
                    <template v-if="Array.isArray(item?.meta_data)">
                      <div class="flex flex-col mt-2">
                        <div
                          class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary"
                          v-for="(varData, index) in item?.meta_data ?? []"
                          :key="index"
                        >
                          <span class="font-semibold rtl:font-notoSans">{{ locale === 'en' ? varData?.name : varData?.name_ar }}: </span>
                          <span class="font-bold">{{ varData?.value }} </span>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>

                <div class="p-4 bg-thirdColor mt-4 flex justify-between">
                  <div>
                    <div class="font-primaryFont text-[13px] text-primary">{{ $t('Price x Qty.') }}</div>
                    <div class="font-primaryFont text-[13px] text-orangeColor">{{ $t('Sub Total') }}</div>
                  </div>
                  <div>
                    <div class="font-primaryFont text-[13px] text-primary font-semibold flex gap-2">
                      <div class="flex gap-1 rtl:flex-row-reverse">
                        <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                        <span>{{ formatPrice(Number(item?.price)) }}</span>
                      </div>
                      <div class="rtl:flex rtl:flex-row rtl:gap-2"><span>x</span> {{ item?.quantity }}</div>
                    </div>
                    <div class="font-primaryFont text-[13px] text-orangeColor font-semibold flex gap-1 rtl:flex-row-reverse justify-start">
                      <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                      <span>{{ formatPrice(Number(item.display_price)) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="text-primary bg-thirdColor border-t border-primary border-b">
        <div class="border-b border-primary py-8 px-[30px] text-primary">
          <div class="flex justify-between w-full items-center gap-14 mt-2">
            <div class="font-secondaryFont font-bold text-[13px]">{{ $t('SUB TOTAL:') }}</div>
            <div class="font-primaryFont font-semibold text-[13px] flex gap-1 rtl:flex-row-reverse">
              <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
              <span>{{ formatPrice(Number(trackOrderData?.subtotal)) }}</span>
            </div>
          </div>
          <div class="flex justify-between w-full items-center gap-14 mt-1" v-if="trackOrderData?.total_discount">
            <div class="font-secondaryFont font-bold text-[13px]">{{ $t('DISCOUNT:') }}</div>
            <div class="font-primaryFont font-semibold text-[13px] flex gap-1 rtl:flex-row-reverse">
              <span class="rtl:font-notoSans">-{{ $t('EGP') }}</span>
              <span>{{ formatPrice(Number(trackOrderData?.total_discount)) }}</span>
            </div>
          </div>
          <div class="flex justify-between w-full items-center gap-14 mt-1">
            <div class="font-secondaryFont font-bold text-[13px]">{{ $t('VAT:') }}</div>
            <div class="font-primaryFont font-semibold text-[13px] flex gap-1 rtl:flex-row-reverse">
              <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
              <span>{{ formatPrice(Number(trackOrderData?.total_tax)) }}</span>
            </div>
          </div>
          <div class="flex justify-between w-full items-center gap-14 mt-4">
            <div class="font-secondaryFont font-bold text-[18px]">{{ $t('TOTAL') }}</div>
            <div class="font-primaryFont font-semibold text-[18px] flex gap-1 rtl:flex-row-reverse">
              <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
              <span>{{ formatPrice(Number(trackOrderData?.total)) }}</span>
            </div>
          </div>
        </div>
        <div class="" v-if="userData?.id">
          <div class="py-8 px-[30px]">
            <div class="font-secondaryFont font-bold text-lg rtl:font-notoSans">{{ $t('SHIPPING INFORMATION') }}</div>
            <div class="font-primaryFont font-medium mt-3" v-if="trackOrderData.orders?.[0]?.shipping?.last_name">{{ trackOrderData.orders?.[0]?.shipping?.first_name }} {{ trackOrderData.orders?.[0]?.shipping?.last_name }}, {{ trackOrderData.orders?.[0]?.shipping?.address_2 }}, {{ trackOrderData.orders?.[0]?.shipping?.city }}, {{ trackOrderData.orders?.[0]?.billing?.phone }}</div>
            <div class="font-primaryFont font-medium mt-3" v-else>{{ trackOrderData.orders?.[0]?.shipping?.first_name }}</div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>
<script setup lang="ts">
import { formatPrice } from '~/composables/formatPrice'
import type { CartItem } from '~/types/defaultCart'
import type { UserData } from '~/types/user'

const props = defineProps<{
  orderData?: any;
}>();

const { locale } = useI18n()
const localePath = useLocalePath()
const route = useRoute()
const router = useRouter()
const trackOrderData = ref()
const userData = useState<UserData>("userData", () => ({} as UserData))

const productId = ref(route.params.id)
trackOrderData.value = props.orderData

// Function to navigate to product detail page
const navigateToProduct = (item: CartItem) => {
  // Determine product type (simple or variable)
  const productType = item.variation_id ? 'variable' : 'simple'
  // Navigate to product page
  router.push(localePath(`/product/${productType}/${item.variation_id ?? item.product_id}`))
}
</script>