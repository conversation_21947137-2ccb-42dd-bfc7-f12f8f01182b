<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="hidden md:block">
        <div class="relative sellerSwiper">
          <!-- Desktop product grid -->
          <div
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start"
            v-if="!loadingCategory"
          >
            <div
              class="mx-auto flex items-end w-full"
              v-for="(item, index) in products || []"
              :key="typeof item.id === 'number' ? item.id : index"
            >
              <div class="w-full">
                <div class="w-full">
                  <!-- Product image with brand logo -->
                  <div class="relative mx-auto w-[50%]">
                    <img
                      class="mx-auto md:w-[136px] md:h-[136px]"
                      :src="item?.image || '/images/placeholder.jpg'"
                      :alt="item?.name || 'Product image'"
                      loading="lazy"
                    />
                    <img
                      class="absolute bottom-0 ltr:left-[-35%] rtl:right-[-35%] w-[80px] h-[80px] rounded-full border border-primary"
                      :src="item?.brand_logo || '/images/placeholder-brand.jpg'"
                      :alt="item?.brand_name || 'Brand logo'"
                      loading="lazy"
                    />
                    <!-- Sale badge -->
                    <div
                      v-if="item?.sale && typeof item.sale === 'object'"
                      class="absolute z-2 ltr:right-[-61px] rtl:left-[-61px] bottom-0 uppercase w-full"
                    >
                      <div class="py-1 px-3 bg-thirdColor rounded-t-[15px] text-[11px] font-semibold text-primary">
                        {{ locale === 'en' ? item?.sale?.title : item?.sale?.title_ar || 'Sale' }}
                      </div>
                      <div class="bg-orangeColor rounded-b-[15px] px-3 py-1 text-thirdColor flex flex-col">
                        <span class="font-semibold text-[15px]">{{ locale === 'en' ? item?.sale?.percent : item?.sale?.percent_ar || '0%' }}</span>
                        <span class="text-[12px] font-medium line-through">{{ locale === 'en' ? item?.sale?.line_through : item?.sale?.line_through_ar || '' }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- Product name and details -->
                  <h2 class="text-primary ltr:italic font-semibold text-xl mt-4 mb-0 pb-0 truncate">
                    {{ locale === 'en' ? item?.name : item?.name_ar }}
                  </h2>
                  <div class="text-orangeColor ltr:italic font-semibold text-lg mt-[-5px] truncate">
                    {{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}
                  </div>

                  <!-- Product specifications -->
                  <div class="flex flex-col mt-3">
                    <div class="flex gap-2 ltr:italic text-sm text-primary" v-if="item?.load_index?.value">
                      <div class="font-semibold">{{ locale === 'en' ? item?.load_index?.name : item?.load_index?.name_ar || 'Load Index' }}:</div>
                      <span class="font-bold">{{ item?.load_index?.value }}</span>
                    </div>
                    <div class="flex gap-2 ltr:italic text-sm text-primary" v-if="item?.speed_index?.value">
                      <div class="font-semibold">{{ locale === 'en' ? item?.speed_index?.name : item?.speed_index?.name_ar || 'Speed Index' }}:</div>
                      <span class="font-bold">{{ item?.speed_index?.value }}</span>
                    </div>
                    <div class="flex gap-2 ltr:italic text-sm text-primary" v-if="item?.origin?.value">
                      <div class="font-semibold">{{ locale === 'en' ? item?.origin?.name : item?.origin?.name_ar || 'Origin' }}:</div>
                      <span class="font-bold">{{ item?.origin?.value }}</span>
                    </div>
                  </div>

                  <!-- Product description -->
                  <div
                    class="ltr:italic text-sm text-primary font-thirdFont mt-4 lines-2"
                    v-html="locale === 'en' ? item?.description : item?.description_ar"
                  ></div>

                  <!-- Product buttons -->
                  <div class="pb-5 mt-4 flex flex-col gap-3">
                    <!-- Add to Cart button -->
                    <UButton
                      v-if="item?.stock"
                      class="
                        text-primary
                        flex
                        flex-col
                        w-full
                        items-center
                        justify-between
                        font-bold
                        text-[15px]
                        py-2
                        bg-thirdColor
                        border-2
                        border-thirdColor
                        uppercase
                        transition-all
                        button-shadow
                        duration-500
                        ease-in-out
                        hover:text-thirdColor
                        hover:bg-primary
                        hover:border-2
                        hover:border-primary
                        hover:border-secondary
                        hover:bg-secondary
                        min-h-[43px]
                        disabled:opacity-70
                        disabled:cursor-not-allowed
                      "
                      :loading="cartLoading[typeof item.id === 'number' ? item.id : 0]"
                      :disabled="!item?.id || !userData?.id"
                      @click="addToCarts(item)"
                      aria-label="Add to cart"
                    >
                      <div v-if="!cartLoading[typeof item.id === 'number' ? item.id : 0]" class="flex items-center gap-3">
                        <ClientOnly>
                          <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                        </ClientOnly>
                        <div class="flex gap-1 rtl:flex-row-reverse">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(item?.price || 0)) }}</span>
                        </div>
                      </div>
                    </UButton>
                    <UButton
                      v-else
                      class="
                        text-primary
                        flex
                        flex-col
                        w-full
                        items-center
                        justify-between
                        font-bold
                        uppercase
                        py-2
                        transition-all
                        button-shadow
                        hover:text-primary
                        hover:border-thirdColor
                        hover:bg-thirdColor
                        disabled:border-thirdColor
                        disabled:bg-thirdColor
                        min-h-[43px]
                      "
                      disabled
                    >
                      <div class="flex items-center gap-3">
                        <div class="text-[12px] mt-[3px]">{{ $t('Out of stock') }}</div>
                        <div class="flex gap-1 rtl:flex-row-reverse text-[18px]">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(item.price)) }}</span>
                        </div>
                      </div>
                    </UButton>
                    <!-- View Product button -->
                    <UButton
                      class="
                      flex
                      flex-col
                      w-full
                      uppercase
                      items-center
                      justify-between
                      font-bold
                      text-[15px]
                      py-2
                      text-thirdColor
                      border-2
                      border-primary
                      button-shadow
                      transition-all
                      duration-500
                      min-h-[43px]
                      ease-in-out
                      hover:text-primary
                      hover:bg-secondary
                      hover:border-secondary
                      hover:text-thirdColor
                      disabled:opacity-70
                      disabled:cursor-not-allowed"
                      :to="localePath(item?.id ? `/product/${item.type || 'product'}/${item.id}` : '#')"
                      :disabled="!item?.id"
                      aria-label="View product details"
                    >
                      {{ $t('VIEW PRODUCT') }}
                    </UButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Loading skeleton -->
          <div v-if="loadingCategory">
            <el-skeleton animated>
              <template #template>
                <div
                  class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start p-4"
                >
                  <div
                    class="mx-auto flex items-end w-full"
                    v-for="(item, index) in [1, 2, 3, 4]" :key="index"
                  >
                    <div class="w-full">
                      <div class="w-full">
                          <div class="!relative mx-auto w-[60%]">
                            <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[150px]"/>
                            <el-skeleton-item variant="image" class="!absolute !bottom-0 !left-[-35%] !w-[80px] !h-[80px] !rounded-full !border !border-primary" />
                          </div>
                          <div class="mt-4">
                            <el-skeleton-item variant="text" style="width: 80%" />
                          </div>
                          <div>
                            <el-skeleton-item variant="text" style="width: 60%" />
                          </div>
                          <div class="!mt-4">
                            <el-skeleton-item variant="text" style="width: 40%" />
                          </div>
                          <div>
                            <el-skeleton-item variant="text" style="width: 50%" />
                          </div>
                          <div>
                            <el-skeleton-item variant="text" style="width: 40%" />
                          </div>
                          <div class="mt-6">
                            <el-skeleton-item variant="text" style="width: 100%; height: 100px;" />
                          </div>
                      </div>
                    </div>
                  </div>

                </div>
              </template>
            </el-skeleton>
          </div>
          <!-- Empty state message -->
          <div v-if="!products?.length && !loadingCategory">
            <div
              class="flex items-center justify-center font-secondaryFont uppercase text-secondary text-[22px] font-bold h-[400px]"
              role="alert"
              aria-live="polite"
            >
              {{ $t('No products in this category') }}
            </div>
          </div>
        </div>
        <!-- Pagination info -->
        <div class="font-thirdFont text-primary text-xl text-center mt-5" v-if="products?.length">
          {{ $t('There are more than') }} {{ Number(perPage) * Number(paginationPages) }} {{ $t('products to see.') }}
        </div>

        <!-- Pagination controls -->
        <div class="flex justify-center mt-6" v-if="paginationPages > 1">
          <nav aria-label="Pagination" class="flex gap-3">
            <!-- Previous page button -->
            <button
              @click="previousPage"
              :disabled="currentNumber === 1"
              class="
                text-primary
                font-secondaryFont
                font-bold
                w-[40px]
                h-[40px]
                border-2
                border-primary
                rounded-full
                flex
                items-center
                justify-center
                disabled:opacity-50
                disabled:cursor-not-allowed"
              aria-label="Go to previous page"
            >
              <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
              </svg>
              <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
              </svg>
            </button>

            <!-- Page number buttons -->
            <button
              v-for="pageNumber in totalPagesArray"
              :key="pageNumber"
              @click="selectPage(pageNumber)"
              class="
                text-primary
                font-secondaryFont
                font-bold
                w-[40px]
                h-[40px]
                uppercase
                border-2
                border-primary
                rounded-full"
              :class="{ 'bg-orangeColor !border-orangeColor text-thirdColor': currentNumber === pageNumber }"
              :aria-label="`Go to page ${pageNumber}`"
              :aria-current="currentNumber === pageNumber ? 'page' : undefined"
            >
              {{ pageNumber }}
            </button>

            <!-- Next page button -->
            <button
              @click="nextPage"
              :disabled="currentNumber === paginationPages"
              class="
                text-primary
                font-secondaryFont
                font-bold
                w-[40px]
                h-[40px]
                border-2
                uppercase
                border-primary
                rounded-full
                flex
                items-center
                justify-center
                disabled:opacity-50
                disabled:cursor-not-allowed"
              aria-label="Go to next page"
            >
              <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
              </svg>
              <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>




    <!-- Mobile product list -->
    <div class="md:hidden">
      <div class="relative md:hidden" ref="scrollContainer">
        <div
          v-if="!loadingCategory"
          class="mx-auto flex items-end border-none-custom border-b border-primary pb-6 mt-6"
          v-for="(item, index) in products || []"
          :key="typeof item.id === 'number' ? item.id : index"
        >
          <div class="px-[20px] max-w-[1280px] mx-auto w-full">
            <div class="w-full">
              <div class="w-full">
                <!-- Product link with image and details -->
                <ULink :to="localePath(item?.id ? `/product/${item.type || 'product'}/${item.id}` : '#')">
                  <div class="flex gap-2 items-center">
                    <!-- Product image with brand logo -->
                    <div class="relative">
                      <img
                        class="mx-auto w-[200px] md:w-full"
                        :src="item?.image || '/images/placeholder.jpg'"
                        :alt="locale === 'en' ? item?.name : item?.name_ar"
                        loading="lazy"
                      />
                      <img
                        class="absolute bottom-0 ltr:left-[25%] rtl:right-[25%] w-[60px] h-[60px] rounded-full border border-primary"
                        :src="item?.brand_logo || '/images/placeholder-brand.jpg'"
                        :alt="locale === 'en' ? item?.brand_name : item?.brand_name_ar"
                        loading="lazy"
                      />
                    </div>

                    <!-- Product details -->
                    <div>
                      <h2 class="text-primary italic font-semibold text-[15px] lines-1">
                        {{ locale === 'en' ? item?.name : item?.name_ar }}
                      </h2>
                      <div class="text-orangeColor italic font-semibold text-sm mt-[-5px] lines-1">
                        {{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}
                      </div>

                      <!-- Product specifications -->
                      <div class="flex flex-col mt-2">
                        <div class="flex mt-1 gap-2 italic text-xs text-primary" v-if="item?.load_index?.value">
                          <div class="font-semibold">{{ locale === 'en' ? item?.load_index?.name : item?.load_index?.name_ar }}:</div>
                          <span class="font-bold">{{ item?.load_index?.value }}</span>
                        </div>
                        <div class="flex mt-1 gap-2 italic text-xs text-primary" v-if="item?.speed_index?.value">
                          <div class="font-semibold">{{ locale === 'en' ? item?.speed_index?.name : item?.speed_index?.name_ar }}:</div>
                          <span class="font-bold">{{ item?.speed_index?.value }}</span>
                        </div>
                        <div class="flex mt-1 gap-2 italic text-xs text-primary" v-if="item?.origin?.value">
                          <div class="font-semibold">{{ locale === 'en' ? item?.origin?.name : item?.origin?.name_ar }}:</div>
                          <span class="font-bold">{{ item?.origin?.value }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </ULink>

                <!-- Add to Cart button -->
                <div class="mt-4 flex flex-col gap-3">
                  <!-- Add to Cart button -->
                  <UButton
                    v-if="item?.stock"
                    class="
                      text-thirdColor
                      flex
                      flex-col
                      w-full
                      items-center
                      justify-between
                      font-bold
                      text-[15px]
                      uppercase
                      py-2
                      bg-orangeColor
                      border-2
                      border-orangeColor
                      transition-all
                      button-shadow
                      duration-500
                      ease-in-out
                      hover:text-thirdColor
                      hover:bg-primary
                      hover:border-2
                      hover:border-primary
                      hover:border-primary
                      hover:bg-primary
                      min-h-[43px]
                      disabled:opacity-70
                      disabled:cursor-not-allowed
                    "
                    :loading="cartLoading[typeof item.id === 'number' ? item.id : 0]"
                    :disabled="!item?.id || !userData?.id"
                    @click="addToCarts(item)"
                    aria-label="Add to cart"
                  >
                    <div v-if="!cartLoading[typeof item.id === 'number' ? item.id : 0]" class="flex items-center justify-center gap-3 w-full">
                      <ClientOnly>
                        <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                      </ClientOnly>
                      <div class="flex gap-1 rtl:flex-row-reverse">
                        <span>{{ $t('EGP') }}</span>
                        <span>{{ formatPrice(Number(item?.price)) }}</span>
                      </div>
                    </div>
                  </UButton>
                  <UButton
                    v-else
                    class="
                      text-primary
                      flex
                      flex-col
                      w-full
                      items-center
                      justify-between
                      font-bold
                      uppercase
                      py-2
                      transition-all
                      button-shadow
                      hover:text-primary
                      hover:border-thirdColor
                      hover:bg-thirdColor
                      disabled:border-thirdColor
                      disabled:bg-thirdColor
                      min-h-[43px]
                    "
                    disabled
                  >
                    <div class="flex items-center gap-3">
                      <div class="text-[12px] mt-[3px]">{{ $t('Out of stock') }}</div>
                      <div class="flex gap-1 rtl:flex-row-reverse text-[18px]">
                        <span>{{ $t('EGP') }}</span>
                        <span>{{ formatPrice(Number(item?.price)) }}</span>
                      </div>
                    </div>
                  </UButton>
                  <!-- View Product button -->
                  <UButton
                    class="
                      flex
                      flex-col
                      w-full
                      uppercase
                      items-center
                      justify-between
                      font-bold
                      text-[15px]
                      py-2
                      text-thirdColor
                      border-2
                      border-primary
                      button-shadow
                      transition-all
                      duration-500
                      min-h-[43px]
                      ease-in-out
                      hover:text-primary
                      hover:bg-secondary
                      hover:border-secondary
                      hover:text-thirdColor
                      disabled:opacity-70
                      disabled:cursor-not-allowed"
                    :to="localePath(item?.id ? `/product/${item.type || 'product'}/${item.id}` : '#')"
                    :disabled="!item?.id"
                    aria-label="View product details"
                  >
                    {{ $t('VIEW PRODUCT') }}
                  </UButton>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Mobile skeleton loading -->
        <div v-if="loadingCategory" class="px-[30px]">
          <el-skeleton animated>
            <template #template>
              <div
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start p-4"
              >
                <div
                  class="mx-auto flex items-end w-full"
                  v-for="(item, index) in [1, 2, 3]" :key="index"
                >
                  <div class="w-full">
                    <div class="w-full">
                        <div class="!relative mx-auto w-[60%]">
                          <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[150px]"/>
                          <el-skeleton-item variant="image" class="!absolute !bottom-0 !left-[-35%] !w-[80px] !h-[80px] !rounded-full !border !border-primary" />
                        </div>
                        <div class="mt-4">
                          <el-skeleton-item variant="text" style="width: 80%" />
                        </div>
                        <div>
                          <el-skeleton-item variant="text" style="width: 60%" />
                        </div>
                        <div class="!mt-4">
                          <el-skeleton-item variant="text" style="width: 40%" />
                        </div>
                        <div>
                          <el-skeleton-item variant="text" style="width: 50%" />
                        </div>
                        <div>
                          <el-skeleton-item variant="text" style="width: 40%" />
                        </div>
                        <div class="mt-6">
                          <el-skeleton-item variant="text" style="width: 100%; height: 100px;" />
                        </div>
                    </div>
                  </div>
                </div>

              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- Load more skeleton -->
      <div v-if="loadingLoadMoreButton" class="px-[30px]">
        <el-skeleton animated>
          <template #template>
            <div
              class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start p-4"
            >
              <div
                class="mx-auto flex items-end w-full"
                v-for="(item, index) in [1]" :key="index"
              >
                <div class="w-full">
                  <div class="w-full">
                      <div class="!relative mx-auto w-[60%]">
                        <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[150px]"/>
                        <el-skeleton-item variant="image" class="!absolute !bottom-0 !left-[-35%] !w-[80px] !h-[80px] !rounded-full !border !border-primary" />
                      </div>
                      <div class="mt-4">
                        <el-skeleton-item variant="text" style="width: 80%" />
                      </div>
                      <div>
                        <el-skeleton-item variant="text" style="width: 60%" />
                      </div>
                      <div class="!mt-4">
                        <el-skeleton-item variant="text" style="width: 40%" />
                      </div>
                      <div>
                        <el-skeleton-item variant="text" style="width: 50%" />
                      </div>
                      <div>
                        <el-skeleton-item variant="text" style="width: 40%" />
                      </div>
                      <div class="mt-6">
                        <el-skeleton-item variant="text" style="width: 100%; height: 100px;" />
                      </div>
                  </div>
                </div>
              </div>

            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UserData } from '~/types/user'
import type { CartOrder, CartItem } from '~/types/defaultCart'
import type { Product } from '~/types/pagesContent'

/**
 * Define component emits with proper types
 */
const emit = defineEmits<{
  'updatePage': [page: number];
  'loadMore': [];
}>()

/**
 * State variables for loading and pagination
 */
const { locale } = useI18n()
const localePath = useLocalePath()

const loadingLoadMoreButton = useState<boolean>("loadingLoadMoreButton", () => false)
const isLoadMore = useState<boolean>('isLoadMore', () => false)
const loadingCategory = useState<boolean>("loadingCategory", () => false)
const cartLoading = ref<{ [key: number]: boolean }>({}) // Track loading state per product

/**
 * User and cart data
 */
const userData = useState<UserData>("userData", () => ({} as UserData))
const products = useState<Product[]>("products", () => [])
const cartItems = useState<CartItem[]>("cartItems", () => [])

/**
 * Pagination state
 */
const paginationPages = useState<number>("paginationPages", () => 1)
const perPage = useState<number>("perPage", () => 10)
const currentNumber = useState<number>("currentNumber", () => 1)

/**
 * Navigate to a specific page
 * @param pageNumber The page number to navigate to
 */
const selectPage = (pageNumber: number): void => {
  if (currentNumber.value !== pageNumber) {
    currentNumber.value = pageNumber
    emit('updatePage', pageNumber)
  }
}

/**
 * Navigate to the previous page
 */
const previousPage = (): void => {
  if (currentNumber.value > 1) {
    currentNumber.value--
    emit('updatePage', currentNumber.value)
  }
}

/**
 * Navigate to the next page
 */
const nextPage = (): void => {
  if (currentNumber.value < paginationPages.value) {
    currentNumber.value++
    emit('updatePage', currentNumber.value)
  }
}

// Number of pages to display in pagination control
const pagesPerSet = 5;

/**
 * Calculate the set of page numbers to display using a sliding window algorithm
 * This ensures the current page is always visible in the pagination control
 * @returns Array of page numbers to display
 */
const getCurrentPageSet = (): number[] => {
  // Calculate the start page (try to center the current page in the window)
  const start = Math.max(1, currentNumber.value - Math.floor(pagesPerSet / 2));

  // Calculate the end page (limited by total pages)
  const end = Math.min(start + pagesPerSet - 1, paginationPages.value);

  // Adjust the start if we're near the end (to always show pagesPerSet pages if possible)
  const adjustedStart = Math.max(1, end - pagesPerSet + 1);

  // Create an array of page numbers
  return Array.from({ length: end - adjustedStart + 1 }, (_, i) => adjustedStart + i);
}

/**
 * Computed property that returns the current set of pages to display
 */
const totalPagesArray = computed<number[]>(() => {
  return getCurrentPageSet();
})

/**
 * Add a product to the cart
 * @param product The product to add to the cart
 */
const addToCarts = async (product: Product): Promise<void> => {
  try {
    // Only proceed if product has a valid ID
    if (product.id) {
      // Set loading state only for the clicked product
      cartLoading.value[Number(product.id)] = true

      // Default quantity is 1
      const counter = ref(1)

      // Call the addToCart composable
      const { cart } = await addToCart(
        product,
        Number(userData?.value?.id),
        counter,
        true
      )

      // Update cart state
      cartItems.value = cart.value || []
    }
  } catch (error) {
    console.error('Error adding product to cart:', error)
  } finally {
    // Always reset loading state
    if (product.id) {
      cartLoading.value[Number(product.id)] = false
    }
  }
}

/**
 * Format price with commas for thousands
 * @param price The price to format
 * @returns Formatted price string
 */
const formatPrice = (price: number): string => {
  return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

/**
 * Check if the device is desktop (width >= 768px)
 */
const isDesktop = computed<boolean>(() => {
  if (typeof window !== 'undefined') {
    return window.innerWidth >= 768
  }
  return false
})

/**
 * Load more products (for infinite scroll)
 */
const loadMorePosts = (): void => {
  if (!loadingLoadMoreButton.value && !isLoadMore.value) {
    emit('loadMore')
  }
}

/**
 * Debounce function to limit how often a function can be called
 * @param fn The function to debounce
 * @param delay The delay in milliseconds
 * @returns A debounced function
 */
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number): ((...args: Parameters<T>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return function(this: any, ...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      fn.apply(this, args);
      timeoutId = null;
    }, delay);
  };
};

/**
 * Handle scroll event to implement infinite scrolling on mobile
 */
const handleScroll = debounce((): void => {
  // Get the scrollable container
  const container = document.documentElement || document.body

  // Check if we've reached the bottom (with a 100px threshold)
  const bottomReached = container.scrollTop + window.innerHeight >= container.scrollHeight - 100

  // Load more posts if we've reached the bottom and not already loading
  if (bottomReached && !loadingLoadMoreButton.value && !isLoadMore.value) {
    loadMorePosts()
  }
}, 200); // 200ms debounce

/**
 * Lifecycle hooks
 */
onMounted(() => {
  // Only add scroll listener on mobile devices
  if (typeof window !== 'undefined' && !isDesktop.value) {
    window.addEventListener('scroll', handleScroll)
  }
})

onBeforeUnmount(() => {
  // Clean up scroll listener
  if (typeof window !== 'undefined') {
    window.removeEventListener('scroll', handleScroll)
  }
})
</script>

