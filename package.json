{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "getLang": "node ./scripts/getLang.cjs"}, "dependencies": {"@duskmoon/vue3-typed-js": "^0.0.4", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@nuxt/ui": "^2.15.2", "@nuxtjs/i18n": "9.5.4", "@vue-email/nuxt": "^0.8.19", "buffer": "^6.0.3", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "nodemailer": "^6.9.13", "nuxt-swiper": "^1.2.2", "oauth-1.0a": "^2.2.6", "vue": "^3.4.21", "vue-image-zoomer": "^2.4.0", "vue-router": "^4.3.0", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@element-plus/nuxt": "^1.0.9", "@nuxtjs/device": "^3.1.1", "@sidebase/nuxt-pdf": "^1.0.0-alpha.0", "@types/nodemailer": "^6.4.15", "autoprefixer": "^10.4.19", "element-plus": "^2.9.6", "nuxt": "^3.11.2", "nuxt-icon": "1.0.0-beta.7"}}