import OAuth from 'oauth-1.0a'
import crypto from 'crypto'
import config from '../../../config.json'

// Function to generate OAuth 1.0a headers
const getOAuthHeader = (url: string, method: string, consumerKey: string, consumerSecret: string) => {
  const oauth = new OAuth({
    consumer: { key: consumerKey, secret: consumerSecret },
    signature_method: 'HMAC-SHA1',
    hash_function(baseString: string, key: string) {
      return crypto.createHmac('sha1', key).update(baseString).digest('base64')
    },
  })

  const requestData = {
    url: url,
    method: method,
  }

  return oauth.toHeader(oauth.authorize(requestData))
}


export default defineEventHandler(async (event) => {
   try {
    const queryId = event.context.params.id

    const url = `${process.env.WC_API_URL}product/brands?category_id=${queryId}`
    const method = 'GET'
    const consumerKey = 'ck_da29de315a5e4c5d1873adfd87a3d30db0fce742'
    const consumerSecret = 'cs_0088f2bd53cb7d47c8758217a122f4c24e1c4130'
    
    const headers = {
      ...getOAuthHeader(url, method, consumerKey, consumerSecret),
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

      const response = await fetch(url, {
        method: method,
        headers: headers,
      })
  
      const data = await response.json()
      return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
 })
