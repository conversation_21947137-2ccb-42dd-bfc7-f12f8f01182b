<template>
  <div class="mt-5 md:mt-[60px] mb-[100px] md:mb-0">
    <div class="px-[20px] max-w-[1280px] mx-auto privacy-policy rtl:font-notoSans text-primary">
      <div v-html="locale === 'en' ? pagesContent['privacy-policy']?.content : pagesContent['privacy-policy']?.content_ar" class="mb-10 ">
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PagesContent } from '~/types/pagesContent'

const { locale } = useI18n()
const pagesContent = useState<PagesContent>("pagesContent")
</script>
<style>
.privacy-policy p {
  padding: 10px 0;
}
.privacy-policy br {
  display: block;
  content: "";
  margin: 10px 0;
}
.privacy-policy h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 10px 0;
}
.privacy-policy a {
  color: #007bff;
  text-decoration: underline;
}
</style>