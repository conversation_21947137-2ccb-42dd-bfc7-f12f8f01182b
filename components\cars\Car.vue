<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="text-primary border-[3px] border-primary p-5 sm:p-10 mt-10 rounded-md">
      <h2 class="italic font-semibold text-primary text-center sm:text-[22px]">{{ $t('CANNOT FIND YOUR TIRE, YOU CAN SELECT YOUR CAR BRAND AND MODEL.') }}</h2>
      <div class="flex justify-center flex-col sm:flex-row items-center gap-8 mt-10">
        <div class="bg-white rounded-full border-2 border-primary">
          <img src="~/assets/images/category/car.png" alt="Car"/>
        </div>
        <div class="select-cars w-full sm:w-[50%]">
          <el-select
            v-for="(variation, attrIndex) in optionss"
            :key="attrIndex"
            clearable
            class="mb-3"
            size="large"
            style="width: 100%"
            loading-text="Loading data"
          >
            <el-option
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <UButton
        class="
          mx-auto
          mt-10
          text-thirdColor
          flex
          flex-col
          items-center
          justify-between
          font-bold
          text-xl
          py-3
          px-6
          sm:px-14
          rounded-full
          text-[15px]
          uppercase
          md:text-base
          bg-primary
          shadow-lg
          shadow-primary-500/40
          border-2
          border-primary
          transition-all
          rtl:font-notoSans
          duration-500
          ease-in-out
          hover:text-thirdColor
          hover:bg-secondary
          hover:border-2
          hover:border-secondary"
      >
        {{ $t('FIND The Matching Tires') }}
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
const optionss = [
  {name: 'BMW'},
  {name: '320 | 2024 | A/T | Advantage'},
  {name: '1600 Turbo'},
]
</script>
<style>
</style>