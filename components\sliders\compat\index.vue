<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="border-t border-b border-primary pt-10">
      <div class="relative mx-auto recentlySwiper pb-[60px]">
        <Swiper
          @swiper="onSwiper"
          :modules="[ SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
          :loop="true"
          :space-between="20"
          :autoplay="{
            delay: 4000,
            disableOnInteraction: false,
          }"
          :breakpoints="{
            360: {
                slidesPerView: 1,
            },
            640: {
                slidesPerView: 1,
            },
            768: {
                slidesPerView: 2,
            },
            1024: {
                slidesPerView: 3.5,
            },
            1400: {
                slidesPerView: 3.5,
            },
            1600: {
                slidesPerView: 3.5,
            },
            1920: {
                slidesPerView: 3.5,
            },
            2560: {
                slidesPerView: 3.5,
            },
            3200: {
                slidesPerView: 3.5,
            },
          }"
        >
        <div class="absolute top-0 left-0 w-full z-20">
          <div class="flex justify-between items-center w-full">
            <div class="font-secondaryFont uppercase text-primary text-lg sm:text-[25px] font-bold">
              MAKE Compatibility
            </div>
            <div class="top-slider-arrows hidden sm:flex gap-2">
              <div
                class="swiper-button-prev-outside cursor-pointer w-[40px] h-[40px] bg-primary rounded-full flex justify-center items-center"
                @click="swiperInstance.slideNext()"
              >
                <Icon class="text-thirdColor cursor-pointer" name="carbon:chevron-left" size="30" />
              </div>
              <div
                class="swiper-button-next-outside cursor-pointer w-[40px] h-[40px] bg-primary rounded-full flex justify-center items-center"
                @click="swiperInstance.slidePrev()"
              >
                <Icon class="text-thirdColor cursor-pointer" name="carbon:chevron-right" size="30" />
              </div>
            </div>
          </div>
        </div>
          <SwiperSlide v-for="(item, index) in items" :key="index">
            <div class="mx-auto w-full">
              <div class="mt-[50px]">
                <UBadge class="py-6 px-4 justify-center font-thirdFont items-center text-lg font-semibold text-thirdColor bg-secondary w-full">
                  {{ item.name }}
                </UBadge>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const items = [
  { 'name': 'TOYOTA YARIS | 2012-2015' },
  { 'name': 'HONDA CITY |  2020-2025' },
  { 'name': 'TOYOTA YARIS | 2012-2015' },
  { 'name': 'TOYOTA YARIS | 2012-2015' },
  { 'name': 'HONDA CITY |  2020-2025' },
  { 'name': 'TOYOTA YARIS | 2012-2015' },
]

// Swipper settings
const swiperInstance = ref()
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.recentlySwiper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', onMouseOver)
    slide.addEventListener('mouseleave', onMouseLeave)
    slide.addEventListener('touchstart', onMouseOver)
    slide.addEventListener('touchend', onMouseLeave)
  })
})

function onMouseOver(event: any) {
  swiperInstance.value.autoplay.stop()
}

function onMouseLeave(event: any) {
  swiperInstance.value.autoplay.start()
}
</script>
<style>
</style>