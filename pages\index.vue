<template>
  <div>
    <div
      v-for="(section, index) in pagesContent?.home?.sections"
      :key="index"
      class="md:mt-[60px]"
    >
      <SlidersProduct
        v-if="section?.type === 'products_carousel'"
        :products="section?.products"
        :title="locale === 'ar' ? section?.title_ar : section?.title"
      />
      <SlidersAdverAds :products="section?.ads" v-if="section?.type === 'ads_carousel'" />
      <SlidersLogosHome :logos="section?.brands" v-if="section?.type === 'brands_carousel'" />
      <SlidersBlogsTips
        v-if="section?.type === 'articles_carousel'"
        :posts="section?.posts"
        :title="locale === 'ar' ? section?.title_ar : section?.title"
        class="mb-[60px] mt-[60px]"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import type { PagesContent } from '~/types/pagesContent'
const { locale } = useSafeI18n()

const pagesContent = useState<PagesContent>("pagesContent")
</script>
