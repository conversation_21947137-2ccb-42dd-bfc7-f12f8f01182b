export default defineEventHandler(async (event) => {
   try {
    const queryId: any = event.context.params?.id
    const query = getQuery(event)
    
    const page = query.page || 1
    const posts_per_page = query.posts_per_page || 10
    const rate = query.rate || ''

    const url = `${process.env.WP_API_URL}get_user_reviews?user_id=${queryId}&page=${page}&posts_per_page=${posts_per_page}${rate ? `&rate=${rate}` : ''}`;
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json',
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    })
  
    const data = await response.json()
    return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
})
