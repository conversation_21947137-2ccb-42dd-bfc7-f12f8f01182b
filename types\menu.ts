/**
 * Interface for a menu item
 */
export interface MenuItem {
  /**
   * Display name of the menu item
   */
  name: string;
  
  /**
   * URL slug for the menu item
   */
  slug: string;
  
  /**
   * ID of the menu item (category ID or page ID)
   */
  id: number;
  
  /**
   * Icon URL (false if no icon)
   */
  icon: string | false;
}

/**
 * Interface for the menu structure
 */
export interface MenuStructure {
  /**
   * Side menu items
   */
  side_menu: MenuItem[];
  
  /**
   * Footer menu items
   */
  footer_menu: MenuItem[];
}
