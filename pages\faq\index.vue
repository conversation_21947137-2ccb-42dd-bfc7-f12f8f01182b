<template>
  <div class="md:mt-[60px]">
    <div class="text-primary font-secondaryFont text-[16px] md:hidden" style="background-color:#B7CDC2;">
      <div class="px-[20px] max-w-[1280px] mx-auto rtl:font-notoSans py-2 text-center font-secondaryFont">
        {{ locale === 'en' ? pagesContent?.faq?.page_banner_desc : pagesContent?.faq?.page_banner_desc_ar }}
      </div>
    </div>
    <div>
      <div class="mb-10 md:hidden faqdropdown faq-dropdown-res">
        <el-select
          v-model="activeItem"
          placeholder="Categories"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in items.terms"
            :key="index"
            :label="locale === 'en' ? item?.name : item?.name_ar"
            :value="item.slug"
          />
        </el-select>
      </div>
    </div>
    <div class="px-[20px] max-w-[1280px] mx-auto mb-20">
      <div class="lg:px-10 hidden md:block">
        <div class="bg-cartColor rounded-2xl p-5 sm:p-10 text-black">
          <div class="flex md:gap-20 gap-4 justify-between flex-col md:flex-row items-center">
            <div class="font-secondaryFont text-sm sm:text-lg text-primary rtl:font-notoSans">
              {{ locale === 'en' ? pagesContent?.faq?.page_banner_desc : pagesContent?.faq?.page_banner_desc_ar }}
            </div>
            <UButton
              class="rounded-full flex items-center justify-center font-bold sm:text-xl py-3 sm:px-20 px-14 font-thirdFont text-thirdColor uppercase bg-orangeColor border-2 border-orangeColor hover:bg-secondary hover:border-secondary transition-all duration-500 ease-in-out"
              :to="localePath(`/${pagesContent?.faq?.banner_link_slug}`)"
            >
              {{ locale === 'en' ? pagesContent?.faq?.banner_link_text : pagesContent?.faq?.banner_link_text_ar }}
            </UButton>
          </div>
        </div>
      </div>

      <div class="flex flex-col md:flex-row gap-4 lg:px-10 md:mt-[60px]">
        <!-- Sidebar for Collapse Titles -->
        <div class="hidden md:block md:w-[30%] border-2 border-orangeColor p-5 rounded-2xl faq-sidebar faq-container h-fit">
          <el-collapse v-model="activeItem" accordion>
            <el-collapse-item
              v-for="(item, index) in items?.terms ?? []"
              :key="index"
              :title="locale === 'en' ? item?.name : item?.name_ar"
              :name="item.slug"
            >
              <template #title>
                <span
                  class="font-bold text-orangeColor lg:text-[15px] font-primaryFont uppercase text-start rtl:font-notoSans"
                  :class="{'active-title !text-primary': activeItem === item.slug}"
                  v-html="locale === 'en' ? item?.name : item?.name_ar"
                >
                </span>
              </template>
            </el-collapse-item>
          </el-collapse>
        </div>
        <!-- Main Content -->
        <div class="faq-content-container-custom md:w-[70%]">
          <el-collapse v-model="activeItems" accordion>
            <el-collapse-item 
              v-for="(item, index) in filteredQuestions ?? []"
              :key="index"
              :title="locale === 'en' ? item?.question : item?.question_ar"
              :name="item.question"
            >
              <template #title>
                <span v-html="locale === 'en' ? item?.question : item?.question_ar"></span>
              </template>
              <div v-html="locale === 'en' ? item.answer : item.answer_ar" class="my-[20px] text-[15px] rtl:font-notoSans"></div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import type { PagesContent } from '~/types/pagesContent'

const { locale } = useI18n()
const localePath = useLocalePath()

/**
 * Interface for FAQ question
 */
interface FaqQuestion {
  id: number;
  question: string;
  question_ar?: string;
  answer: string;
  answer_ar?: string;
  filter: {
    id: number;
    name: string;
    slud: string; // Note: This appears to be a typo in the API (should be 'slug')
  };
}

const router = useRouter()
const route = useRoute()

// Get FAQ data from API
const { items } = await fetchHock('newBranch/faq')

// Get page content
const pagesContent = useState<PagesContent>("pagesContent", () => ({} as PagesContent))

// Active category and question state
const activeItem = ref<string>(items?.value?.terms?.[0]?.slug || '')
const activeItems = ref<string>('')

/**
 * Filter questions based on active category
 */
const filteredQuestions = computed<FaqQuestion[]>(() => {
  if (!items?.value?.questions) return []

  return items.value.questions.filter(
    (item: FaqQuestion) => item.filter.slud === activeItem.value
  )
})

// Function to set the first question in the current tab
const setFirstActiveItem = () => {
  if (filteredQuestions.value.length > 0) {
    activeItems.value = filteredQuestions.value[0].question // Always use the first question as active
  }
}

onMounted(() => {
  const queryItem = route.query.activeItem;
  if (typeof queryItem === 'string') {
    activeItem.value = queryItem
  }

  // Set first question in the active tab
  setFirstActiveItem()
})

// Watch activeItem for changes to update activeItems
watch(activeItem, (current) => {
  router.push({ path: localePath('/faq'), query: { activeItem: current } });
  setFirstActiveItem() // Update the active item when switching tabs
})
</script>

<style>
.faq-container .el-collapse {
  border: none !important;
}

.faq-container .el-collapse-item__header {
  padding: 30px 0 !important;
  cursor: pointer;
  border: none !important;
  line-height: 1.2;
  border-bottom: 2px solid #FF671F !important;
}

.faq-container .el-collapse-item:last-child .el-collapse-item__header {
  border-bottom: none !important;
}

.faq-container .el-collapse-item__header i {
  display: none;
}

.faq-sidebar .el-collapse-item__wrap {
  display: none !important;
}


/* Content */
.faq-content-container-custom .el-collapse {
  border: none;
}

.faq-content-container-custom .el-collapse-item__header {
  font-weight: bold;
  font-size: 20px;
  line-height: 1.2;
  color: #1D3C34;
  background-color: #E9EAC8;
  border: 1px solid #1D3C34;
  padding: 15px 30px;
  border-radius: 15px;
  text-align: start;
  height: auto;
}

.faq-content-container-custom .el-collapse-item__header.is-active {
  border: unset;
  border-bottom: 1px solid #1D3C34;
  border-radius: unset;
}

.faq-content-container-custom .el-collapse-item {
  overflow: hidden;
  margin-bottom: 20px;
}

.faq-content-container-custom .el-collapse-item__header i {
  display: none;
}

.faq-content-container-custom .el-collapse-item__wrap {
  background-color: transparent;
  border-bottom: none;
  padding: 0 30px;
  color: #1D3C34;
  font-size: 15px;
}

.faq-content-container-custom .el-collapse-item__content {
  background-color: transparent;
  padding-bottom: 0;
  color: #1D3C34;
}

.faq-content-container-custom .el-collapse-item.is-active {
  border: 1px solid #1D3C34;
  border-radius: 15px;
}

@media (max-width: 1024px) {
  .faq-content-container-custom .el-collapse-item__header {
    font-size: 16px !important;
  }
}

@media (max-width: 768px) {
  .faq-content-container-custom .el-collapse-item__header {
    font-size: 16px !important;
  }
}

@media (max-width: 640px) {
  .faq-content-container-custom .el-collapse-item__header {
    height: fit-content;
    padding: 13px 15px;
    line-height: 1.5;
  }
}
.faqdropdown .el-select__wrapper {
  border: 1px solid #1D3C34;
  padding: 17px 15px;
  border-radius: 15px;
  color: #1D3C34;
}

.faqdropdown .el-select__placeholder {
  color: #1D3C34;
  font-weight: bold;
  font-size: 16px;
}
.faq-dropdown-res .el-select__wrapper {
  border-radius: 0;
  background-color: #E9EAC8;
}

.faqdropdown .el-select__wrapper.is-focused {
  box-shadow: unset;
}
.faq-content-container-custom ol,
.faq-content-container-custom ul {
  list-style: auto;
  margin-inline-start: 30px;
}
</style>