<template>
  <div class="bg-thirdColor border-b border-primary md:pt-10">
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="flex items-center justify-between gap-8 flex-col lg:flex-row lg:gap-0 md:px-10 md:pb-10 py-5 md:py-0">
        <div class="flex items-center gap-5">
          <div class="border border-primary rounded-full bg-white p-2 overflow-hidden w-[100px] h-[100px] sm:w-[180px] sm:h-[180px] flex items-center justify-center">
            <div class="w-full h-full relative truncate fileInput">
              <img
                v-if="!photoLoading"
                class="rounded-full md:w-[162px] md:h-[162px]"
                :src="photo?.url ? photo?.url : profilePic?.value ? profilePic?.value : profileData?.avatar_url"
                :alt="locale === 'en' ? profileData?.first_name : profileData?.first_name_ar"
              />
              <el-skeleton animated v-else class="!h-full">
                <template #template class="!h-full">
                  <el-skeleton-item variant="image" class="!rounded-full !w-full !h-full"/>
                </template>
              </el-skeleton>
              <!-- <UInput v-model="photoFile" @change="uploadPhoto" type="file" class="fileInput" /> -->
              <input 
                  type="file" 
                  @change="uploadPhoto"
                  accept="image/*"
                  class="form-control"
              />
            </div>
          </div>
          <div>
            <h2 class="font-secondaryFont font-bold text-primary md:text-2xl sm:mb-4 rtl:font-notoSans">
              {{ locale === 'en' ? profileData?.first_name : profileData?.first_name_ar }} {{ locale === 'en' ? profileData?.last_name : profileData?.last_name_ar }}
            </h2>
            <div class="font-thirdFont text-primary md:text-base text-sm rtl:font-notoSans">
              {{ $t('CUSTOMER') }}
            </div>
            <div class="font-thirdFont text-primary md:text-base text-sm rtl:font-notoSans">
              {{ $t('Since') }} {{ locale === 'en' ? formattedDate?.en : formattedDate?.ar }}
            </div>
          </div>
        </div>

        <div class="sm:gap-3 gap-[5px] hidden md:flex">
          <div>
            <UButton
              :class="{'bg-orangeColor': currentTab === 'profile', 'bg-primary': currentTab !== 'profile'}"
              @click="setActiveTab('profile')"
              class="text-thirdColor uppercase rtl:font-notoSans font-secondaryFont font-bold text-[12px] sm:text-lg py-3 mb-4 px-3 w-full xl:px-10 xl:min-w-[300px] rounded-full flex items-center justify-center border hover:bg-orangeColor hover:border-orangeColor ease-in-out duration-500"
              variant="solid"
            >
              {{ $t('MY PROFILE') }}
            </UButton>
            <UButton
              @click="setActiveTab('support')"
              :class="{'bg-orangeColor': currentTab === 'support', 'bg-primary': currentTab !== 'support'}"
              class="text-thirdColor uppercase rtl:font-notoSans font-secondaryFont font-bold text-[12px] sm:text-lg py-3 mb-4 px-3 w-full xl:px-10 xl:min-w-[300px] rounded-full flex items-center justify-center border hover:bg-orangeColor hover:border-orangeColor ease-in-out duration-500"
              variant="solid"
            >
              {{ $t('SUPPORT TICKETS') }}
            </UButton>
          </div>
          <div>
            <UButton
              @click="setActiveTab('reviews')"
              :class="{'bg-orangeColor': currentTab === 'reviews', 'bg-primary': currentTab !== 'reviews'}"
              class="text-thirdColor uppercase rtl:font-notoSans font-secondaryFont font-bold text-[12px] sm:text-lg py-3 mb-4 px-3 w-full xl:px-10 xl:min-w-[300px] rounded-full flex items-center justify-center border hover:bg-orangeColor hover:border-orangeColor ease-in-out duration-500"
              variant="solid"
            >
              {{ $t('MY REVIEWS') }}
            </UButton>
            <UButton
              @click="setActiveTab('history')"
              :class="{'bg-orangeColor': currentTab === 'history', 'bg-primary': currentTab !== 'history'}"
              class="text-thirdColor uppercase rtl:font-notoSans font-secondaryFont font-bold text-[12px] sm:text-lg py-3 mb-4 px-3 w-full xl:px-10 xl:min-w-[300px] rounded-full flex items-center justify-center border hover:bg-orangeColor hover:border-orangeColor ease-in-out duration-500"
              variant="solid"
            >
              {{ $t('MY ORDERS') }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="bg-primary md:hidden">
    <div class="px-[20px] max-w-[1280px] mx-auto flex items-center justify-between py-2">
      <div class="w-full border-r border-thirdColor">
        <div
          :class="{'bg-primary !text-orangeColor': currentTab === 'profile', 'bg-primary': currentTab !== 'profile'}"
          @click="setActiveTab('profile')"
          class="text-thirdColor rtl:font-notoSans uppercase font-secondaryFont font-bold text-[13px] focus:bg-primary  rounded-none text-center py-1"
          variant="solid"
        >
          {{ $t('PROFILE') }}
        </div>
      </div>
      <div class="w-full border-r border-thirdColor">
        <div
          @click="setActiveTab('support')"
          :class="{'bg-primary !text-orangeColor': currentTab === 'support', 'bg-primary': currentTab !== 'support'}"
          class="text-thirdColor rtl:font-notoSans uppercase font-secondaryFont font-bold text-[13px] focus:bg-primary  rounded-none text-center py-1"
          variant="solid"
        >
          {{ $t('SUPPORT') }}
        </div>
      </div>
      <div class="w-full border-r border-thirdColor">
        <div
          @click="setActiveTab('reviews')"
          :class="{'bg-primary !text-orangeColor': currentTab === 'reviews', 'bg-primary': currentTab !== 'reviews'}"
          class="text-thirdColor rtl:font-notoSans uppercase font-secondaryFont font-bold text-[13px] focus:bg-primary rounded-none text-center py-1"
          variant="solid"
        >
          {{ $t('REVIEWS') }}
        </div>
      </div>
      <div class="w-full ">
        <div
          @click="setActiveTab('history')"
          :class="{'bg-primary !text-orangeColor': currentTab === 'history', 'bg-primary': currentTab !== 'history'}"
          class="text-thirdColor rtl:font-notoSans uppercase font-secondaryFont font-bold text-[13px] focus:bg-primary text-center py-1"
          variant="solid"
        >
          {{ $t('ORDERS') }}
        </div>
      </div>
    </div>
  </div>
  <div>
    <ProfileInformation v-if="currentTab === 'profile'" />
    <ProfileSupport v-if="currentTab === 'support'" />
    <ProfileMyorders v-if="currentTab === 'history'" />
    <ProfileReviews v-if="currentTab === 'reviews'" />
  </div>
</template>
<script setup lang="ts">
import { persistentCookieOptions } from '~/utils/cookieOptions'

// Reactive state for the active tab
const router = useRouter()
const route = useRoute()
const tokenCookie = useCookie('token', persistentCookieOptions)

const { locale } = useI18n()

const currentTab = ref('profile')
const profilePic = ref()

const queryItem = route.query.tab;
if (typeof queryItem === 'string') {
  currentTab.value = queryItem
}

// Method to update the active tab
const setActiveTab = (tab: string) => {
  currentTab.value = tab
  router.push({ query: { tab: currentTab.value } });
}


const profileData: any = useState("profileData", () => {})
const formattedDate = ref()
const additionalAddress: any = useState("additionalAddress", () => [])


watch(profileData, (current) => {
  const dateString = profileData.value.date_created
  const dateObject = new Date(dateString)
  // English
  const formattedDateEn = dateObject.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long' 
  })
  // Arabic
  const formattedDateAr = dateObject.toLocaleDateString('ar-EG', { 
    year: 'numeric', 
    month: 'long' 
  })

  formattedDate.value = { en: formattedDateEn, ar: formattedDateAr }
})

const userData: any = useState("userData", () => [])

onMounted(() => {
  const userData: any = useState("userData", () => [])
  
  setTimeout(async () => {
    const { items } = await fetchHock(`newBranch/customer/${Number(userData?.value?.id)}`)
    profileData.value = items.value
    items.value.meta_data.map((el: any) => {
      if (el.key === 'usr_profilepic') {
        profilePic.value = el
      }
      if (el.key === 'additional_shipping_addresses') {
        additionalAddress.value = el.value
      }
    })
  }, 1)


  
  // Check if the user is trying to access the login page and is already logged in
  if (!tokenCookie.value) navigateTo(localePath('/auth/login'))
})

const photoLoading = ref(false)
const photo = ref<any>()
const uploadPhoto = async (event: any) => {
  photoLoading.value = true
  const formData = new FormData()

  // Add the file to the formData
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    formData.append('file', file)
  }

  // Add the user_id to the formData
  formData.append('user_id', userData.value.id.toString())  // Ensure it's a string

  // Set up the headers
  const headers = {
    'Authorization': `Basic ${btoa('endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb')}`
    // 'Content-Type': 'multipart/form-data',  // Remove this line
  }

  // Make the API call
  try {
    const config = useRuntimeConfig()
    const response = await $fetch(`${config.public.wpApiUrl}profile_picture`, {
      method: 'POST',
      body: formData,
      headers: headers,
    })

    // Handle the response
    photo.value = response
    // Update the profile picture in your state or UI
  } catch (error) {
    console.error('Error uploading photo:', error)
    // Handle the error (e.g., show an error message to the user)
  } finally {
    photoLoading.value = false
  }
}
</script>
<style>
/* .fileInput {
  width: 100%;
  height: 100%;
  cursor: pointer;
} */
.fileInput input {
  position: absolute;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
</style>