export default defineEventHandler(async (event) => {
   try {
    const queryId: any = event.context.params.id

    const url = `${process.env.WP_API_URL}validate-mobile-verify?user_id=${queryId}`;
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json',
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    })
  
    const data = await response.json()
    return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
})
