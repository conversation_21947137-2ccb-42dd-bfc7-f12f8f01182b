<template>
  <div>
    <div class="flex gap-2 items-center">
      <div class="relative">
        <div id="g_id_onload"
          data-client_id="56182116676-r7h8lrkf5nru3g3mv57eoftrua31v25g.apps.googleusercontent.com"
          data-callback="handleCredentialResponse"
          data-auto_prompt="false">
        </div>
        <div class="g_id_signin absolute w-[50px] h-[100px] top-[3px] opacity-0 z-[100] left-0 rounded-full" data-type="standard"></div>
        <div class="w-[40px] h-[40px] md:w-[50px] md:h-[50px] rounded-full bg-primary border border-primary flex items-center justify-center relative z-[0] overflow-hidden">
          <svg width="20" height="21" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M23.8281 12.3926C23.8281 19.3018 19.0967 24.2188 12.1094 24.2188C5.41016 24.2188 0 18.8086 0 12.1094C0 5.41016 5.41016 0 12.1094 0C15.3711 0 18.1152 1.19629 20.2295 3.16895L16.9336 6.33789C12.6221 2.17773 4.60449 5.30273 4.60449 12.1094C4.60449 16.333 7.97852 19.7559 12.1094 19.7559C16.9043 19.7559 18.7012 16.3184 18.9844 14.5361H12.1094V10.3711H23.6377C23.75 10.9912 23.8281 11.5869 23.8281 12.3926Z" fill="#E9EAC8"/>
          </svg>
        </div>
      </div>
      <div class="w-[40px] h-[40px] md:w-[50px] md:h-[50px] rounded-full bg-primary border border-primary flex items-center justify-center">
        <UButton
          @click="loginWithFacebook"
          external
          class="bg-transparent hover:bg-transparent"
        >
          <svg class="hidden md:block" width="25" height="25" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M30 15.0913C30 6.75456 23.2863 0 15 0C6.71371 0 0 6.75456 0 15.0913C0 22.6235 5.48528 28.8669 12.6562 30V19.4538H8.84577V15.0913H12.6562V11.7663C12.6562 7.98438 14.8942 5.89533 18.3218 5.89533C19.9633 5.89533 21.6798 6.18986 21.6798 6.18986V9.90183H19.7879C17.925 9.90183 17.3438 11.0653 17.3438 12.2586V15.0913H21.5038L20.8385 19.4538H17.3438V30C24.5147 28.8669 30 22.6235 30 15.0913Z" fill="#E9EAC8"/>
          </svg>
          <svg class="md:hidden" width="22" height="22" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M30 15.0913C30 6.75456 23.2863 0 15 0C6.71371 0 0 6.75456 0 15.0913C0 22.6235 5.48528 28.8669 12.6562 30V19.4538H8.84577V15.0913H12.6562V11.7663C12.6562 7.98438 14.8942 5.89533 18.3218 5.89533C19.9633 5.89533 21.6798 6.18986 21.6798 6.18986V9.90183H19.7879C17.925 9.90183 17.3438 11.0653 17.3438 12.2586V15.0913H21.5038L20.8385 19.4538H17.3438V30C24.5147 28.8669 30 22.6235 30 15.0913Z" fill="#E9EAC8"/>
          </svg>
        </UButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { persistentCookieOptions } from '~/utils/cookieOptions'

const isSignedIn = ref(false);
const errorMessage = ref('');
const socialData: any = useState("socialData", () => '')
const userDatas = ref({});
const urlError: any = useState("urlError", () => '')
const titleError: any = useState("titleError", () => '')
const socialLoading: any = useState("socialLoading", () => false)
const router = useRouter()
const localePath = useLocalePath()

// const token: any = useState("token", () => '')
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => [])
const cartFulltem: any = useState("cartFulltem", () => {})
const showModal: any = useState("showModal", () => false)

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

// Create a unique ID for the visitor
const uniqueid = (): string => {
  // always start with a letter (for DOM friendlyness)
  let idstr = String.fromCharCode(Math.floor((Math.random() * 25) + 65));
  do {
    // between numbers and characters (48 is 0 and 90 is Z (42-48 = 90)
    const ascicode = Math.floor((Math.random() * 42) + 48);
    if (ascicode < 58 || ascicode > 64) {
      // exclude all chars between : (58) and @ (64)
      idstr += String.fromCharCode(ascicode)
    }
  } while (idstr.length < 32)

  return idstr
}

const isLoggedIn = async () => {
  titleError.value = ''
  urlError.value = ''
  socialLoading.value = true
  if (socialData.value) {
    const { items } = await fetchHock(`newBranch/orders/${Number(socialData.value)}`)

    const orders = ref()
    const completeOrder = localStorage.getItem("completeOrder")
    orders.value = completeOrder ? JSON.parse(completeOrder) : null
    if (items?.value?.single[0]?.id) {
      await $fetch(`/api/newBranch/orders/delete/${items?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any

      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: socialData.value,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/newBranch/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee.line_items
        cartFulltem.value = responsee
      }
    } else {
      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: socialData.value,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/newBranch/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee.line_items
        cartFulltem.value = responsee
      }
    }
    // Delete Old Order
    const { items: usersData } = await fetchHock(`newBranch/address/${Number(socialData.value)}`)
    userData.value = usersData.value

    const { items: lastOrders } = await fetchHock(`newBranch/orders/${Number(socialData.value)}`)
    cartItems.value = lastOrders?.value?.single[0]?.items
    cartFulltem.value = lastOrders?.value?.single[0]
    
    tokenCookie.value = uniqueid()
    const currentRoute = router.currentRoute.value.path
    if (currentRoute === '/cart/mobile/userinfo') {
      router.push(localePath('/cart/mobile/address'))
    } else {
      router.push(localePath('/checkout'))
    }
  }
  socialLoading.value = false
}

const decodeJWT = (token) => {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
  }).join(''));
  return JSON.parse(jsonPayload);
};

const handleCredentialResponse = async (response) => {
  const decodedToken = decodeJWT(response.credential);
  const res: any = await $fetch('/api/newBranch/auth/providers', {
    method: 'post',
    body: {
      first_name: decodedToken.given_name,
      last_name: decodedToken.family_name,
      email: decodedToken.email,
      auth_method: 'google',
    },
  }) as any

  if (res.error) {
    urlError.value = res.error.message;
    titleError.value = res.error.error_title;
  } else {
    userIdCookie.value = res.user_id
    tokenCookie.value = uniqueid()
    socialData.value = res.user_id
    await isLoggedIn()
  }
  isSignedIn.value = true;
};

const handleFacebookResponse = (response) => {
  if (response.status === 'connected') {
    FB.api('/me', { fields: 'name,email,picture' }, (userInfo) => {
      userDatas.value = userInfo;
      tokenCookie.value = uniqueid()
      isSignedIn.value = true;
    });
  } else {
    errorMessage.value = 'Failed to authenticate with Facebook.';
  }
};

const loginWithFacebook = () => {
  FB.login(handleFacebookResponse, { scope: 'public_profile,email' });
};

// Ensure the function is available globally
if (typeof window !== 'undefined') {
  window.handleCredentialResponse = handleCredentialResponse;
}

watch(userDatas, async (newValue) => {
  if (newValue && newValue.email) {
    const [firstName, lastName] = newValue.name.split(' ');

    const res: any = await $fetch('/api/newBranch/auth/providers', {
      method: 'post',
      body: {
        first_name: firstName,
        last_name: lastName,
        email: newValue.email,
        auth_method: 'facebook',
      },
    }) as any

    if (res.error) {
      urlError.value = res.error.message;
      titleError.value = res.error.error_title;
    } else {
      userIdCookie.value = res.user_id
      tokenCookie.value = uniqueid()
      socialData.value = res.user_id;
      await isLoggedIn()
    }
  }
});

onMounted(() => {
  const script = document.createElement('script');
  script.src = 'https://accounts.google.com/gsi/client';
  script.async = true;
  script.defer = true;
  script.onload = () => {
    window.handleCredentialResponse = handleCredentialResponse;
  };
  script.onerror = () => {
    console.error('Failed to load Google Sign-In script.');
    errorMessage.value = 'Failed to load Google Sign-In. Please try again later.';
  };
  document.head.appendChild(script);

  // Load Facebook SDK
  window.fbAsyncInit = function() {
    FB.init({
      appId      : '****************',
      cookie     : true,
      xfbml      : true,
      version    : 'v12.0'
    });
  };

  (function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) { return; }
    js = d.createElement(s); js.id = id;
    js.src = "https://connect.facebook.net/en_US/sdk.js";
    fjs.parentNode.insertBefore(js, fjs);
  }(document, 'script', 'facebook-jssdk'));
})
</script>