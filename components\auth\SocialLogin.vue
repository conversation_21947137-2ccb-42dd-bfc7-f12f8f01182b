<template>
  <div class="pt-5 relative sm:max-w-[60%] mx-auto">
    <div class="flex justify-between items-center flex-col-reverse md:flex-row gap-5">
      <div class="text-primary font-bold text-lg">
        {{ $t('Login using Social Media') }}
      </div>
      <div class="flex gap-2 items-center">














        <div class="relative">
          <div id="g_id_onload"
            data-client_id="56182116676-r7h8lrkf5nru3g3mv57eoftrua31v25g.apps.googleusercontent.com"
            data-callback="handleCredentialResponse"
            data-auto_prompt="false">
          </div>
          <div class="g_id_signin absolute w-[50px] h-[100px] top-[3px] opacity-0 z-[100] left-0 rounded-full" data-type="standard"></div>
          <div class="w-[50px] h-[50px] rounded-full bg-thirdColor border border-primary flex items-center justify-center relative z-[0] overflow-hidden">
            <svg width="20" height="21" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23.8281 12.3926C23.8281 19.3018 19.0967 24.2188 12.1094 24.2188C5.41016 24.2188 0 18.8086 0 12.1094C0 5.41016 5.41016 0 12.1094 0C15.3711 0 18.1152 1.19629 20.2295 3.16895L16.9336 6.33789C12.6221 2.17773 4.60449 5.30273 4.60449 12.1094C4.60449 16.333 7.97852 19.7559 12.1094 19.7559C16.9043 19.7559 18.7012 16.3184 18.9844 14.5361H12.1094V10.3711H23.6377C23.75 10.9912 23.8281 11.5869 23.8281 12.3926Z" fill="#1D3C34"/>
            </svg>
          </div>
        </div>
        <div class="w-[50px] h-[50px] rounded-full bg-thirdColor border border-primary flex items-center justify-center relative z-[110]">
          <UButton
            @click="loginWithFacebook"
            external
            class="bg-transparent hover:bg-transparent"
          >
            <svg width="25" height="25" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M30 15.0913C30 6.75456 23.2863 0 15 0C6.71371 0 0 6.75456 0 15.0913C0 22.6235 5.48528 28.8669 12.6562 30V19.4538H8.84577V15.0913H12.6562V11.7663C12.6562 7.98438 14.8942 5.89533 18.3218 5.89533C19.9633 5.89533 21.6798 6.18986 21.6798 6.18986V9.90183H19.7879C17.925 9.90183 17.3438 11.0653 17.3438 12.2586V15.0913H21.5038L20.8385 19.4538H17.3438V30C24.5147 28.8669 30 22.6235 30 15.0913Z" fill="#1D3C34"/>
            </svg>
          </UButton>
        </div>
      </div>
    </div>
    <div v-if="errorMessage" class="error">{{ errorMessage }}</div>
  </div>
</template>
<script setup lang="ts">
/**
 * Social login component state
 */
 const { locale, t } = useI18n()
 const localePath = useLocalePath()

const isSignedIn = ref<boolean>(false);
const errorMessage = ref<string>('');
const socialData = useState<string | number>("socialData", () => '');
const userData = ref<Record<string, any>>({});
const urlError = useState<string>("urlError", () => '');
const titleError = useState<string>("titleError", () => '');

titleError.value = ''
urlError.value = ''
/**
 * Decode a JWT token to extract user information
 * @param token The JWT token to decode
 * @returns The decoded token payload
 */
const decodeJWT = (token: string): Record<string, any> => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64).split('').map(c => {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return {};
  }
};

/**
 * Handle Google Sign-In response
 * @param response The response from Google Sign-In
 */
const handleCredentialResponse = async (response: { credential: string }): Promise<void> => {
  try {
    const decodedToken = decodeJWT(response.credential);

    // Call the API to authenticate with Google
    const res: any = await $fetch('/api/auth/providers', {
      method: 'post',
      body: {
        first_name: decodedToken.given_name,
        last_name: decodedToken.family_name,
        email: decodedToken.email,
        auth_method: 'google',
      },
    });

    // Handle API response
    if (res.error) {
      urlError.value = res.error.message;
      titleError.value = res.error.error_title;
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      socialData.value = res.user_id;
      navigateTo(localePath('/login-success'));
    }

    isSignedIn.value = true;
  } catch (error) {
    console.error('Error handling Google Sign-In:', error);
    errorMessage.value = t('Failed to authenticate with Google. Please try again.');
  }
};

/**
 * Handle Facebook login response
 * @param response The response from Facebook login
 */
const handleFacebookResponse = (response: { status: string }): void => {
  if (response.status === 'connected') {
    // Get user information from Facebook
    FB.api('/me', { fields: 'name,email,picture' }, (userInfo: Record<string, any>) => {
      userData.value = userInfo;
      isSignedIn.value = true;
    });
  } else {
    errorMessage.value = t('Failed to authenticate with Facebook.');
  }
};

/**
 * Initiate Facebook login
 */
const loginWithFacebook = (): void => {
  try {
    FB.login(handleFacebookResponse, { scope: 'public_profile,email' });
  } catch (error) {
    console.error('Error initiating Facebook login:', error);
    errorMessage.value = t('Failed to connect to Facebook. Please try again.');
  }
};

// Make the Google Sign-In callback available globally
if (typeof window !== 'undefined') {
  window.handleCredentialResponse = handleCredentialResponse;
}

/**
 * Watch for changes in user data from Facebook login
 */
watch(userData, async (newValue) => {
  if (newValue && newValue.email) {
    try {
      // Extract first and last name from the full name
      const nameParts = newValue.name.split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

      // Call the API to authenticate with Facebook
      const res: any = await $fetch('/api/auth/providers', {
        method: 'post',
        body: {
          first_name: firstName,
          last_name: lastName,
          email: newValue.email,
          auth_method: 'facebook',
        },
      });

      // Handle API response
      if (res.error) {
        urlError.value = res.error.message;
        titleError.value = res.error.error_title;
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        socialData.value = res.user_id;
        navigateTo(localePath('/login-success'));
      }
    } catch (error) {
      console.error('Error processing Facebook login:', error);
      errorMessage.value = t('Failed to process Facebook login. Please try again.');
    }
  }
});

/**
 * Load social login SDKs on component mount
 */
onMounted(() => {
  // Load Google Sign-In SDK
  const loadGoogleSDK = (): void => {
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;
    script.onload = () => {
      window.handleCredentialResponse = handleCredentialResponse;
    };
    script.onerror = () => {
      console.error('Failed to load Google Sign-In script.');
      errorMessage.value = t('Failed to load Google Sign-In. Please try again later.');
    };
    document.head.appendChild(script);
  };

  // Load Facebook SDK
  const loadFacebookSDK = (): void => {
    window.fbAsyncInit = function() {
      FB.init({
        appId: '****************',
        cookie: true,
        xfbml: true,
        version: 'v12.0'
      });
    };

    (function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) { return; }
      js = d.createElement(s);
      js.id = id;
      js.src = "https://connect.facebook.net/en_US/sdk.js";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
  };

  // Load both SDKs
  loadGoogleSDK();
  loadFacebookSDK();
});
</script>