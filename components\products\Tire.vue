<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="lg:px-10 md:py-5">
        <div class="flex justify-between flex-col md:flex-row items-start mt-5 md:mt-0">
          <div class="w-full md:max-w-[36%] flex flex-col justify-center items-center gap-8 md:mt-8">
            <vue-image-zoomer
              :zoom-amount="3"
              img-class="img-fluid"
              :regular="selectedImage?.src || product?.image"
            />
            <div class="flex-col items-center md:flex hidden">
              <div class="custom-rating">
                <el-rate :colors="colors" v-model="reviews.overall_rate" size="large" allow-half disabled />
              </div>
              <div class="font-bold font-secondaryFont text-2xl text-orangeColor mt-4">
                {{ $t('Reviews') }} - {{ reviews.overall_rate ? reviews.overall_rate : 0}}
              </div>
            </div>
          </div>
          <div class="w-full md:w-[60%] mt-8 hidden md:block">
            <div class="flex items-center gap-2 mb-6">
              <div class="text-primary font-semibold text-xl rtl:font-notoSans rtl:text-base rtl:font-bold">
                {{ $t('SHARE') }}
              </div>
              <div class="flex gap-3 items-center">
                <div @click="shareOnFacebook" class="bg-thirdColor border-2 border-primary w-[45px] h-[45px] flex items-center justify-center rounded-full cursor-pointer">
                  <div>
                    <svg width="12" height="20" viewBox="0 0 18 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16.5305 17.9165L17.4293 12.3666H11.8093V8.76502C11.8093 7.24666 12.5944 5.76663 15.1114 5.76663H17.6663V1.04145C17.6663 1.04145 15.3478 0.666504 13.1311 0.666504C8.50285 0.666504 5.47764 3.32468 5.47764 8.13671V12.3666H0.333008V17.9165H5.47764V31.3332H11.8093V17.9165H16.5305Z" fill="#1D3C34"/>
                    </svg>
                  </div>
                </div>
                <div @click="shareOnTwitter" class="bg-thirdColor border-2 border-primary w-[45px] h-[45px] flex items-center justify-center rounded-full cursor-pointer">
                  <div>
                    <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.1761 4H20.3037L14.0516 11.0948L21.5 20H15.8588L11.2336 14.6205L6.01857 20H2.89286L9.5323 12.4866L2.5 4H8.26286L12.4481 8.9233L17.1761 4ZM16.6741 18.3205H18.3425L7.39286 5.6125H5.61714L16.6741 18.3205Z" fill="#1D3C34"/>
                    </svg>
                  </div>
                </div>
                <div @click="shareOnWhatsApp" class="bg-thirdColor border-2 border-primary w-[45px] h-[45px] flex items-center justify-center rounded-full cursor-pointer">
                  <div>
                    <svg width="20" height="20" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M34.0089 5.8125C30.2679 2.0625 25.2857 0 19.9911 0C9.0625 0 0.169643 8.89286 0.169643 19.8214C0.169643 23.3125 1.08036 26.7232 2.8125 29.7321L0 40L10.5089 37.2411C13.4018 38.8214 16.6607 39.6518 19.9821 39.6518H19.9911C30.9107 39.6518 40 30.7589 40 19.8304C40 14.5357 37.75 9.5625 34.0089 5.8125ZM19.9911 36.3125C17.0268 36.3125 14.125 35.5179 11.5982 34.0179L11 33.6607L4.76786 35.2946L6.42857 29.2143L6.03571 28.5893C4.38393 25.9643 3.51786 22.9375 3.51786 19.8214C3.51786 10.7411 10.9107 3.34821 20 3.34821C24.4018 3.34821 28.5357 5.0625 31.6429 8.17857C34.75 11.2946 36.6607 15.4286 36.6518 19.8304C36.6518 28.9196 29.0714 36.3125 19.9911 36.3125ZM29.0268 23.9732C28.5357 23.7232 26.0982 22.5268 25.6429 22.3661C25.1875 22.1964 24.8571 22.1161 24.5268 22.6161C24.1964 23.1161 23.25 24.2232 22.9554 24.5625C22.6696 24.8929 22.375 24.9375 21.8839 24.6875C18.9732 23.2321 17.0625 22.0893 15.1429 18.7946C14.6339 17.9196 15.6518 17.9821 16.5982 16.0893C16.7589 15.7589 16.6786 15.4732 16.5536 15.2232C16.4286 14.9732 15.4375 12.5357 15.0268 11.5446C14.625 10.5804 14.2143 10.7143 13.9107 10.6964C13.625 10.6786 13.2946 10.6786 12.9643 10.6786C12.6339 10.6786 12.0982 10.8036 11.6429 11.2946C11.1875 11.7946 9.91071 12.9911 9.91071 15.4286C9.91071 17.8661 11.6875 20.2232 11.9286 20.5536C12.1786 20.8839 15.4196 25.8839 20.3929 28.0357C23.5357 29.3929 24.7679 29.5089 26.3393 29.2768C27.2946 29.1339 29.2679 28.0804 29.6786 26.9196C30.0893 25.7589 30.0893 24.7679 29.9643 24.5625C29.8482 24.3393 29.5179 24.2143 29.0268 23.9732Z" fill="#1D3C34"/>
                    </svg>
                  </div>
                </div>
                <div @click="copyCurrentLink" class="bg-thirdColor border-2 border-primary w-[45px] h-[45px] flex items-center justify-center rounded-full cursor-pointer">
                  <div>
                    <svg data-v-9c34c54e="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="icon" width="20px" height="20px" viewBox="0 0 256 256"><path fill="#1D3C34" d="M216 32H88a8 8 0 0 0-8 8v40H40a8 8 0 0 0-8 8v128a8 8 0 0 0 8 8h128a8 8 0 0 0 8-8v-40h40a8 8 0 0 0 8-8V40a8 8 0 0 0-8-8m-56 176H48V96h112Zm48-48h-32V88a8 8 0 0 0-8-8H96V48h112Z"></path></svg>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex flex-col sm:flex-row items-center gap-5 pb-6">
              <div>
                <div class="bg-white truncate w-[100px] h-[100px] p-2 rounded-full flex items-center justify-between border border-primary">
                  <img class="w-full" :src="product?.brand_logo">
                </div>
              </div>
              <div>
                <h2 class="text-primary font-bold leading-7 text-[29px] font-secondaryFont rtl:font-notoSans">{{ locale === 'en' ? product?.name : product?.name_ar }}</h2>
                <h2 class="text-orangeColor font-bold text-[22px] font-secondaryFont">
                  {{ locale === 'en' ? product?.excerpt : product?.excerpt_ar }}
                </h2>
              </div>
              <div
                v-if="product?.sale"
                class="uppercase w-fit"
              >
                <div class="py-1 px-3 bg-thirdColor rounded-t-[15px] text-[11px] font-primaryFont font-semibold text-primary text-nowrap rtl:font-notoSans rtl:font-bold">{{ locale === 'en' ? product?.sale?.title : product?.sale?.title_ar }}</div>
                <div class="bg-orangeColor rounded-b-[15px] px-3 py-1 text-thirdColor flex flex-col">
                  <span class="font-semibold text-[15px] text-nowrap rtl:font-notoSans">{{ locale === 'en' ? product?.sale?.percent : product?.sale?.percent_ar }}</span>
                  <span class="text-[12px] font-primaryFont font-medium line-through text-nowrap rtl:font-notoSans">{{ locale === 'en' ? product?.sale?.line_through : product?.sale?.line_through_ar }}</span>
                </div>
              </div>
            </div>
            
            <div v-html="locale === 'en' ? product.description : product.description_ar" class="font-thirdFont rtl:font-notoSans italic text-primary text-[14px] w-full"></div>
            <div
              class="flex gap-[5px] lg:gap-[6px] mt-6 flex-wrap pb-6"
            >
              <div
                v-for="(meta, index) in product?.metas ?? []"
                :key="index"
                class="
                  md:w-[32.5%]
                  w-[48%]
                "
                :class="{'hidden': !meta?.value}"
              >
                <UBadge
                  class="
                    flex
                    flex-col
                    items-start
                    justify-between
                    p-3
                    text-primary
                    bg-thirdColor
                  "
                  v-if="meta?.value"
                >
                  <div class="font-bold text-[13px] uppercase rtl:font-notoSans" v-if="meta?.Key" v-html="locale === 'en' ? meta?.Key : meta?.Key_ar"></div>
                  <div class="font-bold text-base uppercase rtl:font-primaryFont" v-if="meta?.value" v-html="meta?.value"></div>
                </UBadge>
              </div>
            </div>
  
            <div v-if="product?.stock" class="w-full flex flex-col sm:flex-row">
              <button
                class="
                  flex
                  uppercase
                  bg-transparent
                  text-primary
                  sm:w-[40%]
                  py-5
                  px-3
                  lg:px-5
                  items-center
                  justify-between
                  rounded-s-md
                  shadow-2xl
                  border
                  border-orangeColor"
              >
                <div class="flex items-center gap-2 lg:gap-4 w-full justify-around">
                  <span class="font-bold text-[40px] leading-3 text-orangeColor" @click="decreaseCounter">-</span>
                  <span class="font-bold text-[22px] leading-3 text-orangeColor">{{ counter >= 10 ? counter : '0' + counter }}</span>
                  <span class="font-bold text-[40px] leading-3 text-orangeColor" @click="incrementCounter">+</span>
                </div>
              </button>
              <UButton
                class="
                  flex
                  uppercase
                  bg-primary
                  text-thirdColor
                  sm:w-[60%]
                  py-5
                  px-3
                  lg:px-5
                  items-center
                  justify-between
                  shadow-2xl
                  font-thirdFont
                  bg-orangeColor
                  border
                  !rounded-s-none
                  border-orangeColor
                  hover:text-thirdColor
                  hover:bg-orangeColor
                  hover:border-1
                  hover:border-orangeColor
                  rtl:flex-row-reverse
                "
                :loading="cartLoading"
                @click="addToCarts(product)"
              >
                <div class="flex gap-2 items-center rtl:flex-row-reverse">
                  <ClientOnly>
                    <font-awesome-icon icon="fa-solid fa-cart-shopping" class="text-[20px]" />
                  </ClientOnly>
                  <div class="font-semibold font-secondaryFont text-sm lg:text-lg">{{ $t('ADD TO CART') }}</div>
                </div>
                <div class="font-bold font-primaryFont lg:text-2xl">
                  <span class="rtl:flex rtl:flex-row-reverse rtl:gap-1" v-if="selectedPrices[product.id]"><span class="rtl:font-notoSans">{{ $t('EGP') }} </span><span>{{ formatPrice(Number(selectedPrices[product.id])) }}</span></span>
                  <span class="rtl:flex rtl:flex-row-reverse rtl:gap-1" v-else><span class="rtl:font-notoSans">{{ $t('EGP') }} </span><span>{{ formatPrice(Number(product?.price)) }}</span></span>
                </div>
              </UButton>
            </div>
            <div v-else class="w-full flex flex-col sm:flex-row">
              <UButton
                class="
                  flex
                  uppercase
                  disabled:bg-white
                  text-primary
                  sm:w-[40%]
                  py-5
                  px-3
                  lg:px-5
                  items-center
                  justify-between
                  rounded-s-md
                  rounded-e-none
                  shadow-2xl
                  border
                  border-orangeColor"
                  disabled
              >
                <div class="flex items-center gap-2 lg:gap-4 w-full justify-around">
                  <span class="font-bold text-[40px] leading-3 text-orangeColor">-</span>
                  <span class="font-bold text-[22px] leading-3 text-orangeColor">{{ counter >= 10 ? counter : '0' + counter }}</span>
                  <span class="font-bold text-[40px] leading-3 text-orangeColor">+</span>
                </div>
              </UButton>
              <UButton
                class="
                  flex
                  uppercase
                  bg-primary
                  disabled:text-thirdColor
                  sm:w-[60%]
                  py-5
                  px-3
                  lg:px-5
                  items-center
                  justify-between
                  shadow-2xl
                  font-thirdFont
                  disabled:bg-orangeColor
                  border
                  !rounded-l-none
                  border-orangeColor
                  hover:text-thirdColor
                  hover:bg-orangeColor
                  hover:border-1
                  hover:border-orangeColor
                "
                disabled
              >
                <div class="flex gap-2 items-center">
                  <div class="font-bold text-lg font-secondaryFont rtl:font-notoSans">OUT OF STOCK</div>
                </div>
                <div class="font-bold lg:text-2xl font-primaryFont">
                  <span class="rtl:flex rtl:flex-row-reverse rtl:gap-1" v-if="selectedPrices[product.id]"><span class="rtl:font-notoSans">{{ $t('EGP') }} </span><span>{{ formatPrice(Number(selectedPrices[product.id])) }}</span></span>
                  <span class="rtl:flex rtl:flex-row-reverse rtl:gap-1" v-else><span class="rtl:font-notoSans">{{ $t('EGP') }} </span><span>{{ formatPrice(Number(product?.price)) }}</span></span>
                </div>
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="md:hidden w-full">
        <div class="w-full">
          <div class="w-full">
            <div class="flex gap-4 items-center">
              <div class="relative flex flex-col justify-between gap-5">
                <img class="w-[85px] h-[85px] rounded-full border border-primary" :src="product?.brand_logo" :alt="locale === 'en' ? product?.brand_name : product?.brand_name_ar" />
                <div class="small-stars">
                  <el-rate :colors="colors" v-model="reviews.overall_rate" size="large" allow-half disabled />
                </div>
              </div>
              <div>
                <h2 class="text-primary ltr:italic font-semibold text-[15px] font-primaryFont rtl:font-notoSans">{{ locale === 'en' ? product?.name : product?.name_ar }}</h2>
                <div class="text-orangeColor ltr:italic font-semibold text-sm font-primaryFont rtl:font-secondaryFont">{{ locale === 'en' ? product?.excerpt : product?.excerpt_ar }}</div>
                <div class="flex flex-col">
                  <div
                    class="flexgap-2 font-primaryFont italic text-xs text-primary"
                    v-for="metas in product.metas"
                    :class="{'hidden': metas.Key !== 'Load Index' && metas.Key !== 'Speed Index' && metas.Key !== 'ORIGIN'}"
                  >
                    <div class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary">
                      <div class="font-semibold rtl:font-notoSans">{{ locale === 'en' ? metas.Key : metas.Key_ar }}:</div>
                      <span class="font-bold">{{ metas.value }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-4 flex flex-col gap-3 w-full">
              <UButton
                v-if="product?.stock"
                class="
                  text-thirdColor
                  flex
                  flex-col
                  w-full
                  items-center
                  justify-between
                  font-primaryFont
                  font-bold
                  text-[15px]
                  py-2
                  bg-orangeColor
                  uppercase
                  border-2
                  border-orangeColor
                  transition-all
                  button-shadow
                  duration-500
                  ease-in-out
                  hover:text-thirdColor
                  hover:bg-primary
                  hover:border-2
                  hover:border-primary
                  hover:border-primary
                  hover:bg-primary
                "
                :loading="cartLoading"
                @click="addToCarts(product)"
              >
                <div class="flex items-center justify-between w-full" v-if="!cartLoading">
                  <ClientOnly>
                    <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                  </ClientOnly>
                  <div class="rtl:flex rtl:flex-row-reverse rtl:gap-1"><span class="rtl:font-notoSans">{{ $t('EGP') }}</span> <span>{{ formatPrice(Number(product?.price)) }}</span></div>
                </div>
              </UButton>
              <UButton
               v-else
                class="
                  text-thirdColor
                  flex
                  w-full
                  items-center
                  justify-between
                  font-primaryFont
                  font-bold
                  text-[15px]
                  py-2
                  disabled:bg-orangeColor
                  uppercase
                  border-2
                  disabled:border-orangeColor
                  transition-all
                  button-shadow
                  duration-500
                  ease-in-out
                  hover:text-thirdColor
                  hover:bg-primary
                  hover:border-2
                  hover:border-primary
                  hover:border-primary
                  hover:bg-primary
                "
                disabled
              >
                <div class="flex gap-2 items-center">
                  <div class="font-bold font-secondaryFont rtl:font-notoSans">{{ $t('OUT OF STOCK') }}</div>
                </div>
                <div class="font-bold text-lg font-primaryFont">
                  <span class="rtl:flex rtl:flex-row-reverse rtl:gap-1" v-if="selectedPrices[product.id]"><span class="rtl:font-notoSans">EGP</span> <span>{{ formatPrice(Number(selectedPrices[product.id])) }}</span></span>
                  <span class="rtl:flex rtl:flex-row-reverse rtl:gap-1" v-else><span class="rtl:font-notoSans">EGP</span> <span>{{ formatPrice(Number(product?.price)) }}</span></span>
                </div>
              </UButton>
            </div>
          </div>
        </div>
        <div class="rtl:font-notoSans mt-5 italic font-thirdFont text-primary text-[14px] w-full" v-html="locale === 'en' ? product.description : product.description_ar">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'
import { VueImageZoomer } from 'vue-image-zoomer'
import 'vue-image-zoomer/dist/style.css'

const route = useRoute()
const { locale, t } = useI18n()
const localePath = useLocalePath()

const productId = ref(Number(route.params.id))

const counter = ref(1)
const product: any = useState("product", () => [])

// State Management for Selections, Prices, Image, and MetaData
const selectedPrices = reactive<any>({})
const savedPrice = reactive<any>({})
const realPrice = ref<any>(product.value.price)
const selectedImage = ref<any>(null)

const incrementCounter = () => {
  counter.value += 1
  if (selectedPrices[productId.value]) {
    selectedPrices[productId.value] = savedPrice[productId.value] * counter.value
  } else {
    product.value.price = realPrice.value * counter.value
  }
}

const decreaseCounter = () => {
  if (counter.value !== 1) {
    counter.value -= 1
    if (selectedPrices[productId.value]) {
      selectedPrices[productId.value] = savedPrice[productId.value] * counter.value
    } else {
      product.value.price = realPrice.value * counter.value
    }
  }
}

// State Initialization
const cartItems: any = useState('cartItems', () => [])
const userData: any = useState('userData', () => [])
const cartLoading = ref(false) // Track loading state per product

const addToCarts = async (AddToCartItem: any) => {
  cartLoading.value = true
    // Call the addToCart function with proper typing
    const { cart } = await addToCart(
      AddToCartItem,
      Number(userData?.value?.id),
      counter,
      true
    )

    // Update cart state
    cartItems.value = cart.value
  cartLoading.value = false
}

// Product Review
const reviews: any = useState('reviews', () => [])
const colors = ref(['#FF671F', '#FF671F', '#FF671F', '#FF671F', '#FF671F']);

// 


const shareOnFacebook = () => {
  const url = window.location.href;
  const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
  window.open(facebookShareUrl, 'facebook-share-dialog', 'width=800,height=600');
};

const shareOnWhatsApp = () => {
  const url = window.location.href;
  const whatsappShareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(url)}`;
  window.open(whatsappShareUrl, '_blank');
};

const shareOnTwitter = () => {
  const url = window.location.href;
  const text = 'Sparefinders';
  const twitterShareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
  window.open(twitterShareUrl, '_blank');
};

const successPopUp = () => {
  ElNotification({
    // title: 'Create order',
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('Link copied to clipboard!')),
    // type: 'success',
    position: 'top-right',
  })
}

const copyCurrentLink = () => {
  const url = window.location.href;
  navigator.clipboard.writeText(url).then(() => {
    successPopUp();
  }).catch(err => {
    console.error('Failed to copy: ', err);
  });
};
</script>

<style>
.el-rate {
  --el-rate-void-color: #FF671F !important;
}
.small-stars {
  height: 18px;
}
.small-stars .el-rate  {
  gap: 0;
  height: 18px !important;
}
.small-stars .el-rate__item {
  width: 17px;
}
.small-stars .el-icon svg {
  width: 20px;
  height: 20px;
}
</style>
