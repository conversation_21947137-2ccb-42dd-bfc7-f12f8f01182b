<template>
  <div class="bg-secondary border-b border-primary">
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="py-5">
        <h2 class="text-primary font-thirdFont text-lg mb-3 font-bold">{{ $t('Select the Best') }} {{ categoryName }} {{ $t('for Your Needs from the Options Below') }}</h2>
        <div class="flex w-full justify-between flex-col md:flex-row gap-[20px] lg:gap-[80px]">
          <div class="flex flex-col md:flex-row w-full lg:w-4/5 gap-5 select-filter">
            <div
              class="w-full variations-filter relative flex justify-between"
              v-for="(item, index) in filterItems"
              :key="item?.attribute_slug"
              :class="{
                'variationUp' : selectedFilter[item?.attribute_slug]
              }"
            >
              <div
                class="
                  uppercase
                  absolute
                  text-primary
                  font-semibold
                  opacity-1
                  rtl:font-bold
                  font-base
                  italic
                  z-[4]
                  ltr:left-[15px]
                  rtl:right-[15px]
                  top-[13px]
                  transition-all
                  duration-100
                  ease-in-out"
                  :class="{
                    'placeholder-animation-with-bg' : selectedFilter[item?.attribute_slug]
                  }"
              >
                {{ locale === 'en' ? item?.name : item?.name_ar }}
              </div>
            <!-- <span class="absolute z-[2] flex items-center justify-center">{{ item.name }}</span> -->
              <el-select
                v-model="selectedFilter[item?.attribute_slug]"
                placeholder=" "
                clearable
                size="large"
                style="width: 100%"
                @change="onSelect"
                :loading="loading"
              >
                <el-option
                  :key="`all-${item?.attribute_slug}`"
                  label="All"
                  value=""
                />
                <el-option
                  v-for="option in item.terms"
                  :key="option.term_id"
                  :label="locale === 'en' ? option.name : option.name_ar"
                  :disabled="!option.status"
                  :value="`filter[${option.taxonomy}]=${option.slug}`"
                />
              </el-select>
            </div>

          </div>
          <div class="md:w-[22%]">
            <UButton
              class="
                flex
                flex-col
                w-full
                items-center
                justify-between
                font-bold
                text-lg
                py-2
                font-thirdFont
                h-[51px]
                text-thirdColor
                uppercase
                bg-primary
                border-2
                border-primary
                hover:bg-orangeColor
                hover:border-orangeColor
                transition-all
                duration-500
                ease-in-out"
                @click="goToCategory"
                >
              {{ $t('SEARCH') }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const categoryId: any = useState("categoryId", () => [])
const { items: filterItem } = await fetchHock(`newBranch/attributes/${Number(categoryId.value)}`)
const categoryName: any = useState("categoryName", () => '')

const { locale } = useI18n()
const localePath = useLocalePath()

const filterItems = ref<any>([])
const loading = ref(false)
const selectedFilter = ref<any>({})
const router = useRouter()


filterItems.value = filterItem.value.attributes

// Function to parse selectedFilter and extract key-value pairs
const parseSelectedFilter = () => {
  if (!selectedFilter.value || typeof selectedFilter.value !== 'object') {
    return []  // Return an empty array if selectedFilter.value is not an object
  }

  return Object.entries(selectedFilter.value)
    .map(([taxonomy, filterString]: [any, any]) => {
      if (!filterString) return null  // Check if filterString is valid
      const match = filterString.match(/\[(.+?)\]=(.+)/)
      return match ? { key: taxonomy, value: match[2] } : null
    })
    .filter((pair: any) => pair !== null)  // Filter out null matches
}

// Function to parse the query string and return a query object
const parseQueryString = (queryString: string) => {
  const queryObject: Record<string, string> = {}

  // Split the query string into key-value pairs
  queryString.split('&').forEach((pair) => {
    const [key, value] = pair.split('=')
    queryObject[key] = value
  })

  return queryObject
}


// Ensure that filterItems is initialized with status
const onSelect = async () => {
  loading.value = true
  const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')
  const { items: filter } = await fetchHock(`newBranch/attributes/${Number(categoryId.value)}?${queryString}`)
  filterItems.value = filter.value.attributes
  // transformAndApplyQueryParams(queryString, brand)
  loading.value = false
}

const goToCategory = () => {
  if (Object.keys(selectedFilter.value).length >= 1) {
    const filterPairs = parseSelectedFilter()
    const filterQuery = filterPairs
        .map(({ key, value }: any) => `${key}=${value}`)
        .join('&')
  
    // Parse the query string into a query object
    const queryParams = parseQueryString(filterQuery)
    
    // Navigate to the target route with the query parameters
    router.push({ path: localePath(`/category/${categoryId.value}`), query: queryParams })
  }
}
</script>
<style>
</style>
