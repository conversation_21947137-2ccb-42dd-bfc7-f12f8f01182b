<template>
  <div class="bg-secondary">
    <div class="px-[20px] max-w-[1280px] mx-auto hidden md:block">
      <div class="py-5">
        <h2 class="text-primary font-thirdFont text-lg mb-3 font-bold">{{ categoryDescription }}</h2>
        <div class="flex w-full justify-between flex-col md:flex-row gap-[20px] lg:gap-[50px]">
          <div class="flex flex-col md:flex-row w-full gap-5 select-filter">
            <div
              class="w-full variations-filter relative flex justify-between"
              v-for="(item, index) in filterData.menus"
              :key="item?.slug"
            >
              <div
                class="
                  uppercase
                  absolute
                  text-primary
                  font-semibold
                  opacity-1
                  font-base
                  italic
                  z-[4]
                  ltr:left-[15px]
                  rtl:right-[15px]
                  top-[13px]
                  transition-all
                  duration-100
                  ease-in-out"
                  :class="{
                    'placeholder-animation-with-bg' : selectedFilter[item?.slug]
                  }"
              >
                {{ locale === 'en' ? item?.name : item?.name_ar }}
              </div>




              <el-select
                v-model="selectedFilter[item?.slug]"
                placeholder=" "
                clearable
                size="large"
                style="width: 100%"
                @change="onSelectChange(item?.slug)"
                :loading="loadingCategory"
                :disabled="!isSelectEnabled(index)"
              >
                <template v-if="item.type === 'nested'">
                  <el-option-group
                    v-for="(group, index) in item.options"
                    :key="index"
                    :label="locale === 'en' ? group?.label : group?.label_ar"
                  >
                    <el-option
                      v-for="option in group.options"
                      :key="option.term_id"
                      :label="locale === 'en' ? option?.name : option?.name_ar"
                      :disabled="!option.status"
                      :value="`${item?.slug}=${option.term_id}`"
                    />
                  </el-option-group>
                </template>
                <template v-else>
                  <el-option
                    v-for="option in item.options"
                    :key="option.term_id"
                    :label="locale === 'en' ? option?.name : option?.name_ar"
                    :disabled="!option.status"
                    :value="`${item?.slug}=${option.term_id}`"
                  />
                </template>
              </el-select>
            </div>
          </div>

          <div class="flex gap-2">
            <UButton
              class="
                flex
                items-center
                justify-center
                font-extrabold
                text-xl
                py-1
                px-4
                font-thirdFont
                text-thirdColor
                bg-orangeColor
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                uppercase
                border-2
                transition-all
                duration-500
                ease-in-out"
                @click="resetButton"
            >
              {{ $t('RESET') }}
            </UButton>
            <div class="sort-button">
              <div class="sort-button-placeholder">
                {{ $t('SORT') }}
              </div>
              <el-select v-model="sortValue" placeholder=" " style="width: 96PX; position: relative; z-index:2">
                <el-option-group
                  v-for="group in options"
                  :key="group.label"
                  :label="group.label"
                >
                  <el-option
                    v-for="item in group.options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-option-group>
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </div>










    <div class="md:hidden">
      <div
        class="flex justify-between items-center"
        :class="{
          'border-b border-primary': mobFilter
        }"
      >
        <div
          class="font-secondaryFont text-[15px] text-thirdColor font-bold w-full text-center py-3"
            @click="mobFilter = !mobFilter"
            :class="{
              '!text-primary': mobFilter
            }"
        >
          <ClientOnly>
            <font-awesome-icon icon="fa-solid fa-filter" />
          </ClientOnly>
            {{ $t('Filter') }}
        </div>
        <div class="h-[30px] w-[1px] bg-thirdColor"></div>
        <div
          class="font-secondaryFont text-[15px] text-thirdColor font-bold w-full text-center py-3"
            @click="resetButton"
        >
          <ClientOnly>
            <font-awesome-icon icon="fa-solid fa-circle-xmark" />
          </ClientOnly>
          {{ $t('RESET') }}
        </div>
      </div>
      <transition name="slide-transition">
        <div v-if="mobFilter" class="overscroll-auto overflow-y-scroll h-[100vh]">
          <div class="overscroll-auto overflow-y-scroll h-[430px]">




            <div class="mobile-filter-collapse">
              <el-collapse v-model="activeNames" accordion>
                <!-- Brands -->
                <el-collapse-item name="BRANDS">
                <template #title>
                  <div class="flex justify-between items-center w-full">
                    <div class="uppercase font-bold font-secondaryFont text-base">{{ $t('BRANDS') }}</div>
                  </div>
                </template>
                  <ul>
                    <li
                      v-for="i in filterData.brands"
                      :key="i.brand_id"
                      class="italic uppercase font-semibold border-b border-primary mx-3 py-1 px-2 border-none-custom"
                      :class="{
                        'text-orangeColor' : brandId == i.brand_id
                      }">
                      <div v-if="i.status && !loadingMobile" @click="selectBrand(i.brand_id)" class="flex items-center justify-between w-full">
                        <div v-html="locale === 'en' ? i?.brand_name : i?.brand_name_ar"></div>
                        <svg v-if="brandId == i.brand_id" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M15 7.5C15 11.6421 11.6421 15 7.5 15C3.35785 15 0 11.6421 0 7.5C0 3.35785 3.35785 0 7.5 0C11.6421 0 15 3.35785 15 7.5ZM6.63248 11.4712L12.197 5.90667C12.3859 5.71772 12.3859 5.41134 12.197 5.22239L11.5127 4.5381C11.3238 4.34912 11.0174 4.34912 10.8284 4.5381L6.29032 9.07615L4.1716 6.95743C3.98265 6.76848 3.67627 6.76848 3.48729 6.95743L2.803 7.64171C2.61405 7.83067 2.61405 8.13705 2.803 8.326L5.94817 11.4712C6.13715 11.6601 6.4435 11.6601 6.63248 11.4712Z" fill="#FF671F"/>
                        </svg>
                      </div>
                      <div v-else class="opacity-50">
                        <div v-html="locale === 'en' ? i?.brand_name : i?.brand_name_ar"></div>
                      </div>
                    </li>
                  </ul>
                </el-collapse-item>













                <!-- Selects -->
                <el-collapse-item
                  v-for="(item, index) in filterData.menus"
                  :key="item?.slug"
                  :name="item.name"
                  :disabled="!isSelectEnabled(index)"
                  :class="{ 'disabled-collapse': !isSelectEnabled(index) }"
                >
                  <template #title>
                    <div class="flex justify-between items-center w-full">
                      <div
                        class="uppercase font-bold font-secondaryFont text-base"
                        :class="{ 'opacity-50': !isSelectEnabled(index) }"
                      >
                        {{ locale === 'en' ? item?.name : item?.name_ar }}
                      </div>
                    </div>
                  </template>
                  <ul>
                    <template v-if="item.type === 'nested'">
                      <li v-for="group in item.options" :key="group.label" class="group-label">
                        {{ locale === 'en' ? group?.label : group?.label_ar }}
                        <ul>
                          <li
                            v-for="option in group.options"
                            :key="option.term_id"
                            class="italic uppercase font-semibold border-b border-primary mx-3 py-1 px-2 border-none-custom"
                            :class="{
                              'text-orangeColor': selectedFilter[item?.slug]?.split('=')[1] === String(option.term_id)
                            }"
                          >
                            <div
                              v-if="option.status && !loadingMobile"
                              @click="selectMobile(`${item?.slug}=${option.term_id}`, item?.slug)"
                              class="flex items-center justify-between w-full"
                            >
                              <div v-html="locale === 'en' ? option?.name : option?.name_ar"></div>
                              <svg v-if="selectedFilter[item?.slug]?.split('=')[1] === String(option.term_id)" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 7.5C15 11.6421 11.6421 15 7.5 15C3.35785 15 0 11.6421 0 7.5C0 3.35785 3.35785 0 7.5 0C11.6421 0 15 3.35785 15 7.5ZM6.63248 11.4712L12.197 5.90667C12.3859 5.71772 12.3859 5.41134 12.197 5.22239L11.5127 4.5381C11.3238 4.34912 11.0174 4.34912 10.8284 4.5381L6.29032 9.07615L4.1716 6.95743C3.98265 6.76848 3.67627 6.76848 3.48729 6.95743L2.803 7.64171C2.61405 7.83067 2.61405 8.13705 2.803 8.326L5.94817 11.4712C6.13715 11.6601 6.4435 11.6601 6.63248 11.4712Z" fill="#FF671F"/>
                              </svg>
                            </div>
                            <div v-else class="opacity-50">
                              <div v-html="locale === 'en' ? option?.name : option?.name_ar"></div>
                            </div>
                          </li>
                        </ul>
                      </li>
                    </template>
                    <template v-else>
                      <li
                        v-for="option in item.options"
                        :key="option.term_id"
                        class="italic uppercase font-semibold border-b border-primary mx-3 py-1 px-2 border-none-custom"
                        :class="{
                          'text-orangeColor': selectedFilter[item?.slug]?.split('=')[1] === String(option.term_id)
                        }"
                      >
                        <div
                          v-if="option.status && !loadingMobile"
                          @click="selectMobile(`${item?.slug}=${option.term_id}`, item?.slug)"
                          class="flex items-center justify-between w-full"
                        >
                          <div v-html="locale === 'en' ? option?.name : option?.name_ar"></div>
                          <svg v-if="selectedFilter[item?.slug]?.split('=')[1] === String(option.term_id)" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15 7.5C15 11.6421 11.6421 15 7.5 15C3.35785 15 0 11.6421 0 7.5C0 3.35785 3.35785 0 7.5 0C11.6421 0 15 3.35785 15 7.5ZM6.63248 11.4712L12.197 5.90667C12.3859 5.71772 12.3859 5.41134 12.197 5.22239L11.5127 4.5381C11.3238 4.34912 11.0174 4.34912 10.8284 4.5381L6.29032 9.07615L4.1716 6.95743C3.98265 6.76848 3.67627 6.76848 3.48729 6.95743L2.803 7.64171C2.61405 7.83067 2.61405 8.13705 2.803 8.326L5.94817 11.4712C6.13715 11.6601 6.4435 11.6601 6.63248 11.4712Z" fill="#FF671F"/>
                          </svg>
                        </div>
                        <div v-else class="opacity-50">
                          <div v-html="locale === 'en' ? option?.name : option?.name_ar"></div>
                        </div>
                      </li>
                    </template>
                  </ul>
                </el-collapse-item>
              </el-collapse>
            </div>




            <UButton
              class="
                flex
                items-center
                justify-center
                font-bold
                text-[15px]
                mx-auto
                mt-14
                w-[80%]
                py-2
                rounded-full
                font-thirdFont
                text-thirdColor
                bg-primary
                border-primary
                hover:bg-primary
                hover:border-primary
                uppercase
                transition-all
                duration-500
                ease-in-out"
                @click="selectedMobileDone"
            >
              {{ $t('SUBMIT FILTER') }}
            </UButton>
          </div>

        </div>
      </transition>
    </div>
  </div>
</template>
<script setup lang="ts">
const emit = defineEmits(['onSelect', 'onSelectForMob'])

const categoryDescription: any = useState("categoryDescription", () => '')

const { t, locale } = useI18n()

const filterData: any = useState('filterData', () => {})
const selectedFilter: any = useState('selectedFilter', () => {})
const brandId =  useState<any>('brandId', () => '')
const loadingCategory: any = useState<boolean>('loadingCategory', () => false)
const page: any = useState("page", () => [])
const perPage: any = useState("perPage", () => [])
const currentNumber: any = useState("currentNumber", () => 1)
const sortValue: any = useState("sortValue", () => '')

const onSelectChange = (attributeSlug: string) => {
  if (!selectedFilter.value[attributeSlug]) {
    brandId.value = '';
  }

  const currentIndex = filterData.value.menus.findIndex((item: MenuItem) => item.slug === attributeSlug);

  // Clear all subsequent selections
  filterData.value.menus.forEach((item: MenuItem, index: number) => {
    if (index > currentIndex) {
      selectedFilter.value[item.slug] = null;
    }
  });

  emit('onSelect');
};

const options = [
  {
    label: t('SORT BY PRICE'),
    options: [
      {
        value: 'ASC',
        label: t('HIGH TO LOW'),
      },
      {
        value: 'DESC',
        label: t('LOW TO HIGH'),
      },
    ],
  },
]

const resetButton = () => {
  // Clear all selected filters
  selectedFilter.value = {}
  brandId.value = ''
  perPage.value = 8
  page.value = 1
  sortValue.value = ''
  currentNumber.value = 1
  // Emit an event to fetch filtered items
  emit('onSelect')
}

// Mobile Filter Logic
const mobFilter = ref(false)
const activeNames = ref(['1'])

watch(mobFilter, (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
})

// Clean up on component unmount
onUnmounted(() => {
  document.body.style.overflow = '';
})

const loadingMobile: any = useState<boolean>('loadingMobile', () => false)

watch(sortValue, (newValue) => {
  if (newValue) {
    emit('onSelect')
  }
})

const selectBrand = (item: string) => {
  brandId.value = item
  emit('onSelectForMob')
}

const selectMobile = (item: any, value: any) => {
  selectedFilter.value[value] = item
  emit('onSelectForMob')
}

const selectedMobileDone = () => {
  mobFilter.value = false
  emit('onSelect')
}

// Define interfaces for better type safety
interface MenuItem {
  slug: string;
  name: string;
  options: any[];
  type?: string;
  [key: string]: any;
}

// Add this computed property to check if a select should be enabled
const isSelectEnabled = (index: number): boolean => {
  if (index === 0) return true;

  const previousSelects = filterData.value.menus.slice(0, index);
  return previousSelects.every((item: MenuItem) => {
    // Check if the filter is selected either directly or from URL parameters
    return selectedFilter.value[item.slug] != null;
  });
};

</script>
<style>
/* Header */
.mobile-filter-collapse {
  background-color: #4B9560 !important;
}
.mobile-filter-collapse .el-collapse {
  border-color: #4B9560 !important;
}
.mobile-filter-collapse .el-collapse .el-collapse-item__header {
  border-color: #1D3C34 !important;
  background-color: #4B9560 !important;
  padding: 0 20px !important;
}
.mobile-filter-collapse .el-collapse .el-collapse-item__header.is-active {
  background-color: #E9EAC8 !important;
}
.mobile-filter-collapse .el-collapse-item__arrow {
  color: #1D3C34 !important;
  font-size: 18px !important;
}
/* Content */
.mobile-filter-collapse .el-collapse-item__content {
  background-color: #E9EAC8 !important;
}
.mobile-filter-collapse .el-collapse-item__content ul {
  margin: 0 35px;
  max-height: 175px;
  overflow-y: scroll;
}
.sort-button {
  position: relative;
}
.sort-button-placeholder {
  color: #E9EAC8;
  font-size: 20px;
  position: absolute;
  z-index: 99;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  background-color: #1D3C34 !important;
  font-weight: 800;
  border-radius: 6px;
}
.sort-button .el-select__wrapper {
  height: 50px;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background-color: transparent !important;
}
.sort-button .el-select__selected-item.el-select__placeholder {
  color: #E9EAC8 !important;
  font-size: 20px !important;
  text-align: center !important;
  display: none;
}
.sort-button .el-select__suffix {
  display: none;
}
.el-select-group__title {
  padding: 5px 0 !important;
  border-bottom: 1px solid #1D3C34 !important;
  border-top: 1px solid #1D3C34 !important;
  font-family: "Montserrat", sans-serif !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #1D3C34;
  opacity: 0.5;
  font-style: italic;
}
.el-select-group__wrap:not(:last-of-type):after {
  background: transparent;
}
.el-select-group__wrap:not(:last-of-type) {
  padding-bottom: 0 !important;
}
.el-select-group {
  padding: 0;
}
.el-select-group li {
  border-bottom: 1px solid #1D3C34 !important;
}
.el-select-group {
  padding: 0 10px !important;
}
.el-select-group .el-select-dropdown__item:last-child {
  border-bottom: 1px solid #1D3C34 !important;
}
.el-select-group .el-select-dropdown__item:last-child {
  border-bottom: none !important;
}
.el-select-dropdown__item.is-selected {
  color: #1D3C34;
}
.group-label {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #1D3C34;
  opacity: 0.9;
  padding: 10px 0;
  margin: 0 15px;
  border-bottom: 1px solid #1D3C34;
}
.disabled-collapse .el-collapse-item__header {
  cursor: not-allowed !important;
  opacity: 0.5;
}
.disabled-collapse .el-collapse-item__arrow {
  display: none;
}
</style>