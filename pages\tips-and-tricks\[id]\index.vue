<template>
  <div class="mb-20 relative">
    <div class="md:hidden sticky z-[55] rtl:font-notoSans top-0 left-0 w-full font-secondaryFont uppercase text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
      {{ $t('Discover tips & tricks') }}
    </div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="bg-cartColor rounded-[12px] hidden md:flex justify-between items-center flex-col-reverse md:flex-row gap-5 md:gap-0 mt-10 p-8">
        <div class="text-black text-xl font-secondaryFont md:w-[60%] rtl:font-notoSans">
          {{ $t("Check out our blog for more tips and tricks, and don't hesitate to leave a review. Also, visit our social media for more updates!") }}
        </div>
        <div class="flex gap-3 items-center">
          <a v-for="(item, index) in footer?.bottom_footer?.socia_media || []"
             :key="index"
             :href="item?.url"
             target="_blank"
             class="bg-orangeColor w-[45px] h-[45px] flex items-center justify-center rounded-full cursor-pointer">
            <div>
              <img :src="item?.icon" :alt="item?.platform || 'Social media'" class="w-[25px] h-[25px]" />
            </div>
          </a>
        </div>
      </div>
      <div class="md:flex md:justify-between mt-5 md:mt-10">
        <div class="md:w-[67%]">
          <img class="mb-5 md:mb-14 rounded-[30px] truncate border border-primary"
               :src="currentPost?.custom_large_crop || blog3"
               :alt="currentPost?.title?.rendered || 'Blog post image'" />
          <div class="text-primary font-bold font-secondaryFont font-base md:text-3xl uppercase rtl:font-notoSans">
            {{ locale === 'en' ? currentPost?.title?.rendered : currentPost?.title_ar  }}
          </div>
          <div class="md:mt-14 mt-8 font-primaryFont text-[13px] md:text-xl font-medium text-primary md:tracking-[0.1rem] md:leading-9 rtl:font-notoSans">
            <div class="mb-8" v-if="currentPost?.excerpt?.rendered" v-html="locale === 'en' ? currentPost?.excerpt?.rendered : currentPost?.excerpt?.rendered_ar"></div>
            <div class="blogs-single" v-if="currentPost?.content?.rendered" v-html="locale === 'en' ? currentPost?.content?.rendered : currentPost?.content_ar"></div>
          </div>
        </div>
        <div class="md:w-[30%] hidden md:block">
          <div class="text-secondary font-secondaryFont text-xl font-bold uppercase mb-5 rtl:font-notoSans">{{ $t('Share with friends') }}</div>
          <div class="bg-thirdColor p-4 rounded-[20px]">
            <div class="flex gap-3 items-center">
              <div @click="shareOnFacebook" class="bg-primary w-[35px] h-[35px] flex items-center justify-center rounded-full">
                <svg width="12" height="22" viewBox="0 0 12 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11.02 12.2773L11.6192 8.57738H7.87257V6.17635C7.87257 5.16412 8.39594 4.17743 10.0739 4.17743H11.7772V1.02731C11.7772 1.02731 10.2316 0.777344 8.75371 0.777344C5.66824 0.777344 3.65143 2.54946 3.65143 5.75748V8.57738H0.22168V12.2773H3.65143V21.2218H7.87257V12.2773H11.02Z" fill="#E9EAC8"/>
                </svg>
              </div>
              <div @click="shareOnTwitter" class="bg-primary w-[35px] h-[35px] flex items-center justify-center rounded-full">
                <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.1761 4H20.3037L14.0516 11.0948L21.5 20H15.8588L11.2336 14.6205L6.01857 20H2.89286L9.5323 12.4866L2.5 4H8.26286L12.4481 8.9233L17.1761 4ZM16.6741 18.3205H18.3425L7.39286 5.6125H5.61714L16.6741 18.3205Z" fill="#E9EAC8"/>
                </svg>
              </div>
              <div @click="shareOnWhatsApp" class="bg-primary w-[35px] h-[35px] flex items-center justify-center rounded-full">
                <svg width="20" height="20" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M34.0089 5.8125C30.2679 2.0625 25.2857 0 19.9911 0C9.0625 0 0.169643 8.89286 0.169643 19.8214C0.169643 23.3125 1.08036 26.7232 2.8125 29.7321L0 40L10.5089 37.2411C13.4018 38.8214 16.6607 39.6518 19.9821 39.6518H19.9911C30.9107 39.6518 40 30.7589 40 19.8304C40 14.5357 37.75 9.5625 34.0089 5.8125ZM19.9911 36.3125C17.0268 36.3125 14.125 35.5179 11.5982 34.0179L11 33.6607L4.76786 35.2946L6.42857 29.2143L6.03571 28.5893C4.38393 25.9643 3.51786 22.9375 3.51786 19.8214C3.51786 10.7411 10.9107 3.34821 20 3.34821C24.4018 3.34821 28.5357 5.0625 31.6429 8.17857C34.75 11.2946 36.6607 15.4286 36.6518 19.8304C36.6518 28.9196 29.0714 36.3125 19.9911 36.3125ZM29.0268 23.9732C28.5357 23.7232 26.0982 22.5268 25.6429 22.3661C25.1875 22.1964 24.8571 22.1161 24.5268 22.6161C24.1964 23.1161 23.25 24.2232 22.9554 24.5625C22.6696 24.8929 22.375 24.9375 21.8839 24.6875C18.9732 23.2321 17.0625 22.0893 15.1429 18.7946C14.6339 17.9196 15.6518 17.9821 16.5982 16.0893C16.7589 15.7589 16.6786 15.4732 16.5536 15.2232C16.4286 14.9732 15.4375 12.5357 15.0268 11.5446C14.625 10.5804 14.2143 10.7143 13.9107 10.6964C13.625 10.6786 13.2946 10.6786 12.9643 10.6786C12.6339 10.6786 12.0982 10.8036 11.6429 11.2946C11.1875 11.7946 9.91071 12.9911 9.91071 15.4286C9.91071 17.8661 11.6875 20.2232 11.9286 20.5536C12.1786 20.8839 15.4196 25.8839 20.3929 28.0357C23.5357 29.3929 24.7679 29.5089 26.3393 29.2768C27.2946 29.1339 29.2679 28.0804 29.6786 26.9196C30.0893 25.7589 30.0893 24.7679 29.9643 24.5625C29.8482 24.3393 29.5179 24.2143 29.0268 23.9732Z" fill="#E9EAC8"/>
                </svg>
              </div>
              <div @click="copyCurrentLink" class="bg-primary w-[35px] h-[35px] flex items-center justify-center rounded-full">
                <svg data-v-9c34c54e="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="icon" width="22px" height="22px" viewBox="0 0 256 256"><path fill="#E9EAC8" d="M216 32H88a8 8 0 0 0-8 8v40H40a8 8 0 0 0-8 8v128a8 8 0 0 0 8 8h128a8 8 0 0 0 8-8v-40h40a8 8 0 0 0 8-8V40a8 8 0 0 0-8-8m-56 176H48V96h112Zm48-48h-32V88a8 8 0 0 0-8-8H96V48h112Z"></path></svg>
              </div>
            </div>
          </div>
          <div class="text-secondary font-secondaryFont text-xl font-bold uppercase mt-10 mb-5 hidden md:block rtl:font-notoSans">{{ $t('Related articles') }}</div>
          <div class="hidden md:block" v-for="(relatedPost, index) in blogPosts" :key="relatedPost?.id || index">
            <NuxtLink :to="localePath(`/tips-and-tricks/${relatedPost?.id}`)">
              <div class="relative rounded-[30px] border border-primary truncate mb-5">
                <img class="w-full"
                     :src="relatedPost?.custom_large_crop || blog3"
                     :alt="relatedPost?.title?.rendered || 'Related article'" />
                <div class="bg-primary rounded-t rounded-[30px]">
                  <div class="py-8 px-8">
                    <div class="text-thirdColor italic text-xl font-semibold font-primaryFont truncate rtl:font-notoSans">
                      {{ locale === 'en' ? relatedPost?.title?.rendered : relatedPost?.title_ar }}
                    </div>
                    <div class="text-wrap text-thirdColor mt-4 font-thirdFont text-sm blogs-text-limit rtl:font-notoSans"
                         v-if="relatedPost?.excerpt?.rendered"
                         v-html="locale === 'en' ? relatedPost?.excerpt?.rendered : relatedPost?.content_ar">
                    </div>
                  </div>
                </div>
              </div>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-10 md:hidden mb-[10rem]">
      <div class="md:hidden rtl:font-notoSans sticky z-[55] top-0 left-0 w-full font-secondaryFont uppercase text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
        {{ $t('Related articles') }}
      </div>
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="mt-5 md:mt-0" v-for="(relatedPost, index) in blogPosts" :key="relatedPost?.id || index">
          <NuxtLink :to="localePath(`/tips-and-tricks/${relatedPost?.id}`)">
            <div class="relative rounded-[30px] border border-primary truncate mb-5">
              <img class="w-full"
                   :src="relatedPost?.custom_large_crop || blog3"
                   :alt="relatedPost?.title?.rendered || 'Related article'" />
              <div class="bg-primary rounded-t rounded-[30px]">
                <div class="py-8 px-8">
                  <div class="text-thirdColor italic text-xl font-semibold font-primaryFont truncate rtl:font-notoSans">
                    {{ locale === 'en' ? relatedPost?.title?.rendered : relatedPost?.title_ar }}
                  </div>
                  <div class="text-wrap text-thirdColor mt-4 font-thirdFont text-sm blogs-text-limit rtl:font-notoSans"
                       v-if="relatedPost?.excerpt?.rendered"
                       v-html="locale === 'en' ? relatedPost?.excerpt?.rendered : relatedPost?.content_ar">
                  </div>
                </div>
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>
    </div>

    <div class="fixed bottom-0 z-[100] w-full mb-[59px] md:hidden">
      <div class="bg-thirdColor p-4">
        <div class="text-primary font-secondaryFont text-[12px] text-center uppercase mb-5 rtl:font-notoSans">{{ $t('Share The Article with friends') }}</div>
        <div class="flex gap-3 items-center justify-center">
          <button @click="copyCurrentLink" class="bg-orangeColor box-shadow w-[35px] h-[35px] flex items-center justify-center rounded-full">
            <svg data-v-9c34c54e="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="icon" width="22px" height="22px" viewBox="0 0 256 256">
              <path fill="#E9EAC8" d="M216 32H88a8 8 0 0 0-8 8v40H40a8 8 0 0 0-8 8v128a8 8 0 0 0 8 8h128a8 8 0 0 0 8-8v-40h40a8 8 0 0 0 8-8V40a8 8 0 0 0-8-8m-56 176H48V96h112Zm48-48h-32V88a8 8 0 0 0-8-8H96V48h112Z"></path>
            </svg>
          </button>
          <button @click="shareOnFacebook" class="bg-orangeColor box-shadow w-[35px] h-[35px] flex items-center justify-center rounded-full">
            <svg width="12" height="22" viewBox="0 0 12 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.02 12.2773L11.6192 8.57738H7.87257V6.17635C7.87257 5.16412 8.39594 4.17743 10.0739 4.17743H11.7772V1.02731C11.7772 1.02731 10.2316 0.777344 8.75371 0.777344C5.66824 0.777344 3.65143 2.54946 3.65143 5.75748V8.57738H0.22168V12.2773H3.65143V21.2218H7.87257V12.2773H11.02Z" fill="#E9EAC8"/>
            </svg>
          </button>
          <button @click="shareOnTwitter" class="bg-orangeColor box-shadow w-[35px] h-[35px] flex items-center justify-center rounded-full">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.1761 4H20.3037L14.0516 11.0948L21.5 20H15.8588L11.2336 14.6205L6.01857 20H2.89286L9.5323 12.4866L2.5 4H8.26286L12.4481 8.9233L17.1761 4ZM16.6741 18.3205H18.3425L7.39286 5.6125H5.61714L16.6741 18.3205Z" fill="#E9EAC8"/>
            </svg>
          </button>
          <button @click="shareOnWhatsApp" class="bg-orangeColor box-shadow w-[35px] h-[35px] flex items-center justify-center rounded-full">
            <svg width="20" height="20" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M34.0089 5.8125C30.2679 2.0625 25.2857 0 19.9911 0C9.0625 0 0.169643 8.89286 0.169643 19.8214C0.169643 23.3125 1.08036 26.7232 2.8125 29.7321L0 40L10.5089 37.2411C13.4018 38.8214 16.6607 39.6518 19.9821 39.6518H19.9911C30.9107 39.6518 40 30.7589 40 19.8304C40 14.5357 37.75 9.5625 34.0089 5.8125ZM19.9911 36.3125C17.0268 36.3125 14.125 35.5179 11.5982 34.0179L11 33.6607L4.76786 35.2946L6.42857 29.2143L6.03571 28.5893C4.38393 25.9643 3.51786 22.9375 3.51786 19.8214C3.51786 10.7411 10.9107 3.34821 20 3.34821C24.4018 3.34821 28.5357 5.0625 31.6429 8.17857C34.75 11.2946 36.6607 15.4286 36.6518 19.8304C36.6518 28.9196 29.0714 36.3125 19.9911 36.3125ZM29.0268 23.9732C28.5357 23.7232 26.0982 22.5268 25.6429 22.3661C25.1875 22.1964 24.8571 22.1161 24.5268 22.6161C24.1964 23.1161 23.25 24.2232 22.9554 24.5625C22.6696 24.8929 22.375 24.9375 21.8839 24.6875C18.9732 23.2321 17.0625 22.0893 15.1429 18.7946C14.6339 17.9196 15.6518 17.9821 16.5982 16.0893C16.7589 15.7589 16.6786 15.4732 16.5536 15.2232C16.4286 14.9732 15.4375 12.5357 15.0268 11.5446C14.625 10.5804 14.2143 10.7143 13.9107 10.6964C13.625 10.6786 13.2946 10.6786 12.9643 10.6786C12.6339 10.6786 12.0982 10.8036 11.6429 11.2946C11.1875 11.7946 9.91071 12.9911 9.91071 15.4286C9.91071 17.8661 11.6875 20.2232 11.9286 20.5536C12.1786 20.8839 15.4196 25.8839 20.3929 28.0357C23.5357 29.3929 24.7679 29.5089 26.3393 29.2768C27.2946 29.1339 29.2679 28.0804 29.6786 26.9196C30.0893 25.7589 30.0893 24.7679 29.9643 24.5625C29.8482 24.3393 29.5179 24.2143 29.0268 23.9732Z" fill="#E9EAC8"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import blog3 from '~/assets/images/home/<USER>/3.png'
import { ElNotification } from 'element-plus'
import type { Footer } from '~/types/footer'

/**
 * Blog post interface that matches the WordPress API response structure
 */
interface BlogPost {
  id: number;
  title_ar?: string;
  content_ar?: string;
  excerpt_ar?: string;
  title: {
    rendered?: string;
    rendered_ar?: string;
  };
  excerpt: {
    rendered?: string;
    rendered_ar?: string;
  };
  content: {
    rendered?: string;
    rendered_ar?: string;
  };
  custom_large_crop?: string;
  link?: string;
  date?: string;
}

const route = useRoute()
const footer = useState<Footer>("footer", () => ({} as Footer))
const { locale, t } = useI18n()
const localePath = useLocalePath()
// Fetch related blog posts, excluding the current one
const { items } = await fetchHock(`newBranch/blogs?page=1&per_page=2&exclude=${Number(route.params.id)}`)

// Fetch the current blog post
const { items: item } = await fetchHock(`newBranch/blogs/${Number(route.params.id)}`)

// Type assertion for better type safety
const blogPosts = items?.value?.data as BlogPost[] || []
const currentPost = item?.value as BlogPost || {}

/**
 * Share the current page on Facebook
 */
const shareOnFacebook = (): void => {
  const url = window.location.href;
  const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
  window.open(facebookShareUrl, 'facebook-share-dialog', 'width=800,height=600');
};

/**
 * Share the current page on WhatsApp
 */
const shareOnWhatsApp = (): void => {
  const url = window.location.href;
  const whatsappShareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(url)}`;
  window.open(whatsappShareUrl, '_blank');
};

/**
 * Share the current page on Twitter
 */
const shareOnTwitter = (): void => {
  const url = window.location.href;
  const text = 'Sparefinders - Tips & Tricks';
  const twitterShareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
  window.open(twitterShareUrl, '_blank');
};

/**
 * Show a success notification when the link is copied
 */
const successPopUp = (): void => {
  ElNotification({
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('Link copied to clipboard!')),
    position: 'top-right',
  });
};

/**
 * Copy the current page URL to clipboard
 */
const copyCurrentLink = (): void => {
  const url = window.location.href;
  navigator.clipboard.writeText(url)
    .then(() => {
      successPopUp();
    })
    .catch(err => {
      console.error('Failed to copy link to clipboard:', err);
    });
};
</script>
<style>
.blogs-single p {
  margin-top: 20px;
}
.blogs-single ul {
  list-style: auto;
  margin-inline-start: 60px;
}

</style>
<style scoped>
.blogs-text-limit {
   overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 7; /* number of lines to show */
           line-clamp: 7;
   -webkit-box-orient: vertical;
}
</style>