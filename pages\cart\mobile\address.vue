<template>
  <div class="relative">
    <div class="mt-8 px-[30px] mb-[59px]">
      <div class="flex justify-between">
        <div class="font-primaryFont font-bold text-primary text-lg rtl:font-notoSans">{{ $t('Shipping information') }}</div>
        <div v-if="shippingData?.shipping?.first_name" class="cursor-pointer" @click="enableForm">
          <ClientOnly>
            <font-awesome-icon class="text-primary text-[25px]" icon="fa-solid fa-pen-to-square" />
          </ClientOnly>
        </div>
      </div>
      <div class="mt-5" v-if="!formStatus">
        <!-- Address selection dropdown -->
        <div v-if="additionalAddress && additionalAddress.length > 0" class="mb-4">
          <div class="custom-text-input relative variations-filter">
            <div class="information-mob">
              <div class="uppercase absolute rtl:font-notoSans text-primary font-primaryFont font-semibold opacity-1 font-base z-[4] ltr:left-[22px] rtl:right-[22px] top-[13px] transition-all duration-100 ease-in-out"
                :class="{ 'placeholder-animation-mob !opacity-100': selectedAddress || selectedAddress === 0 }">
                {{ $t('Select Address') }}
              </div>
              <el-select v-model="selectedAddress" style="width: 100%" placeholder=" " @change="handleAddressSelect">
                <el-option value="default" label="Main Address" />
                <el-option v-for="(address, index) in additionalAddress" 
                  :key="index"
                  :label="locale === 'en' ? address.ship_address_1 : address.ship_address_1_ar"
                  :value="index" />
              </el-select>
            </div>
          </div>
        </div>
        <!-- Form -->
        <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
          <div class="w-full mx-auto flex flex-col gap-4">
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="address_1">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.address_1"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    rtl:font-notoSans
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.address_1,
                    }"
                  >
                    {{ $t('Title') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="first_name">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.first_name"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    rtl:font-notoSans
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.first_name,
                    }"
                  >
                    {{ $t('First Name') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="last_name">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.last_name"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    rtl:font-notoSans
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.last_name,
                    }"
                  >
                    {{ $t('Last Name') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="phone">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.phone"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    rtl:font-notoSans
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.phone,
                    }"
                  >
                    {{ $t('Phone') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="email">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.email"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    rtl:font-notoSans
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.email,
                    }"
                  >
                    {{ $t('Email') }}
                </span>
                <div v-if="errors" class="mb-1 text-red-700 font-primaryFont rtl:font-notoSans">{{ $t('Email Is Already Used Before') }}</div>
              </UFormGroup>
            </div>
            
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="address_2">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.address_2"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    rtl:font-notoSans
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.address_2,
                    }"
                  >
                    {{ $t('Address') }}
                </span>
              </UFormGroup>
            </div>

            <div class="custom-text-input relative custom-text-input-bg-third variations-filter">
              <UFormGroup name="city">
                <div class="information-mob">
                  <div
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-primaryFont
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      rtl:font-notoSans
                      top-[13px]
                      transition-all
                      duration-100
                      ease-in-out
                      focus:placeholder-animation-mob"
                      :class="{
                        'placeholder-animation-mob !opacity-100' : state.city,
                      }"
                    >
                      {{ $t('City') }}
                    </div>
                  <el-select
                    v-model="state.city"
                    style="width: 100%"
                    placeholder=" "
                  >
                    <el-option
                      v-for="item in cities.response"
                      :key="item.name"
                      :label="locale === 'en' ? item?.name : item?.name_ar"
                      :value="item.name"
                    />
                  </el-select>
                </div>
              </UFormGroup>
            </div>
          </div>
          <div class="text-center mt-6">
            <UButton
              type="submit"
              class="
                text-thirdColor
                font-bold
                font-thirdFont
                rounded-full
                text-[15px]
                uppercase
                border
                border-primary
                bg-primary
                px-20
                py-2
                mt-2
                shadow-unset
                hover:scale-[1.1]
                ease-in-out
                duration-500
              "
              variant="solid"
              v-if="!formStatus"
              :loading="loading"
            >
              <div v-if="!loading">{{ $t('SUBMIT') }}</div>
            </UButton>
          </div>
        </UForm>
      </div>
      <div class="mt-5" v-else>
        <div class="relative">
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.address_1"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('Title') }}
          </span>
        </div>
        <div class="relative mt-4">
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('first name') }}
          </span>
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.first_name"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
        </div>
        <div class="relative mt-4">
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('last name') }}
          </span>
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.last_name"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
        </div>
        <div class="relative mt-4">
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('phone') }}
          </span>
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.phone"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
        </div>
        <div class="relative mt-4">
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('email') }}
          </span>
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.email"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
        </div>
        <div class="relative mt-4">
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('address') }}
          </span>
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.address_2"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
        </div>
        <div class="relative mt-4">
          <span
            class="
              uppercase
              placeholder-animation-mob
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              text-[14px]
              z-[4]
              ltr:left-[22px]
              rtl:right-[22px]
              rtl:font-notoSans
              top-[13px]
              transition-all
              duration-100
              ease-in-out
              "
            >
            {{ $t('city') }}
          </span>
          <input
            class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
            v-model="item.city"
            :disabled="shippingData?.shipping?.first_name"
            placeholder=" "
          />
        </div>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123]">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans flex justify-center gap-1 items-center">
        <span class="rtl:mt-0.5">{{ $t('YOUR CART') }} ({{ totalItemss || '0' }})</span> |
        <span class="rtl:flex rtl:gap-2 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span>
        </span>
      </div>
      <div class="flex justify-center">
        <UButton
          v-if="!shippingData?.shipping?.address_1"
          class="rounded-full flex items-center justify-center font-bold text-[15px] rtl:font-notoSans w-full py-2 font-thirdFont text-thirdColor uppercase bg-orangeColor border-2 disabled:bg-orangeColor border-orangeColor hover:bg-orangeColor hover:border-orangeColor transition-all duration-500 ease-in-out"
          :disabled="true"
        >
          {{ $t('PLACE ORDER') }}
        </UButton>
        <UButton
          v-else
          class="rounded-full flex items-center justify-center font-bold text-[15px] rtl:font-notoSans w-full py-2 font-thirdFont text-thirdColor uppercase bg-orangeColor border-2 disabled:bg-black border-orangeColor hover:bg-orangeColor hover:border-orangeColor transition-all duration-500 ease-in-out"
          :disabled="!formStatus"
          :to="localePath('/cart/mobile/payment')"
        >
          {{ $t('PLACE ORDER') }}
        </UButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const { locale, t } = useI18n()
const localePath = useLocalePath()

const cartItems: any = useState("cartItems", () => {})

const userData: any = useState("userData", () => {
  // Try to load user data from localStorage on initialization
  if (process.client) {
    const storedUserData = localStorage.getItem("userData")
    return storedUserData ? JSON.parse(storedUserData) : []
  }
  return []
})
const submitedButton = useState("submitedButton", () => false)
const errors = ref(false)

const loading = ref(false)
const shippingData = ref()
const formStatus = ref(false)

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

const item = ref({
  first_name: '',
  last_name: '',
  address_1: '',
  address_2: '',
  city: '',
  email: '',
  phone: ''
})

const selectedAddress: any = useState("selectedAddress", () => {
  // Try to load selected address from localStorage on initialization
  if (process.client) {
    const storedSelectedAddress = localStorage.getItem("selectedAddress")
    return storedSelectedAddress || 'default'
  }
  return 'default'
})

const selectedAddressData: any = useState("selectedAddressData", () => {
  // Try to load selected address data from localStorage on initialization
  if (process.client) {
    const storedAddressData = localStorage.getItem("selectedAddressData")
    return storedAddressData ? JSON.parse(storedAddressData) : null
  }
  return null
})
const additionalAddress: any = useState("additionalAddress", () => [])

const handleAddressSelect = (value: any) => {
  // Save selected address to localStorage
  if (process.client) {
    localStorage.setItem("selectedAddress", value)
  }
  
  let addressData = null
  
  if (value === 'default') {
    // Set default address from shipping data
    addressData = {
      first_name: shippingData.value?.shipping?.first_name || '',
      last_name: shippingData.value?.shipping?.last_name || '',
      address_1: shippingData.value?.shipping?.address_1 || '',
      address_2: shippingData.value?.shipping?.address_2 || '',
      email: shippingData.value?.billing?.email || '',
      phone: shippingData.value?.shipping?.phone || '',
      city: shippingData.value?.shipping?.city || '',
    }
    
    Object.assign(state, addressData)
  } else {
    const address = additionalAddress.value[value]
    if (address) {
      addressData = {
        first_name: address.ship_fname,
        last_name: address.ship_lname,
        address_1: address.ship_address_1,
        address_2: address.ship_address_2,
        email: address.ship_address_email || shippingData.value?.billing?.email,
        phone: address.ship_address_phone,
        city: address.ship_address_city,
      }
      
      Object.assign(state, addressData)
    }
  }
  
  // Save the complete address data to localStorage
  if (process.client && addressData) {
    localStorage.setItem("selectedAddressData", JSON.stringify(addressData))
    selectedAddressData.value = addressData
  }
}

const schema = z.object({
  first_name: z.string()
  .min(3, t('First Name must be at least 3 characters')),
  last_name: z.string()
  .min(3, t('Last Name must be at least 3 characters')),
  address_1: z.string()
  .min(3, t('Title must be at least 3 characters')),
  address_2: z.string()
  .min(3, t('Adress must be at least 3 characters')),
  email: z.string()
  .min(4, t('Email must be at least 3 characters')),
  phone: z.string()
  .min(11, t('Phone number must be at least 11 characters')),
  city: z.string()
  .min(1, t('City is required')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  first_name: undefined,
  last_name: undefined,
  phone: undefined,
  address_1: undefined,
  address_2: undefined,
  email: undefined,
  city: undefined,
})

async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true

  if (selectedAddress.value !== 'default' && typeof selectedAddress.value === 'number') {
    // Update additional address
    const data = {
      userid: Number(userData?.value?.id),
      row_index: selectedAddress.value,
      fields: {
        ship_address_1: state.address_1,
        ship_fname: state.first_name,
        ship_lname: state.last_name,
        ship_address_2: state.address_2,
        ship_address_city: state.city,
        ship_address_phone: state.phone,
        ship_address_email: state.email
      }
    }

    const response = await $fetch('/api/newBranch/address/addAddress', {
      method: 'post',
      body: data
    }) as any

  } else {
    // Update default address
    const response = await $fetch(`/api/newBranch/customer/update/${Number(userData?.value?.id)}`, {
      method: 'post',
      body: { 
        shipping: state,
        billing: state,
        email: state.email
      }
    }) as any

    if (response.error) {
      errors.value = true
      loading.value = false
      return
    }
  }

  // Refresh data after update
  const { items: usersData } = await fetchHock(`newBranch/customer/${Number(userData?.value?.id)}`)
  shippingData.value = usersData.value
  userData.value = usersData.value

  localStorage.setItem("userData", JSON.stringify(usersData.value))

  // Update additional addresses
  usersData.value.meta_data?.forEach((el: any) => {
    if (el.key === 'additional_shipping_addresses') {
      additionalAddress.value = el.value
    }
  })

  // Update form state with new data based on selected address
  if (selectedAddress.value === 'default') {
    Object.assign(state, {
      first_name: shippingData.value.shipping.first_name,
      last_name: shippingData.value.shipping.last_name,
      address_1: shippingData.value.shipping.address_1,
      address_2: shippingData.value.shipping.address_2,
      email: shippingData.value.billing.email,
      phone: shippingData.value.shipping.phone,
      city: shippingData.value.shipping.city,
    })
  } else if (typeof selectedAddress.value === 'number') {
    // Keep the selected additional address data
    const selectedAddressData = additionalAddress.value[selectedAddress.value]
    if (selectedAddressData) {
      Object.assign(state, {
        first_name: selectedAddressData.ship_fname,
        last_name: selectedAddressData.ship_lname,
        address_1: selectedAddressData.ship_address_1,
        address_2: selectedAddressData.ship_address_2,
        email: selectedAddressData.ship_address_email || shippingData.value?.billing?.email,
        phone: selectedAddressData.ship_address_phone,
        city: selectedAddressData.ship_address_city,
      })
    }
  }

  // Update item.value to match the current state
  Object.assign(item.value, {
    first_name: state.first_name,
    last_name: state.last_name,
    address_1: state.address_1,
    address_2: state.address_2,
    email: state.email,
    phone: state.phone,
    city: state.city,
  })

  submitedButton.value = true
  formStatus.value = true
  errors.value = false
  loading.value = false
}

const { items: cities } = await fetchHock(`cities`)

// Initialize data on component mount
onMounted(async () => {
  // Check if user is already logged in on page refresh
  if (tokenCookie.value && userData.value?.id) {
    await loadShippingData()
  } else if (tokenCookie.value && userIdCookie.value) {
    // Token exists but userData might not be loaded yet, try to fetch user data first
    try {
      const { items: userDataResponse } = await fetchHock(`newBranch/customer/${Number(userIdCookie.value)}`)
      userData.value = userDataResponse.value
      localStorage.setItem("userData", JSON.stringify(userDataResponse.value))
      
      if (userData.value?.id) {
        await loadShippingData()
      }
    } catch (error) {
      // Error fetching user data on mount
    }
  }
})

// Watch for token changes to handle login/logout
watch(tokenCookie, async (current, previous) => {
  if (current && userData.value?.id) {
    // User is logged in, load shipping data
    await loadShippingData()
  } else if (!current && previous) {
    // User just logged out, clear all data
    clearAllData()
  }
})

// Watch for userData changes
watch(userData, async (current, previous) => {
  if (current?.id && tokenCookie.value) {
    // User data is available and user is logged in, load shipping data
    await loadShippingData()
  } else if (!current?.id && previous?.id) {
    // User data was cleared (logout), clear all data
    clearAllData()
  }
}, { deep: true })

// Watch for userIdCookie changes to handle cases where user ID is available but userData isn't loaded
watch(userIdCookie, async (current, previous) => {
  if (current && tokenCookie.value && !userData.value?.id) {
    // User ID is available but userData isn't loaded, fetch user data
    try {
      const { items: userDataResponse } = await fetchHock(`newBranch/customer/${Number(current)}`)
      userData.value = userDataResponse.value
      localStorage.setItem("userData", JSON.stringify(userDataResponse.value))
      
      if (userData.value?.id) {
        await loadShippingData()
      }
    } catch (error) {
      // Error fetching user data from userIdCookie
    }
  }
})

// Function to load shipping data
const loadShippingData = async () => {
  try {
    // Use the correct API endpoint based on the pattern in the codebase
    const { items: shipping } = await fetchHock(`newBranch/customer/${Number(userData.value?.id)}`)
    shippingData.value = shipping.value
    
    if (shippingData.value?.shipping?.address_1) {
      formStatus.value = true
      
      // Use stored selected address or default to 'default'
      const storedAddress = process.client ? localStorage.getItem("selectedAddress") : null
      const storedAddressData = process.client ? localStorage.getItem("selectedAddressData") : null
      const addressToSelect = storedAddress || 'default'
      
      // Only set to default if no stored address exists
      if (!storedAddress) {
        selectedAddress.value = 'default'
        handleAddressSelect('default')
      } else {
        // Use the stored address selection
        selectedAddress.value = addressToSelect
        
        // If we have stored address data, use it directly
        if (storedAddressData) {
          const parsedAddressData = JSON.parse(storedAddressData)
          selectedAddressData.value = parsedAddressData
          
          // Update form state with stored data
          Object.assign(state, parsedAddressData)
          
          // Update item state with stored data
          Object.assign(item.value, parsedAddressData)
        } else {
          // Fallback to handleAddressSelect if no stored data
          handleAddressSelect(addressToSelect)
        }
      }
      
      // Update form state (fallback to default if no stored data)
      if (!storedAddressData) {
        Object.assign(state, {
          first_name: shippingData.value.shipping.first_name,
          last_name: shippingData.value.shipping.last_name,
          address_1: shippingData.value.shipping.address_1,
          address_2: shippingData.value.shipping.address_2,
          email: shippingData.value.billing.email,
          phone: shippingData.value.shipping.phone,
          city: shippingData.value.shipping.city,
        })
        
        // Update item state
        Object.assign(item.value, {
          first_name: shippingData.value.shipping.first_name,
          last_name: shippingData.value.shipping.last_name,
          address_1: shippingData.value.shipping.address_1,
          address_2: shippingData.value.shipping.address_2,
          email: shippingData.value.billing.email,
          phone: shippingData.value.shipping.phone,
          city: shippingData.value.shipping.city,
        })
      }
    }

    // Load additional addresses
    if (shipping.value?.meta_data) {
      shipping.value.meta_data.forEach((el: any) => {
        if (el.key === 'additional_shipping_addresses') {
          additionalAddress.value = el.value
        }
      })
    }
  } catch (error) {
    // Error loading shipping data
  }
}

// Function to clear all data on logout
const clearAllData = () => {
  shippingData.value = null
  formStatus.value = false
  selectedAddress.value = 'default'
  selectedAddressData.value = null
  additionalAddress.value = []
  
  // Clear localStorage
  if (process.client) {
    localStorage.removeItem("selectedAddress")
    localStorage.removeItem("selectedAddressData")
  }
  
  // Clear form state
  Object.assign(state, {
    first_name: undefined,
    last_name: undefined,
    phone: undefined,
    address_1: undefined,
    address_2: undefined,
    email: undefined,
    city: undefined,
  })
  
  // Clear item state
  Object.assign(item.value, {
    first_name: '',
    last_name: '',
    address_1: '',
    address_2: '',
    city: '',
    email: '',
    phone: ''
  })
  
  errors.value = false
  submitedButton.value = false
}

const enableForm = () => {
  formStatus.value = !formStatus.value
}

const totalItemss = ref(0)

totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)

watch(cartItems, async (current) => {
  if (cartItems?.value?.single?.[0]?.items?.length) {
    totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
      return acc + Number(item.quantity || 0)
    }, 0)
  }
})
</script>
<style>
.information-mob .el-select__wrapper {
  background-color: #fff;
  border-radius: 123123123123123px;
}
.el-select__selected-item.el-select__placeholder {
  color: #1D3C34 !important;
  font-style: normal !important;
}
</style>