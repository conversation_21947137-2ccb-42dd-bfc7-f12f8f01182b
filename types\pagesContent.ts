export interface Section {
  type: string;
  title?: string;
  title_ar?: string;
}
export interface Product {
  type?: string;
  endpoint?: string;
  id: number;
  name?: string;
  name_ar?: string;
  stock?: number;
  price?: number;
  sku?: string;
  image?: string;
  sale?: {
    title?: string;
    title_ar?: string;
    percent?: string;
    percent_ar?: string;
    line_through?: string;
    line_through_ar?: string;
  };
  parent?: number;
  description?: string;
  description_ar?: string;
  excerpt?: string;
  brand_id?: number;
  brand_name?: string;
  brand_name_ar?: string;
  brand_logo?: string;
  load_index?: {
    name?: string;
    name_ar?: string;
    value?: string;
  };
  speed_index?: {
    name?: string;
    name_ar?: string;
    value?: string;
  };
  origin?: {
    name?: string;
    name_ar?: string;
    value?: string;
  };
  [key: string]: any;
}

export interface ProductsCarouselSection extends Section {
  type: 'products_carousel';
  title: string; // Make title required for ProductsCarouselSection
  cat_id: string;
  product_type: 'variable' | 'simple';
  products: Array<Product>; // Now properly typed with Product interface
}

export interface AdsCarouselSection extends Section {
  type: 'ads_carousel';
  ads: Array<{
    content: string;
    image: string;
    redirect_link: string;
  }>;
}

export interface BrandsCarouselSection extends Section {
  type: 'brands_carousel';
  brands: Array<{
    brand_name: string;
    brand_logo: string;
    brand_id: number;
    cat_id: string;
    status: boolean;
  }>;
}

export interface ArticlesCarouselSection extends Section {
  type: 'articles_carousel';
  title: string; // Make title required for ArticlesCarouselSection
  posts: Array<{
    title: string;
    content: string;
    featured_image: string;
    link: string;
  }>;
}

export interface ContactInfo {
  contact_title: string;
  contact_country: string;
  contact_city: string;
  contact_address: string;
  contact_mobile: string;
  contact_email: string;
}

export interface PageContent {
  title: string;
  title_ar?: string;
  slug: string;
  id: number;
  page_banner_desc: string;
  page_banner_desc_ar?: string;
  banner_link_text: string;
  banner_link_text_ar: string;
  banner_link_slug: string;
  content: string;
  content_ar?: string;
  featured_image: string;
}

export interface HomePageContent extends PageContent {
  sections: Array<ProductsCarouselSection | AdsCarouselSection | BrandsCarouselSection | ArticlesCarouselSection>;
}

export interface ContactPageContent extends PageContent {
  contact_info: Array<ContactInfo>;
}

export interface PagesContent {
  'privacy-policy': PageContent;
  'faq': PageContent;
  'contact': ContactPageContent;
  'thank-you': PageContent;
  'checkout': PageContent;
  'cart': PageContent;
  'home': HomePageContent;
  'tips-and-tricks': PageContent;
  'about': PageContent;
  [key: string]: PageContent | HomePageContent | ContactPageContent; // For dynamic access
}
