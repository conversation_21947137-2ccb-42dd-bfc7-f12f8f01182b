<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="grid grid-cols-2 gap-6">
      <div>
        <div>
          <!-- <ClientOnly> -->
          <CheckoutUserInformation />
          <!-- </ClientOnly> -->
        </div>
        <div class="mt-16">
          <CheckoutShippingInformation />
        </div>
        <div class="mt-16">
          <CheckoutPaymentInformation />
        </div>
      </div>
      <div>
        <CheckoutPlaceOrder />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const router = useRouter()
const FormPhoneNumber = useState("FormPhoneNumber", () => "")
const orders: any = useState("orders", () => [])
const selectedPaymentMethod: any = useState("selectedPaymentMethod", () => '')
selectedPaymentMethod.value = ''

const localePath = useLocalePath()

const submitedButton = useState("submitedButton", () => false)
const selectedAddress: any = useState("selectedAddress", () => 'default')
const additionalAddress: any = useState("additionalAddress", () => [])

onMounted(() => {
  const completeOrder = localStorage.getItem("completeOrder")
  orders.value = completeOrder ? JSON.parse(completeOrder) : null
})

watch(orders, (current) => {
  const cartUrl = '/cart'  // Generate the localized path to the cart
  if (current?.length === 0) {
    router.push(localePath(cartUrl))
  } if (!current) {
    router.push(localePath(cartUrl))
  }
})
</script>