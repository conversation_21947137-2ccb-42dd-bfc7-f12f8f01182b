// Fetch the filtered item data from backend
const { items: filteredItem } = await fetchHock(`newFilteration/${Number(route.params.id)}`);

// Compute unique variations
const uniqueVariations = computed(() => {
  if (!filteredItem.value || !filteredItem.value.variations) {
    return {};
  }

  const variations = filteredItem.value.variations;
  const uniqueAttributes: Record<string, Set<string>> = {};

  variations.forEach((variation: any) => {
    Object.entries(variation).forEach(([key, value]) => {
      if (!uniqueAttributes[key]) {
        uniqueAttributes[key] = new Set();
      }
      uniqueAttributes[key].add(value as string);
    });
  });

  // Convert sets to arrays of objects with option and status properties
  return Object.fromEntries(
    Object.entries(uniqueAttributes).map(([key, valueSet]) => [
      key,
      Array.from(valueSet).map(value => ({ option: value, status: true }))
    ])
  );
});