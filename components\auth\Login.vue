<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="pt-10 relative sm:max-w-[60%] mx-auto border-b border-primary pb-5">
      <div class="md:mt-5">
        <!-- Welcome message - desktop -->
        <h1 class="font-secondaryFont font-bold rtl:font-notoSans text-primary mb-10 text-center mx-auto sm:text-lg sm:max-w-[80%] hidden md:block">
          {{ $t("Welcome back! We're glad to see you again. Please log in using the form below.") }}
        </h1>

        <!-- Welcome message - mobile -->
        <h1 class="text-primary font-bold rtl:font-notoSans font-secondaryFont text-[15px] md:hidden text-center">
          {{ $t('Welcome Back, Login!') }}
        </h1>

        <!-- Error messages -->
        <div
          v-if="titleError"
          class="text-red-700 font-bold text-lg text-center uppercase rtl:font-notoSans"
          role="alert"
          aria-live="assertive"
        >
          {{ titleError }}
        </div>

        <div
          v-if="urlError"
          class="mb-2 text-red-700 font-bold text-center mt-2 uppercase text-sm rtl:font-notoSans"
          role="alert"
          aria-live="assertive"
        >
          {{ urlError }}
        </div>

        <div class="mt-5">
          <!-- Server error message -->
          <div
            v-if="serverStatus"
            class="mb-2 text-red-700 font-bold text-lg rtl:font-notoSans"
            role="alert"
            aria-live="assertive"
          >
            {{ serverErrors === $t('username does not exist') ? $t('Phone number does not exist') : serverErrors }}
          </div>

          <!-- Login form -->
          <UForm
            ref="form"
            :schema="schema"
            :state="state"
            class="space-y-4"
            @submit="onSubmit"
            aria-label="Login form"
          >
            <div class="w-full mx-auto flex flex-col gap-6">
              <!-- Phone number field -->
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="username" label-class="sr-only">
                  <label for="phone-input" class="sr-only">{{ $t('Phone Number') }}</label>
                  <input
                    id="phone-input"
                    class="w-full border border-primary rounded-full !bg-thirdColor py-2 px-5 md:py-3 md:px-5 md:rounded-lg focus:outline-0"
                    type="tel"
                    inputmode="numeric"
                    pattern="[0-9]*"
                    autocomplete="tel"
                    v-model="state.username"
                    required
                    placeholder=" "
                    aria-label="Phone Number"
                  >
                  <span
                    class="uppercase absolute text-primary font-semibold md:text-base text-[13px] z-[4] rtl:right-[22px] ltr:left-[22px] md:top-[13px] top-[11px] transition-all duration-100 custom-auth-style ease-in-out"
                    :class="{ 'placeholder-animation-bg-13': state.username }"
                  >
                    {{ $t('Phone Number') }}
                  </span>
                </UFormGroup>
              </div>

              <!-- Password field -->
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="password" label-class="sr-only">
                  <label for="password-input" class="sr-only">{{ $t('Password') }}</label>
                  <input
                    id="password-input"
                    class="w-full border border-primary rounded-full py-2 px-5 !bg-thirdColor md:py-3 md:px-5 md:rounded-lg focus:outline-0"
                    type="password"
                    autocomplete="current-password"
                    v-model="state.password"
                    required
                    placeholder=" "
                    aria-label="Password"
                  >
                  <span
                    class="uppercase absolute text-primary rtl:font-notoSans font-semibold md:text-base text-[13px] z-[4] rtl:right-[22px] ltr:left-[22px] md:top-[13px] top-[11px] transition-all duration-100 custom-auth-style ease-in-out"
                    :class="{ 'placeholder-animation-bg-13': state.password }"
                  >
                    {{ $t('Password') }}
                  </span>
                </UFormGroup>
              </div>
            </div>

            <!-- Form buttons -->
            <div class="text-center !mt-10 flex gap-2 justify-center items-center flex-col">
              <!-- Login button -->
              <UButton
                type="submit"
                class="text-thirdColor font-bold font-thirdFont rounded-full rtl:font-notoSans md:text-lg border uppercase border-primary bg-primary shadow-unset lg:px-[150px] md:px-[100px] px-10 lg:py-3 md:py-1 hover:scale-[1.1] ease-in-out duration-500"
                :loading="isLoading"
                :disabled="isLoading"
                variant="solid"
                aria-label="Login"
              >
                <span v-if="!isLoading">{{ $t('LOGIN') }}</span>
              </UButton>

              <!-- Register link -->
              <UButton
                class="text-orangeColor text-base underline"
                variant="link"
                :to="localePath('/auth/register')"
                aria-label="Register as a new user"
              >
                {{ $t('New User?') }}
              </UButton>
            </div>
          </UForm>
        </div>

        <!-- Forgot password link -->
        <div class="text-end">
          <ULink
            :to="localePath('/auth/forgot-password')"
            class="text-primary text-sm sm:text-lg underline mt-5"
            aria-label="Forgot password"
          >
            {{ $t('Forgot Password?') }}
          </ULink>
        </div>
      </div>
    </div>

    <!-- Social login options -->
    <AuthSocialLogin />
  </div>
</template>
<script setup lang="ts">
import { z } from 'zod'
import { ElNotification } from 'element-plus'
import type { UserData } from '~/types/user'
import type { CartItem } from '~/types/defaultCart'

/**
 * Router and state management
 */
const { t } = useI18n()
const localePath = useLocalePath()
const router = useRouter()
import { persistentCookieOptions } from '~/utils/cookieOptions'
const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)
const userData = useState<UserData>("userData", () => ({} as UserData))
const cartItems = useState<CartItem[]>("cartItems", () => [])

/**
 * Form state and validation
 */
const isLoading = ref<boolean>(false)
const urlError = useState<string>("urlError", () => '')
const titleError = useState<string>("titleError", () => '')
const serverErrors = ref<string>('')
const serverStatus = ref<boolean>(false)

// Define form validation schema with Zod
const schema = z.object({
  username: z.string()
    .min(11, t('Phone must be at least 11 characters'))
    .regex(/^\d+$/, t('Phone number must contain only digits')),
  password: z.string()
    .min(6, t('Password must be at least 6 characters')),
})

// Define the form state interface
interface LoginState {
  username: string;
  password: string;
}

// Initialize form state
const state = reactive<LoginState>({
  username: '',
  password: '',
})

/**
 * Display a notification message
 * @param message The message to display
 * @param type The type of notification (success or error)
 */
const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  ElNotification({
    message: h('i', {
      style: `color: ${type === 'success' ? '#1D3C34' : '#ff4949'}; font-weight: bold;`
    }, message),
    position: 'top-right',
    duration: 3000,
    type
  })
}

/**
 * Interface for login API response
 */
interface LoginResponse {
  response: {
    login: boolean;
    msg: string;
  };
  user: {
    data: {
      ID: string;
      user_pass: string;
      [key: string]: any;
    } | null;
    ID: number;
    [key: string]: any;
  } | null;
}

/**
 * Handle form submission
 */
async function onSubmit() {
  try {
    // Reset error states
    serverStatus.value = false
    serverErrors.value = ''
    isLoading.value = true

    // Call login API
    const response = await $fetch<LoginResponse>('/api/newBranch/login', {
      method: 'post',
      body: state
    })

    // Handle login failure
    if (response.user === null) {
      serverStatus.value = true
      const errorMessage = response.response.msg === 'username does not exist'
        ? 'Phone number does not exist'
        : response.response.msg

      serverErrors.value = errorMessage
      showNotification(errorMessage, 'error')
      window.scrollTo({ top: 0, behavior: 'smooth' })
      return
    }

    // Handle successful login
    if (response.response.login) {
      // Set cookies
      tokenCookie.value = response?.user?.data?.user_pass
      userIdCookie.value = response?.user?.data?.ID

      // Show success message
      showNotification(t('Login successful! Welcome back.'), 'success')

      // Fetch user data and cart items in parallel
      const userId = Number(response?.user?.data?.ID)
      const [ordersResponse, userDataResponse] = await Promise.all([
        fetchHock(`newBranch/orders/${userId}`),
        fetchHock(`newBranch/customer/${userId}`)
      ])

      // Update state
      userData.value = userDataResponse.items.value
      cartItems.value = ordersResponse.items.value

      // Redirect to home page
      router.push('/')
    }
  } catch (error) {
    console.error('Login error:', error)
    showNotification(t('An error occurred during login. Please try again.'), 'error')
    serverStatus.value = true
    serverErrors.value = 'Network error'
  } finally {
    isLoading.value = false
  }
}
</script>