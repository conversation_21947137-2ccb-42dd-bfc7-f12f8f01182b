<template>
  <div class="md:px-[30px] md:max-w-[1200px] md:mx-auto">
    <div class="md:mt-10" v-if="!loading">





      <div class="justify-between items-center mb-10 hidden md:flex">
        <div class="text-secondary font-bold font-secondaryFont text-[22px] uppercase rtl:font-notoSans">
          {{ $t('My ORDERS') }} ({{ ordersCount ?? '0' }})
        </div>
        <div class="myOrders-select-status flex gap-2 variations-filter relative">
          <div
            class="
              uppercase
              absolute
              text-primary
              font-primaryFont
              font-semibold
              opacity-1
              font-base
              z-[4]
              rtl:font-notoSans
              ltr:left-[15px]
              rtl:right-[15px]
              top-[11px]
              transition-all
              duration-100
              ease-in-out
              focus:placeholder-animation-with-bg"
              :class="{
                'placeholder-animation-with-bg' : statusSelected,
              }"
            >
              {{ $t('STATUS') }}
            </div>
          <el-select
            v-model="statusSelected"
            placeholder=" "
            size="large"
            @change="statusChanged"
          >
            <el-option
              label="All"
              value=""
            />
            <el-option
              v-for="item in statuses"
              :key="item.slug"
              :label="item.name"
              :value="item.slug"
            />
          </el-select>
        </div>
      </div>

      <div class="flex justify-between items-center mb-10 bg-thirdColor border-b border-primary px-[30px] py-3 md:hidden">
        <div class="text-primary rtl:font-notoSans font-bold font-secondaryFont text-[15px] uppercase">
          {{ $t('My ORDERS') }} ({{ ordersCount ?? '0' }})
        </div>
        <div class="support-select-status select-status-responsive variations-filter relative">
          <div
            class="
              uppercase
              absolute
              text-thirdColor
              font-primaryFont
              font-semibold
              opacity-1
              font-base
              z-[4]
              rtl:font-notoSans
              ltr:left-[22px]
              rtl:right-[22px]
              top-[10px]
              transition-all
              duration-100
              ease-in-out
              focus:placeholder-animation-with-bg"
              :class="{
                'placeholder-animation-with-bg !bg-orangeColor !border-thirdColor' : statusSelected,
              }"
            >
              {{ $t('STATUS') }}
            </div>
          <el-select
            v-model="statusSelected"
            placeholder=" "
            size="large"
            @change="statusChanged"
          >
            <el-option
              label="All"
              value=""
            />
            <el-option
              v-for="item in statuses"
              :key="item.slug"
              :label="item.name"
              :value="item.slug"
            />
          </el-select>
        </div>
      </div>





      <div class="faq-content-container my-order-tab mb-10 hidden md:block">
        <div v-if="ordersCount === 0" class="text-primary rtl:font-notoSans font-bold text-3xl text-center font-primaryFont mt-14 mb-20">
          {{ $t('No Orders Found') }}
        </div>
        <div v-if="order?.grouped?.length">
          <el-collapse v-model="activeItems" accordion>
            <el-collapse-item
              v-for="(item, index) in order?.grouped ?? []"
              :key="index"
              :title="item.group"
              :name="index"
            >
              <template #title>
                <span class="flex justify-between w-full rtl:font-notoSans">
                  <span>
                    {{ $t('Group Order') }}  #{{ item.group }}  | {{ item.orders.length }} {{ $t('Items') }} | {{ dateFormat(item?.date) }}
                  </span>
                  <span class="flex gap-1 rtl:flex-row-reverse">
                    <span>{{ $t('EGP') }}</span> <span>{{ item?.total }}</span>
                  </span>
                </span>
              </template>
              <div>
                <div>
                  <div
                    v-for="(i, indexx) in item.orders"
                    :key="i.id"
                    class="w-full"
                  >
                    <div
                      class="bg-thirdColor w-full flex items-center justify-between py-3 px-14 border-primary rtl:font-notoSans"
                      :class="{ 'border-b': indexx === 0, 'border-y': indexx !== 0 }"
                    >
                      <div class="font-secondaryFont font-bold text-xl text-primary uppercase">{{ $t('Order') }} #{{ i.id }}</div>
                      <div class="font-thirdFont font-bold text-orangeColor text-lg uppercase">{{ i.status }}</div>
                    </div>
                    <div
                      v-for="(orderItem, indexxxx) in i.items"
                      class="py-4 px-20"
                    >
                      <div class="mt-[20px] flex items-center justify-between h-fit w-full pb-10"
                        :class="{ 'border-b border-primary': indexxxx !== i.items.length - 1 }">
                        <div class="flex items-center gap-14 md:w-[55%] lg:w-[45%]">
                          <div class="relative w-[125px] h-[110px] flex items-center cursor-pointer" @click="navigateToProduct(orderItem)">
                            <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary" :src="orderItem.brand_logo" />
                            <img class="absolute top-0 ltr:left-[35px] rtl:right-[35px]" :src="orderItem.image" />
                          </div>
                          <div class="cursor-pointer" @click="navigateToProduct(orderItem)">
                            <div class="font-primaryFont text-xl italic font-semibold text-primary rtl:font-notoSans">{{ locale === 'en' ? orderItem.product_name : orderItem.product_name_ar }}</div>
                            <div class="font-primaryFont text-lg italic font-semibold text-orangeColor">
                              {{ locale === 'en' ? orderItem?.excerpt : orderItem?.excerpt_ar }}
                            </div>
                            <template v-if="Array.isArray(orderItem?.meta_data)">
                              <div class="flex flex-col mt-2">
                                <div
                                  class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary"
                                  v-for="(varData, index) in orderItem?.meta_data ?? []"
                                  :key="index"
                                >
                                  <span class="font-semibold rtl:font-notoSans">{{ locale === 'en' ? varData?.name : varData?.name_ar }}: </span>
                                  <span class="font-bold">{{ varData?.value }} </span>
                                </div>
                              </div>
                            </template>
                          </div>
                        </div>
                        <UButton
                          v-if="i?.status == 'completed' && !orderItem?.reviews"
                          variant="solid"
                          class="
                            font-secondaryFont
                            font-bold
                            text-lg
                            py-2
                            px-10
                            rounded-full
                            uppercase
                            box-shadow
                            rtl:font-notoSans
                            border
                            text-thirdColor
                            border-primary
                            bg-primary
                            hover:bg-orangeColor
                            hover:border-orangeColor
                            ease-in-out
                            duration-500"
                          @click="reviewOrderGroupFunc(i.id, orderItem)"
                        >
                          {{ $t('review') }}
                        </UButton>
                        <div v-if="orderItem?.reviews" class="text-center">
                          <div class="flex items-center gap-1 text-center justify-center">
                            <template v-for="star in 5" :key="star">
                              <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.02804 16.7414L6.81621 12.2568L1.86755 11.5353C0.980115 11.4066 0.624462 10.3125 1.26803 9.68591L4.84827 6.19712L4.00147 1.26879C3.84905 0.377966 4.7873 -0.289307 5.57312 0.127316L10.0002 2.4543L14.4272 0.127316C15.213 -0.285919 16.1513 0.377966 15.9988 1.26879L15.152 6.19712L18.7323 9.68591C19.3759 10.3125 19.0202 11.4066 18.1328 11.5353L13.1841 12.2568L10.9723 16.7414C10.576 17.5408 9.42772 17.5509 9.02804 16.7414Z" 
                                :fill="star <= orderItem.reviews.rating ? '#FF671F' : 'none'" 
                                stroke="#FF671F"/>
                              </svg>
                            </template>
                          </div>
                          <div class="italic font-medium font-primaryFont rtl:font-notoSans text-primary text-[13px] mt-1">
                            {{ locale === 'en' ? orderItem?.reviews?.comment_content : orderItem?.reviews?.comment_content_ar }}
                          </div>
                        </div>
                        <div class="font-primaryFont font-extrabold text-primary text-2xl">
                          <span class="text-base">X</span>{{ orderItem.quantity }}
                        </div>
                        <div class="text-primary font-bold text-2xl font-primaryFont text-center">
                          <div class="text-center rtl:font-notoSans">{{ $t('EGP') }}</div>
                          <div>{{ formatPrice(Number(orderItem.display_price)) }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-full flex items-center gap-5 justify-center bg-thirdColor border-t border-primary py-8">
                <UButton
                  class="
                    font-thirdFont
                    font-bold
                    text-lg
                    py-2
                    px-36
                    rounded-full
                    uppercase
                    box-shadow
                    border
                    rtl:font-notoSans
                    text-thirdColor
                    border-orangeColor
                    bg-orangeColor
                    hover:bg-orangeColor
                    hover:border-orangeColor
                    ease-in-out
                    duration-500
                  "
                  variant="solid"
                  :to="localePath(`/trackorder/${item.group}`)"
                >
                  {{ $t('TRACK ORDER') }}
                </UButton>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div v-if="order?.single?.length">
          <el-collapse v-model="activeItems" accordion>
            <el-collapse-item
              v-for="(item, index) in order?.single ?? []"
              :key="item.id"
              :title="item.order_title"
              :name="item.id"
            >
              <template #title>
                <span class="flex justify-between w-full rtl:font-notoSans">
                  <span>
                    {{ $t('Order') }}  #{{ item?.id }}  | {{ item?.items?.length }} {{ $t('Items') }} | {{ dateFormat(item?.date) }}
                  </span>
                  <span class="flex gap-1 rtl:flex-row-reverse">
                    <span>{{ $t('EGP') }}</span> <span>{{ item?.total }}</span>
                  </span>
                </span>
              </template>
              <div>
                <div>
                    <div
                    v-for="(orderItem, indexxxx) in item.items"
                    class="py-4 px-20"
                  >
                    <div class="mt-[20px] flex items-center justify-between h-fit w-full pb-10" :class="{ 'border-b border-primary': indexxxx !== item.items.length - 1 }">
                      <div class="flex items-center gap-14 md:w-[65%] lg:w-[55%]">
                        <div class="relative w-[125px] h-[110px] flex items-center cursor-pointer" @click="navigateToProduct(orderItem)">
                          <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary" :src="orderItem.brand_logo"/>
                          <img class="absolute top-0 ltr:left-[35px] rtl:right-[35px]" :src="orderItem.image"/>
                        </div>
                        <div class="cursor-pointer" @click="navigateToProduct(orderItem)">
                          <div class="font-primaryFont text-xl italic font-semibold text-primary rtl:font-notoSans">{{ locale === 'en' ? orderItem.product_name : orderItem.product_name_ar }}</div>
                          <div class="font-primaryFont text-lg italic font-semibold text-orangeColor">
                            {{ locale === 'en' ? orderItem?.excerpt : orderItem?.excerpt_ar }}
                          </div>
                          <template v-if="Array.isArray(orderItem?.meta_data)">
                            <div class="flex flex-col mt-2">
                              <div
                                class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary"
                                v-for="(varData, index) in orderItem?.meta_data ?? []"
                                :key="index"
                              >
                                <span class="font-semibold rtl:font-notoSans">{{ locale === 'en' ? varData?.name : varData?.name_ar }}: </span>
                                <span class="font-bold">{{ varData?.value }} </span>
                              </div>
                            </div>
                          </template>
                        </div>
                      </div>
                      <UButton
                        v-if="item?.status == 'completed' && !orderItem?.reviews"
                        variant="solid"
                        class="
                          font-secondaryFont
                          font-bold
                          text-lg
                          py-2
                          px-10
                          rounded-full
                          uppercase
                          box-shadow
                          border
                          rtl:font-notoSans
                          text-thirdColor
                          border-primary
                          bg-primary
                          hover:bg-orangeColor
                          hover:border-orangeColor
                          ease-in-out
                          duration-500"
                          @click="reviewOrderGroupFunc(item.id, orderItem)"
                      >
                        {{ $t('review') }}
                      </UButton>
                      <div v-if="orderItem?.reviews" class="text-center">
                        <div class="flex items-center gap-1 text-center justify-center">
                          <template v-for="star in 5" :key="star">
                            <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9.02804 16.7414L6.81621 12.2568L1.86755 11.5353C0.980115 11.4066 0.624462 10.3125 1.26803 9.68591L4.84827 6.19712L4.00147 1.26879C3.84905 0.377966 4.7873 -0.289307 5.57312 0.127316L10.0002 2.4543L14.4272 0.127316C15.213 -0.285919 16.1513 0.377966 15.9988 1.26879L15.152 6.19712L18.7323 9.68591C19.3759 10.3125 19.0202 11.4066 18.1328 11.5353L13.1841 12.2568L10.9723 16.7414C10.576 17.5408 9.42772 17.5509 9.02804 16.7414Z" 
                              :fill="star <= orderItem.reviews.rating ? '#FF671F' : 'none'" 
                              stroke="#FF671F"/>
                            </svg>
                          </template>
                        </div>
                        <div class="italic font-medium font-primaryFont rtl:font-notoSans text-primary text-[13px] mt-1">
                          {{ locale === 'en' ? orderItem?.reviews?.comment_content : orderItem?.reviews?.comment_content_ar }}
                        </div>
                      </div>
                      <div class="font-primaryFont font-extrabold text-primary text-2xl">
                        <span class="text-base">X</span>{{ orderItem.quantity }}
                      </div>
                      <div class="text-primary font-bold text-2xl font-primaryFont text-center">
                        <div class="text-center rtl:font-notoSans">EGP</div>
                        <div>{{ formatPrice(Number(orderItem.display_price)) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-full flex items-center gap-5 justify-center bg-thirdColor border-t border-primary py-8">
                <UButton
                  class="
                    font-thirdFont
                    font-bold
                    text-lg
                    py-2
                    px-36
                    rounded-full
                    uppercase
                    box-shadow
                    border
                    rtl:font-notoSans
                    text-thirdColor
                    border-orangeColor
                    bg-orangeColor
                    hover:bg-orangeColor
                    hover:border-orangeColor
                    ease-in-out
                    duration-500
                  "
                  variant="solid"
                  :to="localePath(`/trackorder/${item.id}`)"
                >
                  {{ $t('TRACK ORDER') }}
                </UButton>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>


      <div class="faq-content-container my-order-tab mb-10 px-[30px] md:hidden">
        <div v-if="ordersCount === 0" class="text-primary rtl:font-notoSans font-bold text-3xl text-center font-primaryFont mt-14 mb-20">
          {{ $t('No Orders Found') }}
        </div>
        <div v-if="order?.grouped?.length">
          <div
            v-for="(item, index) in order?.grouped ?? []"
            :key="index"
            :title="item.group"
            :name="index"
            class="border border-primary rounded-[15px] mb-5 bg-thirdColor"
          >
            <div class="flex flex-col w-full">
              <span class="flex justify-between w-full p-2 px-[20px]">
                <span class="font-semibold font-primaryFont text-[14px] text-primary rtl:font-notoSans">{{ $t('Group Order') }}  #{{ item.group }}  | {{ item.orders.length }} {{ $t('Items') }} | {{ dateFormat(item?.date) }}</span>
              </span>
              <div
                v-for="(i, indexx) in item.orders"
                :key="i.id"
                class="w-full"
              >
                <div
                  class="
                    flex justify-between items-center
                    bg-secondary
                    px-[20px]
                    border-b
                    border-t
                    border-primary
                    text-thirdColor
                    py-1
                    font-secondaryFont"
                  >
                  <div class="text-[12px] uppercase rtl:font-notoSans">{{ $t('Order') }} #{{ i.id }}</div>
                  <div class="flex items-center gap-2">
                    <span class="font-bold text-[13px] uppercase">
                      {{ i.status  }}
                    </span>
                  </div>
                </div>
                <div v-for="(orderItem, index) in i.items ?? []" class="py-1 px-[20px] border-b border-primary border-none-custom" :key="index">
                  <div class="py-5">
                    <div class="flex items-center gap-3 cursor-pointer" @click="navigateToProduct(orderItem)">
                      <div class="relative">
                        <img class="absolute z-[20] w-[40px] h-[40px] rounded-full border border-primary" :src="orderItem.brand_logo" />
                        <img class="w-[80px] h-[80px] object-contain" :src="orderItem.image" />
                      </div>
                      <div class="w-[70%]">
                        <div class="font-primaryFont italic font-semibold text-primary rtl:font-notoSans">{{ locale === 'en' ? orderItem.product_name : orderItem.product_name_ar }}</div>
                        <div class="font-primaryFont text-[14px] italic font-semibold text-orangeColor">
                          {{ locale === 'en' ? orderItem?.excerpt : orderItem?.excerpt_ar }}
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center justify-center">
                      <UButton
                        v-if="i?.status == 'completed' && !orderItem?.reviews"
                        variant="solid"
                        class="
                          font-secondaryFont
                          font-bold
                          mt-3
                          px-10
                          rounded-full
                          uppercase
                          box-shadow
                          border
                          rtl:font-notoSans
                          text-thirdColor
                          border-primary
                          bg-primary
                          hover:bg-orangeColor
                          hover:border-orangeColor
                          ease-in-out
                          duration-500"
                        @click="reviewOrderGroupFunc(i.id, orderItem)"
                      >
                        {{ $t('review') }}
                      </UButton>
                      <div v-if="orderItem?.reviews" class="text-center">
                        <div class="flex items-center gap-1 text-center justify-center">
                          <template v-for="star in 5" :key="star">
                            <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9.02804 16.7414L6.81621 12.2568L1.86755 11.5353C0.980115 11.4066 0.624462 10.3125 1.26803 9.68591L4.84827 6.19712L4.00147 1.26879C3.84905 0.377966 4.7873 -0.289307 5.57312 0.127316L10.0002 2.4543L14.4272 0.127316C15.213 -0.285919 16.1513 0.377966 15.9988 1.26879L15.152 6.19712L18.7323 9.68591C19.3759 10.3125 19.0202 11.4066 18.1328 11.5353L13.1841 12.2568L10.9723 16.7414C10.576 17.5408 9.42772 17.5509 9.02804 16.7414Z" 
                              :fill="star <= orderItem.reviews.rating ? '#FF671F' : 'none'" 
                              stroke="#FF671F"/>
                            </svg>
                          </template>
                        </div>
                        <div class="italic font-medium font-primaryFont text-primary text-[13px] rtl:font-notoSans mt-1">
                          {{ locale === 'en' ? orderItem?.reviews?.comment_content : orderItem?.reviews?.comment_content_ar }}
                        </div>
                      </div>
                    </div>
                    <div class="mt-3">
                      <div class="font-primaryFont text-[13px] rtl:font-notoSans text-primary flex justify-between items-center">
                        <span>{{ $t('Price x Qty.') }}</span>
                        <span class="font-semibold flex items-center gap-1 rtl:flex-row-reverse">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(orderItem.display_price)) }} x {{ orderItem.quantity }}</span>
                        </span>
                      </div>
                      <div class="font-primaryFont text-[13px] rtl:font-notoSans text-orangeColor flex justify-between items-center mt-1">
                        <span>{{ $t('Sub Total') }}</span>
                        <span class="font-semibold flex items-center gap-1 rtl:flex-row-reverse">
                          <span class="rtl:font-notoSans">{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(orderItem.subtotal)) }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="text-[15px] flex items-center justify-between px-[20px] w-full border-t border-primary">
                <div class="py-[20px] font-secondaryFont text-primary w-full">
                  <div class="flex justify-between">
                    <!-- <div>
                      <span class="mb-2 block">
                        <div class="text-[12px]">{{ $t('promo code') }}</div>
                        <div
                          class="text-base font-bold font-primaryFont"
                        >
                          NEWYEAR15
                        </div>
                      </span>
                      <span class="mb-2 block">
                        <div class="text-[12px]">sub total</div>
                        <div
                          class="text-base font-bold font-primaryFont"
                        >
                          {{ formatPrice(item.total) }}
                        </div>
                      </span>
                    </div> -->
                    <div>
                      <span class="mb-2 block">
                        <div class="text-[12px]">{{ $t('Discount') }}</div>
                        <div
                          class="text-base font-bold font-primaryFont"
                        >
                          {{ formatPrice(item.total) }}
                        </div>
                      </span>
                      <span class="mb-2 block">
                        <div class="text-[12px]">{{ $t('TOTAL') }}</div>
                        <div
                          class="text-base font-bold font-primaryFont flex items-center gap-1 rtl:flex-row-reverse"
                        >
                          <span class="rtl:font-notoSans">{{ $t('EGP') }}</span> <span>{{ formatPrice(item.total) }}</span>
                        </div>
                      </span>
                    </div>
                  </div>
                  <div class="w-full px-[10px] flex flex-col gap-3 mt-3">
                    <UButton
                      class="
                        font-thirdFont
                        font-bold
                        text-[15px]
                        py-2
                        px-10
                        uppercase
                        rounded-full
                        box-shadow
                        border
                        rtl:font-notoSans
                        border-orangeColor
                        bg-orangeColor
                        hover:bg-orangeColor
                        hover:border-orangeColor
                        ease-in-out
                        duration-500
                        flex items-center justify-center
                      "
                      variant="solid"
                      :to="localePath(`/trackorder/${item.group}`)"
                    >
                      {{ $t('TRACK ORDER') }}
                    </UButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="order?.single?.length">
          <div
            v-for="(item, index) in order?.single ?? []"
            :key="item.id"
            :title="item.order_title"
            :name="item.id"
            class="border border-primary rounded-[15px] mb-5 bg-thirdColor"
          >
            <div class="flex flex-col w-full">
              <span class="flex justify-between w-full p-2 px-[20px]">
                <span class="font-semibold font-primaryFont text-[14px] text-primary rtl:font-notoSans">{{ $t('Order') }}  #{{ item?.id }}  | {{ item?.items?.length }} {{ $t('Items') }} | {{ dateFormat(item?.date) }}</span>
              </span>

              <div
                class="w-full"
              >
                <div
                  class="
                    flex justify-between items-center
                    bg-secondary
                    px-[20px]
                    border-b
                    border-primary
                    text-thirdColor
                    py-1
                    font-secondaryFont"
                  >
                  <div class="text-[12px] uppercase font-primaryFont rtl:font-notoSans">{{ $t('Status') }}</div>
                  <div class="flex items-center gap-2">
                    <span class="font-bold text-[13px] uppercase">
                      {{ item.status  }}
                    </span>
                  </div>
                </div>
                <div v-for="(orderItem, index) in item.items ?? []" class="py-1 px-[20px] border-b border-primary border-none-custom" :key="index">
                  <div class="py-5">
                    <div class="flex items-center gap-3 cursor-pointer" @click="navigateToProduct(orderItem)">
                      <div class="relative">
                        <img class="absolute z-[20] w-[40px] h-[40px] rounded-full border border-primary" :src="orderItem.brand_logo" />
                        <img class="w-[80px] h-[80px] object-contain" :src="orderItem.image" />
                      </div>
                      <div class="w-[70%]">
                        <div class="font-primaryFont italic font-semibold text-primary rtl:font-notoSans">{{ locale === 'en' ? orderItem.product_name : orderItem.product_name_ar }}</div>
                        <div class="font-primaryFont text-[14px] italic font-semibold text-orangeColor">
                          {{ locale === 'en' ? orderItem?.excerpt : orderItem?.excerpt_ar }}
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center justify-center">
                      <UButton
                        v-if="item?.status == 'completed' && !orderItem?.reviews"
                        variant="solid"
                        class="
                          font-secondaryFont
                          font-bold
                          mt-3
                          px-10
                          rounded-full
                          uppercase
                          box-shadow
                          border
                          text-thirdColor
                          border-primary
                          bg-primary
                          rtl:font-notoSans
                          hover:bg-orangeColor
                          hover:border-orangeColor
                          ease-in-out
                          duration-500"
                        @click="reviewOrderGroupFunc(item.id, orderItem)"
                      >
                        {{ $t('review') }}
                      </UButton>
                      <div v-if="orderItem?.reviews" class="text-center">
                        <div class="flex items-center gap-1 text-center justify-center">
                          <template v-for="star in 5" :key="star">
                            <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9.02804 16.7414L6.81621 12.2568L1.86755 11.5353C0.980115 11.4066 0.624462 10.3125 1.26803 9.68591L4.84827 6.19712L4.00147 1.26879C3.84905 0.377966 4.7873 -0.289307 5.57312 0.127316L10.0002 2.4543L14.4272 0.127316C15.213 -0.285919 16.1513 0.377966 15.9988 1.26879L15.152 6.19712L18.7323 9.68591C19.3759 10.3125 19.0202 11.4066 18.1328 11.5353L13.1841 12.2568L10.9723 16.7414C10.576 17.5408 9.42772 17.5509 9.02804 16.7414Z" 
                              :fill="star <= orderItem.reviews.rating ? '#FF671F' : 'none'" 
                              stroke="#FF671F"/>
                            </svg>
                          </template>
                        </div>
                        <div class="italic font-medium font-primaryFont text-primary rtl:font-notoSans text-[13px] mt-1">
                          {{ locale === 'en' ? orderItem?.reviews?.comment_content : orderItem?.reviews?.comment_content_ar }}
                        </div>
                      </div>
                    </div>
                    <div class="mt-3">
                      <div class="font-primaryFont text-[13px] rtl:font-notoSans text-primary flex justify-between items-center">
                        <span>{{ $t('Price x Qty.') }}</span>
                        <span class="font-semibold flex items-center gap-1 rtl:flex-row-reverse">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(orderItem.display_price)) }} x {{ orderItem.quantity }}</span>
                        </span>
                      </div>
                      <div class="font-primaryFont text-[13px] text-orangeColor flex justify-between items-center mt-1">
                        <span>{{ $t('Sub Total') }}</span>
                        <span class="font-semibold flex items-center gap-1 rtl:flex-row-reverse">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(orderItem.subtotal)) }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="text-[15px] flex items-center justify-between px-[20px] w-full">
                <div class="py-[20px] font-secondaryFont text-primary w-full">
                  <div class="flex justify-between">
                    <!-- <div>
                      <span class="mb-2 block">
                        <div class="text-[12px]">promo code</div>
                        <div
                          class="text-base font-bold font-primaryFont"
                        >
                          NEWYEAR15
                        </div>
                      </span>
                      <span class="mb-2 block">
                        <div class="text-[12px]">sub total</div>
                        <div
                          class="text-base font-bold font-primaryFont"
                        >
                          {{ formatPrice(item.total) }}
                        </div>
                      </span>
                    </div> -->
                    <div>
                      <span class="mb-2 block">
                        <div class="text-[12px]">{{ $t('Discount') }}</div>
                        <div
                          class="text-base font-bold font-primaryFont"
                        >
                          {{ formatPrice(item.total) }}
                        </div>
                      </span>
                      <span class="mb-2 block">
                        <div class="text-[12px]">{{ $t('TOTAL') }}</div>
                        <div
                          class="text-base font-bold font-primaryFont flex items-center gap-1 rtl:flex-row-reverse"
                        >
                          <span class="rtl:font-notoSans">{{ $t('EGP') }}</span> <span>{{ formatPrice(item.total) }}</span>
                        </div>
                      </span>
                    </div>
                  </div>
                  <div class="w-full px-[10px] flex flex-col gap-3 mt-3">
                    <UButton
                      class="
                        font-thirdFont
                        font-bold
                        text-[15px]
                        py-2
                        px-10
                        uppercase
                        rounded-full
                        box-shadow
                        border
                        rtl:font-notoSans
                        border-orangeColor
                        bg-orangeColor
                        hover:bg-orangeColor
                        hover:border-orangeColor
                        ease-in-out
                        duration-500
                        flex items-center justify-center
                      "
                      variant="solid"
                      :to="localePath(`/trackorder/${item.id}`)"
                    >
                      {{ $t('TRACK ORDER') }}
                    </UButton>
                    <UButton
                      class="
                        font-thirdFont
                        font-bold
                        text-[15px]
                        py-2
                        px-10
                        uppercase
                        box-shadow
                        rounded-full
                        border
                        border-primary
                        rtl:font-notoSans
                        bg-primary
                        hover:bg-primary
                        hover:border-primary
                        ease-in-out
                        duration-500
                        flex items-center justify-center
                      "
                      variant="solid"
                      @click="reviewOrderFunc(item)"
                    >
                      {{ $t('Write a review') }}
                    </UButton>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>




      </div>



















      <div class="flex justify-center mt-6 pb-10" v-if="ordersCount > 0">
        <div class="flex gap-3">
          <button
            @click="previousPage"
            :disabled="currentNumber === 1"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              uppercase
              border-2
              border-primary
              rounded-full
              flex
              items-center
              justify-center"
          >
            <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
            </svg>
            <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
            </svg>
          </button>
          <button
            v-for="pageNumber in totalPagesArray"
            :key="pageNumber"
            @click="selectPage(pageNumber)"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              uppercase
              border-2
              border-primary
              rounded-full"
            :class="{ 'bg-orangeColor !border-orangeColor text-thirdColor': currentNumber === pageNumber }"
          >
            {{ pageNumber }}
          </button>
          <button
            @click="nextPage"
            :disabled="currentNumber === paginationPages"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              border-2
              border-primary
              uppercase
              rounded-full
              flex
              items-center
              justify-center"
          >
            <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
            </svg>
            <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
            </svg>
          </button>
        </div>
      </div>



    </div>
    <div v-else class="text-center py-20 font-bold text-3xl text-orangeColor">
      {{ $t('Loading....') }}
    </div>
    <div>
      <ModalsOrderreview v-if="reviewOrder"/>
      <ModalsOrderreviewGroup v-if="reviewOrderGroup"/>
    </div>
  </div>
</template>
<script setup lang="ts">
const userData: any = useState("userData", () => [])
const loading: any = useState("loading", () => [])
const router = useRouter()

const value = ref()

const { locale } = useI18n()
const localePath = useLocalePath()

const activeItems = ref<number | null>(1)
const currentNumber: any = ref(1)

// Function to navigate to product detail page
const navigateToProduct = (item: any) => {
  // Determine product type (simple or variable)
  const productType = item.variation_id ? 'variable' : 'simple'
  // Navigate to product page
  router.push(localePath(`/product/${productType}/${item.variation_id ?? item.product_id}`))
}

const order: any = useState("order", () => {})
const paginationPages: any = useState("paginationPages", () => 0)
const ordersCount: any = useState("ordersCount", () => 0)
const perPage: any = useState("perPage", () => 10)
const page: any = useState("page", () => 1)

const statuses = ref()
const statusSelected = ref()
loading.value = false


const fetchProducts = async () => {
  loading.value = true
  const { items: stats } = await fetchHock(`newBranch/orders/getStatus`)
  statuses.value = stats.value

  const { items: orders } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&per_page=${perPage.value}&page=${page.value}`)
  ordersCount.value = orders?.value?.total_orders_count
  paginationPages.value = orders.value.total_pages

  order.value = orders.value
  loading.value = false
}

const statusChanged = async (val: number)  => {
  const { items: orders } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=${val}&per_page=${perPage.value}&page=${page.value}`)
  ordersCount.value = orders?.value?.total_orders_count
  paginationPages.value = orders.value.total_pages

  order.value = orders.value
}

const dateFormat = (date: any) => {
  const dateObj = new Date(date);

  // Extract day, month, and year
  const day = dateObj.getDate();
  const month = dateObj.getMonth() + 1;
  const year = dateObj.getFullYear();

  // Format to D-M-YYYY
  const formattedDate = `${day}-${month}-${year}`;

  return formattedDate
}

setTimeout(async () => {
  await fetchProducts()
}, 1)

// Pageination Logic
const pageinationFunc = async ()  => {
  const { items: orders } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&per_page=${perPage.value}&page=${page.value}`)
  paginationPages.value = orders.value.pages
  const filteredItems = orders?.value?.data?.filter((item: any) => item.status !== 'pending-cart')

  order.value = filteredItems
}

const selectPage = (pageNumber: number) => {
  if (currentNumber.value !== pageNumber) {
    currentNumber.value = pageNumber
    page.value = currentNumber.value
    pageinationFunc()
  }
}

// Function to go to the previous page
const previousPage = () => {
  if (currentNumber.value > 1) {
    currentNumber.value--
    page.value = currentNumber.value
    pageinationFunc()
  }
};

// Function to go to the next page
const nextPage = () => {
  if (currentNumber.value < paginationPages.value) {
    currentNumber.value++
    page.value = currentNumber.value
    pageinationFunc()
  }
}
// Limit to display 10 pages at a time
const pagesPerSet = 5;

// Function to get the current set of pages to display (Sliding Window)
const getCurrentPageSet = () => {
  const start = Math.max(1, currentNumber.value - Math.floor(pagesPerSet / 2));
  const end = Math.min(start + pagesPerSet - 1, paginationPages.value);

  // Adjust the start if the end is less than 10 pages away from paginationPages
  const adjustedStart = Math.max(1, end - pagesPerSet + 1);

  return Array.from({ length: end - adjustedStart + 1 }, (_, i) => adjustedStart + i);
}

// Get the current set of pages
const totalPagesArray = computed(() => {
  return getCurrentPageSet();
})


// Review Order Logic
const reviewModal: any = useState("reviewModal", () => false)
const reviewModalGroup: any = useState("reviewModalGroup", () => false)
const reviewOrder: any = useState("reviewOrder", () => {})
const reviewOrderGroup: any = useState("reviewOrderGroup", () => {})

const reviewOrderFunc = (id: any) => {
  reviewModal.value = true
  reviewOrder.value = id
}
const reviewOrderGroupFunc = (id: any, item: any) => {
  reviewModalGroup.value = true
  reviewOrderGroup.value = { id, ...item }
}
</script>

<style>
.myOrders-select-status {
  width: 200px;
}
.myOrders-select-status .el-select__wrapper {
  border: 1px solid #263238 !important;
  background-color: #E9EAC8 !important;
}
.myOrders-select-status .el-select__wrapper .el-select__selected-item.el-select__placeholder {
  font-style: italic;
  font-weight: 600;
  font-size: 16px;
  color: #1D3C34;
  font-family: "Montserrat", sans-serif !important;
}

/* Content */
.faq-content-container .el-collapse {
  border: none;
}

.faq-content-container .el-collapse-item__header {
  font-weight: bold;
  font-size: 20px;
  line-height: 1.2;
  color: #1D3C34;
  background-color: #E9EAC8;
  border: 1px solid #1D3C34;
  padding: 15px 30px;
  border-radius: 15px;
  text-align: start;
  height: auto;
}

.faq-content-container .el-collapse-item__header.is-active {
  border: unset;
  border-bottom: 1px solid #1D3C34;
  border-radius: unset;
}

.faq-content-container .el-collapse-item {
  overflow: hidden;
  margin-bottom: 20px;
}

.faq-content-container .el-collapse-item__header i {
  display: none;
}

.faq-content-container .el-collapse-item__wrap {
  background-color: transparent;
  border-bottom: none;
  padding: 0 30px;
  color: #1D3C34;
  font-size: 15px;
}

.faq-content-container .el-collapse-item__content {
  background-color: transparent;
  padding-bottom: 0;
  color: #1D3C34;
}

.faq-content-container .el-collapse-item.is-active {
  border: 1px solid #1D3C34;
  border-radius: 15px;
}

@media (max-width: 1024px) {
  .faq-content-container .el-collapse-item__header {
    font-size: 16px !important;
  }
}

@media (max-width: 768px) {
  .faq-content-container .el-collapse-item__header {
    font-size: 16px !important;
  }
}

@media (max-width: 640px) {
  .faq-content-container .el-collapse-item__header {
    height: fit-content;
    padding: 13px 15px;
    line-height: 1.5;
  }
}

.faq-content-container ol,
.faq-content-container ul {
  list-style: auto;
  margin-inline-start: 30px;
}
.faq-content-container.my-order-tab .el-collapse-item__wrap {
  padding: 0 !important;
}
</style>