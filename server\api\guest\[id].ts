export default defineEventHandler(async (event) => {
   try {
    const queryId: any = event.context.params.id

    // const url = `${process.env.WC_API_URL}products/${queryId}`
    const url = `${process.env.WP_API_URL}register_guest?ip=${queryId}`;
    const method = 'POST'
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

      const response = await fetch(url, {
        method: method,
        headers: headers,
      })
  
      const data = await response.json()
      return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
 })
