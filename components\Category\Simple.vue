<template>
  <div>
    <div>
      <FiltersCategorySimple @onSelect="onSelect" @onSelectForMob="onSelectForMob" />
    </div>
    <div class="mt-10 md:block hidden">
      <FiltersCategoryLogo @onSelect="onSelect" />
    </div>
    <div class="mt-10 mb-[59px]">
      <Product @updatePage="updatePage" @loadMore="loadMore" />
    </div>
  </div>
</template>

<script setup lang="ts">
// Define types for better type safety
interface FilterData {
  menus: any[];
  [key: string]: any;
}

interface ProductResponse {
  products: any[];
  pages: number;
  count: number;
}

interface QueryParams {
  [key: string]: string | string[] | undefined;
}

const route = useRoute()

// States with proper typing
const loadingCategory = useState<boolean>('loadingCategory', () => false)
const filterData = useState<FilterData>('filterData', () => ({ menus: [] }))
const selectedFilter = useState<Record<string, string>>('selectedFilter', () => ({}))
const brandId = useState<string>('brandId', () => '')
const page = useState<number>("page", () => 1)
const perPage = useState<number>("perPage", () => 8)
const paginationPages = useState<number>("paginationPages", () => 1)
const currentNumber = useState<number>("currentNumber", () => 1)
const products = useState<any[]>("products", () => [])
const isLoadMore = useState<boolean>('isLoadMore', () => false)
const loadingMobile = useState<boolean>('loadingMobile', () => false)
const loadingLoadMoreButton = useState<boolean>("loadingLoadMoreButton", () => false)
const currentBrand = ref<string>('')

// Initialize default values
page.value = 1
perPage.value = 8
paginationPages.value = 1
currentNumber.value = 1
selectedFilter.value = {}

// Fetch products data with current filters
const fetchData = async () => {
  loadingCategory.value = true
  const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')
  const pageParam = `page=${page.value}`

  try {
    const { items: responseItems } = await fetchHock(
      `refactor/simpeProduct/${Number(route.params.id)}?${pageParam}&posts_per_page=${perPage.value}&${queryString}${brandId.value ? `&brand=${brandId.value}` : ''}`
    )

    const response = responseItems.value as ProductResponse
    paginationPages.value = response.pages

    // Check if we've reached the end of available products
    if (Number(perPage.value) >= Number(response.count)) {
      isLoadMore.value = true
    } else {
      isLoadMore.value = false
    }

    products.value = response.products

    // Update URL with current parameters
    transformAndApplyQueryParamsMob(
      queryString,
      brandId.value ? `brand=${brandId.value}` : '',
      pageParam
    )
    
  } catch (error) {
    console.error('Error fetching filtered items:', error)
    products.value = []
  } finally {
    loadingCategory.value = false
  }
}

// Handle page change from pagination controls
const updatePage = async (newPage: number) => {
  page.value = newPage
  currentNumber.value = newPage
  await fetchData()
}

// Common filter update logic used by both desktop and mobile
const updateFilters = async () => {
  // Reset page when filters change
  page.value = 1
  currentNumber.value = 1

  // Only reset brand if selection hasn't changed
  if (brandId.value === currentBrand.value) {
    brandId.value = ''
  }
  currentBrand.value = brandId.value

  // Get query string from selected filters
  const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')

  // Update filter data based on new selection
  const { items: filter } = await fetchHock(
    `refactor/simpleAttributes/${Number(route.params.id)}?${queryString}${brandId.value ? `&brand=${brandId.value}` : ''}&`
  )
  filterData.value = filter.value

  // Update URL parameters
  const brand = brandId.value ? `brand=${brandId.value}` : ''
  const pageParam = `page=${page.value}`
  transformAndApplyQueryParamsMob(queryString, brand, pageParam)

  // Fetch products with new filters
  await fetchData()
}

// Handle filter selection on desktop
const onSelect = async () => {
  loadingCategory.value = true
  await updateFilters()
  loadingCategory.value = false
}

// Handle filter selection on mobile
const onSelectForMob = async () => {
  loadingCategory.value = true
  loadingMobile.value = true
  await updateFilters()
  loadingCategory.value = false
  loadingMobile.value = false
}

// Implement load more functionality for infinite scroll
const loadMore = async () => {
  // Don't proceed if already loading or at the end
  if (loadingLoadMoreButton.value || isLoadMore.value) return

  loadingLoadMoreButton.value = true

  // For mobile pagination: keep page=1 but increase posts_per_page
  perPage.value = Number(perPage.value) + 8

  const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')
  const brand = brandId.value ? `brand=${brandId.value}` : ''

  // Update URL with new posts_per_page parameter
  transformAndApplyQueryParamsMob(queryString, brand, `posts_per_page=${perPage.value}`)

  try {
    const { items: responseItems } = await fetchHock(
      `refactor/simpeProduct/${Number(route.params.id)}?page=1&posts_per_page=${perPage.value}&${queryString}${brandId.value ? `&brand=${brandId.value}` : ''}`
    )

    const response = responseItems.value as ProductResponse
    paginationPages.value = response.pages

    // Check if we've reached the end of available products
    if (Number(perPage.value) >= Number(response.count)) {
      isLoadMore.value = true
    }

    products.value = response.products
  } catch (error) {
    console.error('Error fetching more items:', error)
  } finally {
    loadingLoadMoreButton.value = false
  }
}

// Initialize filters from URL parameters
const initializeFiltersFromUrl = async () => {
  const queryParams = route.query as QueryParams

  // Set page number from URL
  if (queryParams.page) {
    const pageNum = Number(queryParams.page)
    page.value = pageNum
    currentNumber.value = pageNum
  }

  // Set brand ID from URL
  if (queryParams.brand) {
    brandId.value = queryParams.brand as string
  }

  // Set products per page from URL
  if (queryParams.posts_per_page) {
    perPage.value = Number(queryParams.posts_per_page)
  }

  // Set other filter parameters
  for (const [key, value] of Object.entries(queryParams)) {
    if (key !== 'page' && key !== 'brand' && key !== 'posts_per_page' && typeof value === 'string') {
      selectedFilter.value[key] = `${key}=${value}`
    }
  }

  // Fetch filter data with URL parameters
  const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')
  const { items: filter } = await fetchHock(
    `refactor/simpleAttributes/${Number(route.params.id)}?${queryString}${brandId.value ? `&brand=${brandId.value}` : ''}&`
  )
  filterData.value = filter.value
}

// Initial setup - Modified to respect URL parameters
if (Object.keys(route.query).length > 0) {
  await initializeFiltersFromUrl()
  await fetchData() // Use fetchData to preserve page from URL
} else {
  // Default values for fresh start
  brandId.value = ''
  page.value = 1
  currentNumber.value = 1

  // Fetch initial filter data without parameters
  const { items: filter } = await fetchHock(`refactor/simpleAttributes/${Number(route.params.id)}`)
  filterData.value = filter.value

  await fetchData()
}
</script>