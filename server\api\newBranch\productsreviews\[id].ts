export default defineEventHandler(async (event) => {
   try {
    const queryId: any = event.context.params?.id

    const url = `${process.env.WC_API_URL}get_reviews?variation_id=${queryId}`
    const method = 'GET'

    const response = await fetch(url, {
      method: method,
    })

    const data = await response.json()
    return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
 })
