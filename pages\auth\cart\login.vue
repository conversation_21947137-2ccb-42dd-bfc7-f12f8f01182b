<template>
  <div class="bg-thirdColor pb-[60px]">
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="pt-10 relative sm:max-w-[60%] mx-auto pb-5">
        <div class="md:mt-5">
          <div class="font-secondaryFont font-bold text-primary mb-10 text-center mx-auto sm:text-lg sm:max-w-[80%] hidden md:block rtl:font-notoSans">{{ $t("Welcome back! We're glad to see you again. Please log in using the form below.") }}</div>
          <div class="text-primary font-bold font-secondaryFont text-[15px] md:hidden text-center rtl:font-notoSans">{{ $t('Welcome Back, Login!') }}</div>
          <div class="mt-5">
            <div v-if="serverStatus" class="mb-2 text-red-700 font-bold text-lg rtl:font-notoSans">{{ serverErrors === $t('username does not exist') ? $t('Phone number does not exist') : serverErrors }}</div>
            <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
              <div class="w-full mx-auto flex flex-col gap-6">
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="username">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        !bg-thirdColor
                        py-2
                        px-5
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      variant="outline"
                      v-model="state.username"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.username,
                        }"
                      >
                        {{ $t('Phone Number') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="password">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        !bg-thirdColor
                        px-5
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      type="password"
                      variant="outline"
                      v-model="state.password"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.password,
                        }"
                      >
                        {{ $t('password') }}
                    </span>
                  </UFormGroup>
                </div>
              </div>
              <div class="text-center mt-6 flex gap-5 justify-center items-center">
                <UButton
                  type="submit"
                  class="
                    text-thirdColor
                    font-bold
                    font-thirdFont
                    rounded-full
                    md:text-lg
                    border
                    uppercase
                    border-primary
                    bg-primary
                    rtl:font-notoSans
                    shadow-unset
                    lg:px-[150px]
                    md:px-[100px]
                    px-10
                    lg:py-3
                    md:py-1
                    hover:scale-[1.1]
                    ease-in-out
                    duration-500
                  "
                  :loading="isLoading"
                  variant="solid"
                >
                  <div v-if="!isLoading">{{ $t('LOGIN') }}</div>
                </UButton>
                <UButton
                  class="
                    text-thirdColor
                    font-bold
                    font-thirdFont
                    uppercase
                    rounded-full
                    md:text-lg
                    border
                    rtl:font-notoSans
                    border-orangeColor
                    bg-orangeColor
                    hover:border-primary
                    shadow-unset
                    lg:px-[50px]
                    md:px-[30px]
                    px-5
                    lg:py-3
                    md:py-1
                    hover:scale-[1.1]
                    ease-in-out
                    duration-500
                  "
                  variant="solid"
                  :to="localePath('/auth/cart/register')"
                >
                  {{ $t('New User?') }}
                </UButton>
              </div>
            </UForm>
          </div>
          <div class="text-end">
            <ULink class="text-primary font-primaryFont text-sm sm:text-lg underline mt-5 rtl:font-notoSans">
              {{ $t('Forgot Password?') }}
            </ULink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const router = useRouter()
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => {})

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

const isLoading = ref<boolean>(false)

const { t } = useI18n()
const localePath = useLocalePath()

const schema = z.object({
  username: z.string()
  .min(11, t('Phone must be at least 11 characters')),
  password: z.string()
  .min(6, t('Password must be at least 6 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  username: undefined,
  password: undefined,
})

Object.assign(state, {
  username: undefined,
  password: undefined,
})


const serverErrors = ref('')
const serverStatus = ref(false)

async function onSubmit (event: FormSubmitEvent<Schema>) {
  isLoading.value = true
  serverStatus.value = false
// Do something with event.data
  const response: any = await $fetch('/api/login', {
    method: 'post',
    body: state
  }) as any

  if (response.user === null) {
    serverStatus.value = true
    serverErrors.value = response.response.msg
    isLoading.value = false
    window.scrollTo({ top: 0, behavior: 'smooth' })
    return
  }

  if (response.response.login) {
    const orders = ref()
    const completeOrder = localStorage.getItem("completeOrder")
    orders.value = completeOrder ? JSON.parse(completeOrder) : null

    const { items } = await fetchHock(`orders/${Number(response.user.data.ID)}?status=pending-cart`)
    if (items?.value?.single[0]?.id) {
      await $fetch(`/api/orders/delete/${items?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any

      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    } else {
      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    }
    // Delete Old Order
    const { items: usersData } = await fetchHock(`shipping/${Number(response.user.data.ID)}`)
    userData.value = usersData.value

    tokenCookie.value = response.user.data.user_pass
    userIdCookie.value = response.user.data.ID
    // Redirect to the home page
    router.push(localePath('/checkout'))
  }
}
</script>
<style>
.login-register input {
  background-color: #F5F5F5;
}
</style>