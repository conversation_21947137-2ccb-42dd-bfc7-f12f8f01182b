<template>
  <div>
    <div class="bg-thirdColor border-y rtl:font-notoSans border-primary text-center py-3 font-bold font-secondaryFont text-[14px] mt-10 text-primary uppercase md:hidden">
      {{ $t('ACCOUNT PASSWORD') }}
    </div>
    <div class="px-[30px] md:px-0">
      <div class="text-secondary font-bold font-secondaryFont text-[22px] uppercase hidden md:block mt-10 rtl:font-notoSans">{{ $t('ACCOUNT PASSWORD') }}</div>
      <div class="mt-7 custom-text-input-responsive">
        <div v-if="serverError" class="mb-1 text-red-700 text-lg font-primaryFont rtl:font-notoSans">
          {{ serverError }}
        </div>
        <UForm ref="form" :schema="changePasswordSchema" :state="changePasswordState" class="md:space-y-4" @submit="changePassword">
          <div class="w-full mx-auto flex flex-col gap-4">
            <div class="custom-text-input custom-text-input-bg-white">
              <div class="relative">
                <UFormGroup name="old_password">
                  <div class="relative text-field">
                    <input
                      class="w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                      variant="outline"
                      v-model="changePasswordState.old_password"
                      required
                      :type="showOldPassword ? 'text' : 'password'"
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        rtl:font-notoSans
                        ltr:left-[22px]
                        rtl:right-[22px]
                        top-[13px]
                        transition-all
                        duration-100
                        !bg-white
                        ease-in-out"
                        :class="{
                          'placeholder-animation-with-bg !bg-white' : changePasswordState.old_password,
                        }"
                      >
                        {{ $t('current password') }}
                    </span>
                    <ClientOnly>
                      <font-awesome-icon
                        @click="showOldPassword = !showOldPassword"
                        class="text-primary absolute top-[24%] right-[20px] text-2xl cursor-pointer"
                        icon="fa-solid fa-eye"
                      />
                    </ClientOnly>
                  </div>
                </UFormGroup>
              </div>
              <div class="relative">
                <UFormGroup name="new_password" class="mt-4">
                  <div class="relative text-field">
                    <input
                      class="w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                      variant="outline"
                      v-model="changePasswordState.new_password"
                      required
                      :type="showNewPassword ? 'text' : 'password'"
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        rtl:font-notoSans
                        ltr:left-[22px]
                        rtl:right-[22px]
                        top-[13px]
                        transition-all
                        duration-100
                        !bg-white
                        ease-in-out"
                        :class="{
                          'placeholder-animation-with-bg !bg-white' : changePasswordState.new_password,
                        }"
                      >
                        {{ $t('NEW password') }}
                    </span>
                    <ClientOnly>
                      <font-awesome-icon
                        @click="showNewPassword = !showNewPassword"
                        class="text-primary absolute top-[24%] right-[20px] text-2xl cursor-pointer"
                        icon="fa-solid fa-eye"
                      />
                    </ClientOnly>
                  </div>
                </UFormGroup>
              </div>
            </div>
          </div>
          <div class="flex justify-center">
            <UButton
              type="submit"
              class="
                font-thirdFont
                font-bold
                text-lg
                text-thirdColor
                py-2
                px-20
                mt-6
                rounded-full
                border
                rtl:font-notoSans
                bg-primary
                border-primary
                hover:border-orangeColor
                hover:bg-orangeColor
                ease-in-out
                duration-500
                uppercase
              "
              variant="solid"
              :loading="loading"
            >
              <span v-if="!loading">{{ $t('UPDATE') }}</span>
            </UButton>
          </div>
        </UForm>
      </div>
      <div class="text-center rtl:font-notoSans font-primaryFont text-[12px] md:text-lg font-bold text-orangeColor mt-4">
        <NuxtLink :to="localePath('/auth/forgot-password')">
          {{ $t('FORGOT PASSWORD?') }}
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { ElNotification } from 'element-plus'

const userData: any = useState("userData", () => [])
const loading = ref(false)
const showNewPassword = ref(false)
const showOldPassword = ref(false)
const { t } = useI18n()
const localePath = useLocalePath()

// Update Password
const changePasswordSchema = z.object({
  old_password: z.string()
  .min(6, 'Old Password Must be at least 6 characters'),
  new_password: z.string()
  .min(6, 'Old Password Must be at least 6 characters'),
})

type ChangePasswordSchema = z.output<typeof changePasswordSchema>

const changePasswordState = reactive({
  old_password: undefined,
  new_password: undefined,
  user_id: userData?.value?.id,
})

const serverError = ref<string>('')

const successPopUp = (message: string) => {
  ElNotification({
    // title: message,
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, message),
    // type: 'success',
    position: 'top-right',
  })
}

async function changePassword (event: FormSubmitEvent<ChangePasswordSchema>) {
  loading.value = true
  // // Do something with event.data
  const response = await $fetch(`/api/newBranch/profile/updatePassword`, {
    method: 'post',
    body: changePasswordState
  }) as any

  if (response === 'The current password you entered is incorrect') {
    serverError.value = response
  } else {
    serverError.value = ''
    changePasswordState.old_password = undefined
    changePasswordState.new_password = undefined
    successPopUp(t('Password Changed Successfully'))
  }
  loading.value = false
}
</script>
