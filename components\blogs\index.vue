<template>
  <div class="relative">
    <div class="md:hidden sticky rtl:font-notoSans z-[55] top-0 left-0 w-full font-secondaryFont uppercase text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
      {{ $t('Discover tips & tricks') }}
    </div>
    <div class="px-[20px] max-w-[1280px] mx-auto mt-10 mb-10" ref="scrollContainer">
      <div v-if="!loadingBlogs" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 flex-wrap gap-4 justify-start">
        <!-- v-for="(item, index) in products ?? []" :key="item.id"
          > -->
        <div v-for="(item, index) in blogs" :key="item.id">
          <div>
            <ULink :to="localePath(`/tips-and-tricks/${item.id}`)">
              <div class="relative truncate rounded-[30px] border border-primary">
                <img class="w-full h-[200px]" :src="item?.custom_large_crop || blog3" />
                <div class="bg-primary">
                  <div class="py-8 px-8">
                    <div class="text-thirdColor italic text-xl font-semibold truncate"> {{ locale === 'en' ? item?.title?.rendered : item?.title_ar }} </div>
                    <div class="text-wrap text-thirdColor mt-4 font-thirdFont text-sm blogs-text-limit" v-html="locale === 'en' ? item?.excerpt?.rendered : item?.content_ar"></div>
                  </div>
                </div>
              </div>
            </ULink>
          </div>
        </div>
      </div>
      <el-skeleton animated v-else class="mb-10">
        <template #template>
          <div
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 flex-wrap gap-4 justify-start"
          >
            <div v-for="(item, index) in blogs.length ? blogs : [1, 2, 3]" :key="typeof item === 'number' ? index : item.id" >
              <div class="w-full">
                <div class="relative truncate rounded-[30px] h-[447px]">
                  <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[200px]"/>
                  <div class="">
                    <el-skeleton-item variant="text" style="width: 100%; height: 250px;" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
      <div class="justify-center mt-10 pb-20 border-primary hidden md:flex">
        <div class="flex gap-3">
          <button
            @click="previousPage"
            :disabled="currentNumber === 1"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              uppercase
              h-[40px]
              border-2
              border-primary
              rounded-full
              flex
              items-center
              justify-center"
          >
            <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
            </svg>
            <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
            </svg>
          </button>
          <button
            v-for="pageNumber in totalPagesArray"
            :key="pageNumber"
            @click="selectPage(pageNumber)"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              uppercase
              border-2
              border-primary
              rounded-full"
            :class="{ 'bg-orangeColor !border-orangeColor text-thirdColor': currentNumber === pageNumber }"
          >
            {{ pageNumber }}
          </button>
          <button
            @click="nextPage"
            :disabled="currentNumber === paginationPages"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              border-2
              border-primary
              uppercase
              rounded-full
              flex
              items-center
              justify-center"
          >
            <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
            </svg>
            <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div class="px-[30px] md:hidden" v-if="loadingLoadMoreButton">
      <el-skeleton animated>
        <template #template>
          <div
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 flex-wrap gap-4 justify-start"
          >
            <div v-for="(item, index) in blogs.length ? blogs : [1, 2, 3]" :key="typeof item === 'number' ? index : item.id" >
              <div class="w-full">
                <div class="relative truncate rounded-[30px] h-[447px]">
                  <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[200px]"/>
                  <div class="">
                    <el-skeleton-item variant="text" style="width: 100%; height: 250px;" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import blog3 from '~/assets/images/home/<USER>/3.png'

/**
 * Blog post interface that matches the WordPress API response structure
 */
interface BlogPost {
  id: number;
  title_ar?: string;
  content_ar?: string;
  title: {
    rendered?: string;
    rendered_ar?: string;
  };
  excerpt: {
    rendered?: string;
    rendered_ar?: string;
  };
  content: {
    rendered: string;
    rendered_ar?: string;
  };
  custom_large_crop?: string;
  link?: string;
  date?: string;
}

// Define emit types for better type safety
const emit = defineEmits<{
  (e: 'updatePage', page: number): void;
  (e: 'loadMore'): void;
}>()

const { locale } = useI18n()
const localePath = useLocalePath()

// State variables with proper types
const loadingBlogs = useState<boolean>('loadingBlogs', () => false)
const loadingLoadMoreButton = useState<boolean>("loadingLoadMoreButton", () => false)
const isLoadMore = useState<boolean>('isLoadMore', () => false)
const paginationPages = useState<number>("paginationPages", () => 1)
const currentNumber = useState<number>("currentNumber", () => 1)
const blogs = useState<BlogPost[]>("blogs", () => [])

/**
 * Selects a specific page and emits the updatePage event
 * @param pageNumber The page number to select
 */
const selectPage = (pageNumber: number): void => {
  if (currentNumber.value !== pageNumber) {
    currentNumber.value = pageNumber
    emit('updatePage', pageNumber)
  }
}

/**
 * Emits the loadMore event to trigger loading more posts
 */
const loadMorePosts = (): void => {
  emit('loadMore')
}

/**
 * Navigates to the previous page if not on the first page
 */
const previousPage = (): void => {
  if (currentNumber.value > 1) {
    currentNumber.value--
    emit('updatePage', currentNumber.value)
  }
}

/**
 * Navigates to the next page if not on the last page
 */
const nextPage = (): void => {
  if (currentNumber.value < paginationPages.value) {
    currentNumber.value++
    emit('updatePage', currentNumber.value)
  }
}

// Number of pages to display in the pagination control
const pagesPerSet = 5

/**
 * Calculates which page numbers to display in the pagination control
 * Uses a sliding window approach to keep the current page centered when possible
 * @returns Array of page numbers to display
 */
const getCurrentPageSet = (): number[] => {
  // Calculate the start page, ensuring the current page is centered when possible
  const start = Math.max(1, currentNumber.value - Math.floor(pagesPerSet / 2))

  // Calculate the end page, ensuring we don't exceed the total number of pages
  const end = Math.min(start + pagesPerSet - 1, paginationPages.value)

  // Adjust the start if we're near the end to always show pagesPerSet pages when possible
  const adjustedStart = Math.max(1, end - pagesPerSet + 1)

  // Create an array of page numbers from adjustedStart to end
  return Array.from({ length: end - adjustedStart + 1 }, (_, i) => adjustedStart + i)
}

/**
 * Computed property that returns the current set of page numbers to display
 */
const totalPagesArray = computed<number[]>(() => {
  return getCurrentPageSet()
})

/**
 * Computed property to determine if the current device is a desktop
 */
const isDesktop = computed<boolean>(() => window.innerWidth >= 768);

/**
 * Debounce function to limit how often a function can be called
 * @param fn The function to debounce
 * @param delay The delay in milliseconds
 * @returns A debounced version of the function
 */
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;

  return function(this: any, ...args: Parameters<T>): void {
    if (timeout) clearTimeout(timeout);

    timeout = setTimeout(() => {
      fn.apply(this, args);
      timeout = null;
    }, delay);
  };
};

/**
 * Ref to track if we're currently loading more posts
 * This prevents multiple simultaneous load operations
 */
const isCurrentlyLoading = ref<boolean>(false);

/**
 * Function to detect if user has scrolled to the bottom of the page
 * and trigger loading more posts if needed
 */
const handleScroll = (): void => {
  // If already loading or all posts loaded, don't proceed
  if (isCurrentlyLoading.value || loadingLoadMoreButton.value || isLoadMore.value) {
    return;
  }

  const container = document.documentElement || document.body;
  // Check if user has scrolled to within 100px of the bottom
  const bottomReached = container.scrollTop + window.innerHeight >= container.scrollHeight - 100;

  if (bottomReached) {
    isCurrentlyLoading.value = true;
    loadMorePosts();

    // Reset the loading flag after a delay to prevent multiple calls
    // The 1000ms delay ensures we don't trigger multiple loads
    setTimeout(() => {
      isCurrentlyLoading.value = false;
    }, 1000);
  }
};

// Create a debounced version of the scroll handler with 200ms delay
const debouncedHandleScroll = debounce(handleScroll, 200);

// Lifecycle hooks for event management
onMounted(() => {
  // Only add scroll listener on mobile devices
  if (!isDesktop.value) {
    window.addEventListener('scroll', debouncedHandleScroll);
  }
});

// Clean up event listeners when component is unmounted
onBeforeUnmount(() => {
  window.removeEventListener('scroll', debouncedHandleScroll);
});
</script>

<style scoped>
.blogs-text-limit {
   overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 7; /* number of lines to show */
           line-clamp: 7;
   -webkit-box-orient: vertical;
}
</style>
