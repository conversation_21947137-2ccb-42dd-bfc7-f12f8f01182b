<template>
  <div class="font-primaryFont">
    <div class="bg-thirdColor border-y border-primary text-center py-3 font-bold font-secondaryFont rtl:font-notoSans text-[14px] mt-10  mb-7 text-primary uppercase md:hidden">
      {{ $t('Notification options') }}
    </div>
    <div class="text-secondary font-bold font-secondaryFont text-[22px] uppercase hidden md:block mt-10 rtl:font-notoSans mb-7">{{ $t('Notification options') }}</div>
    <div class="px-[30px] md:px-0">
      <div v-for="option in notificationOptions" :key="option.type" class="border border-primary rounded-md p-7 mt-3 ">
        <div class="flex justify-between items-center">
          <label class="font-bold text-primary text-xl" :for="option.type">{{ $t(option.type) }}</label>
          <div class="flex items-center">
            <input
              v-if="!loading"
              class="notifiction-input"
              type="checkbox"
              v-model="option.selected"
              :id="option.type"
              @change="handleSelection(option)"
              :disabled="loading"
            />
            <span v-if="loading" class="loading-spinner"></span>
          </div>
        </div>
        <div class="font-medium mt-1">
          <span class="text-primary pe-2 border-e border-primary rtl:font-notoSans">{{ option.value }}</span>
          <span class="ms-2" :class="option.verified ? 'verified' : 'not-verified'">
            {{ option.verified ? $t('Verified') : $t('Not Verified') }}
          </span>
        </div>
      </div>
      <p v-if="errorMessage" class="error-message rtl:font-notoSans">{{ errorMessage }}</p>
    </div>
    <ModalsAuthSocialVerify v-if="showModal" />
    <ModalsAuthEmailVerifyModal v-if="showEmailModal" />
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'

const profileData: any = useState("profileData", () => {})
const phoneNumber: any = useState("phoneNumber", () => '')
const showModal: any = useState("showModal", () => false)
const showEmailModal: any = useState("showEmailModal", () => false)
const loading = ref(false)
const { t } = useI18n()

phoneNumber.value = ''

const successPopUp = (message: string) => {
  ElNotification({
    // title: 'Create order',
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, message),
    // type: 'success',
    position: 'top-right',
  })
}

const notificationOptions = reactive([
  {
    type: 'EMAIL',
    value: profileData?.value?.email,
    verified: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'email_verify') ? true : false,
    selected: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_email' && meta.value) ? true : false,
  },
  {
    type: 'WHATSAPP',
    value: /^\d+$/.test(profileData?.value?.username) ? profileData?.value?.username : '',
    verified: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'mobile_verify') ? true : false,
    selected: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_whatsapp' && meta.value) ? true : false,
  },
  {
    type: 'SMS',
    value: /^\d+$/.test(profileData?.value?.username) ? profileData?.value?.username : '',
    verified: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'mobile_verify') ? true : false,
    selected: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_sms' && meta.value) ? true : false,
  },
])

const errorMessage = ref('')

const handleSelection = async (selectedOption: { type: string; selected: boolean; verified: boolean }) => {
  loading.value = true; // Start loading
  try {
    const selectedCount = notificationOptions.filter(option => option.selected).length;

    if (selectedCount > 2) {
      selectedOption.selected = false;
      errorMessage.value = t('Please unselect one option to select another.');
      return;
    } else {
      errorMessage.value = '';
      if (selectedOption.type === 'EMAIL') {
        if (!selectedOption.verified) {
          selectedOption.selected = false;
          showEmailModal.value = true
          return;
        } else {
          await $fetch('/api/newBranch/verify/sendVerifyedMethods', {
            method: 'post',
            body: {
              user_id: profileData?.value?.id,
              email: selectedOption.selected ? 'True' : 'False',
              whatsapp: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_whatsapp' && meta.value) ? 'True' : 'False',
              sms: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_sms' && meta.value) ? 'True' : 'False'
            }
          })
          const { items: prof } = await fetchHock(`newBranch/address/${Number(profileData?.value?.id)}`)

          profileData.value = prof.value
          successPopUp(t('Notification Updated Successfully'))
        }
      } else if (selectedOption.type === 'WHATSAPP') {
        if (!selectedOption.verified) {
          selectedOption.selected = false;
          showModal.value = true
          return;
        } else {
          await $fetch('/api/newBranch/verify/sendVerifyedMethods', {
            method: 'post',
            body: {
              user_id: profileData?.value?.id,
              email: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_email' && meta.value) ? 'True' : 'False',
              whatsapp: selectedOption.selected ? 'True' : 'False',
              sms: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_sms' && meta.value) ? 'True' : 'False'
            }
          })
          const { items: prof } = await fetchHock(`newBranch/address/${Number(profileData?.value?.id)}`)
          profileData.value = prof.value
          successPopUp(t('Notification Updated Successfully'))
        }
      } else if (selectedOption.type === 'SMS') {
        if (!selectedOption.verified) {
          selectedOption.selected = false;
          showModal.value = true
          return;
        } else {
          await $fetch('/api/newBranch/verify/sendVerifyedMethods', {
            method: 'post',
            body: {
              user_id: profileData?.value?.id,
              email: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_email' && meta.value) ? 'True' : 'False',
              whatsapp: profileData?.value?.meta_data?.find((meta: any) => meta.key === 'notify_whatsapp' && meta.value) ? 'True' : 'False',
              sms: selectedOption.selected ? 'True' : 'False'
            }
          })
          const { items: prof } = await fetchHock(`newBranch/address/${Number(profileData?.value?.id)}`)
          profileData.value = prof.value
          successPopUp(t('Notification Updated Successfully'))
        }
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false; // Stop loading
  }
};

watch(
  () => profileData?.value?.meta_data,
  (newMetaData) => {
    notificationOptions.forEach(option => {
      if (option.type === 'EMAIL') {
        option.verified = newMetaData?.find((meta: any) => meta.key === 'email_verify') ? true : false;
        option.selected = newMetaData?.find((meta: any) => meta.key === 'notify_email' && meta.value) ? true : false;
      } else if (option.type === 'WHATSAPP') {
        option.verified = newMetaData?.find((meta: any) => meta.key === 'mobile_verify') ? true : false;
        option.selected = newMetaData?.find((meta: any) => meta.key === 'notify_whatsapp' && meta.value) ? true : false;
      } else if (option.type === 'SMS') {
        option.verified = newMetaData?.find((meta: any) => meta.key === 'mobile_verify') ? true : false;
        option.selected = newMetaData?.find((meta: any) => meta.key === 'notify_sms' && meta.value) ? true : false;
      }
    });
  },
  { deep: true }
);

watch(
  () => profileData?.value,
  (newProfileData) => {
    notificationOptions.forEach(option => {
      if (option.type === 'EMAIL') {
        option.value = newProfileData?.email || '';
        option.verified = newProfileData?.meta_data?.find((meta: any) => meta.key === 'email_verify') ? true : false;
      } else if (option.type === 'WHATSAPP' || option.type === 'SMS') {
        option.value = /^\d+$/.test(newProfileData?.username) ? newProfileData?.username : '';
        option.verified = newProfileData?.meta_data?.find((meta: any) => meta.key === 'mobile_verify') ? true : false;
      }
    });
  },
  { deep: true }
);
</script>

<style scoped>
.notifiction-input {
  width: 20px;
  height: 20px;
  accent-color: #1D3C34;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-top-color: #1D3C34;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.verified {
  color: #2e7d32;
}

.not-verified {
  color: #d32f2f;
}

.error-message {
  color: #d32f2f;
  font-weight: bold;
  margin-top: 10px;
}
</style>
