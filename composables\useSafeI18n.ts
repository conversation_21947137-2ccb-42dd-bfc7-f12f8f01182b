export const useSafeI18n = () => {
  try {
    const i18n = useI18n()
    return {
      locale: i18n.locale,
      t: i18n.t,
      d: i18n.d,
      n: i18n.n,
      availableLocales: i18n.availableLocales
    }
  } catch (error) {
    // Fallback if i18n is not available
    return {
      locale: ref('en'),
      t: (key: string) => key,
      d: (value: any) => value,
      n: (value: any) => value,
      availableLocales: ['en', 'ar']
    }
  }
} 