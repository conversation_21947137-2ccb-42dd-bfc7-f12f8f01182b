<template>
  <div>
    <div class="px-[30px]">
      <h2 class="text-primary font-bold text-[15px] font-secondaryFont text-center mt-8">{{ $t('Payment Options') }}</h2>
      <div class="flex flex-col gap-3 mt-4 pb-7 border-b border-primary">
        <UButton
          class="
            mt-2
          text-thirdColor
            w-full
            flex
            text-[13px]
            justify-between
            items-center
            font-bold
            font-secondaryFont
            rounded-full
            uppercase
            border
            p-3
            border-primary
            bg-primary
            focus:bg-primary
            focus:border-primary
            shadow-unset
            ease-in-out
            duration-500
          "
          :class="{ 'opacity-50': selectedPaymentMethod === 'cash' }"
          variant="solid"
          @click="handlePaymentMethodSelect('visa')"
          >
          <div>{{ $t('PAY BY credit and debit cards') }}</div>
          <div class="flex gap-2">
            <!-- SVGs for card icons (copy from PaymentInformation.vue) -->
            <svg width="35" height="28" viewBox="0 0 35 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M29.2231 22.8326C29.2231 23.2441 28.9447 23.5407 28.5453 23.5407C28.1338 23.5407 27.8676 23.226 27.8676 22.8326C27.8676 22.4393 28.1338 22.1246 28.5453 22.1246C28.9447 22.1246 29.2231 22.4393 29.2231 22.8326ZM10.4148 22.1246C9.98512 22.1246 9.737 22.4393 9.737 22.8326C9.737 23.226 9.98512 23.5407 10.4148 23.5407C10.8081 23.5407 11.0744 23.2441 11.0744 22.8326C11.0684 22.4393 10.8081 22.1246 10.4148 22.1246ZM17.5254 22.1064C17.1986 22.1064 16.9989 22.3183 16.9505 22.6329H18.1063C18.0519 22.288 17.8401 22.1064 17.5254 22.1064ZM24.049 22.1246C23.6375 22.1246 23.3894 22.4393 23.3894 22.8326C23.3894 23.226 23.6375 23.5407 24.049 23.5407C24.4605 23.5407 24.7268 23.2441 24.7268 22.8326C24.7268 22.4393 24.4605 22.1246 24.049 22.1246ZM30.4576 23.7041C30.4576 23.7222 30.4758 23.7343 30.4758 23.7706C30.4758 23.7888 30.4576 23.8009 30.4576 23.8372C30.4395 23.8554 30.4395 23.8675 30.4274 23.8856C30.4092 23.9038 30.3971 23.9159 30.3608 23.9159C30.3427 23.934 30.3306 23.934 30.2942 23.934C30.2761 23.934 30.264 23.934 30.2277 23.9159C30.2095 23.9159 30.1974 23.8977 30.1793 23.8856C30.1611 23.8675 30.149 23.8554 30.149 23.8372C30.1309 23.8069 30.1309 23.7888 30.1309 23.7706C30.1309 23.7404 30.1672 23.6557 30.1793 23.6375C30.1974 23.6193 30.2095 23.6193 30.2277 23.6072C30.2579 23.5891 30.2761 23.5891 30.2942 23.5891C30.3245 23.5891 30.3427 23.5891 30.3608 23.6072C30.3911 23.6254 30.4092 23.6254 30.4274 23.6375C30.4455 23.6496 30.4395 23.6738 30.4576 23.7041ZM30.3245 23.7888C30.3548 23.7888 30.3548 23.7706 30.3729 23.7706C30.3911 23.7525 30.3911 23.7404 30.3911 23.7222C30.3911 23.7041 30.3911 23.692 30.3729 23.6738C30.3548 23.6738 30.3427 23.6557 30.3063 23.6557H30.2095V23.8675H30.2579V23.7827H30.2761L30.3427 23.8675H30.3911L30.3245 23.7888ZM34.8571 2.90476V24.2063C34.8571 25.81 33.5561 27.1111 31.9524 27.1111H2.90476C1.30109 27.1111 0 25.81 0 24.2063V2.90476C0 1.30109 1.30109 0 2.90476 0H31.9524C33.5561 0 34.8571 1.30109 34.8571 2.90476ZM3.87302 11.3528C3.87302 15.9822 7.63105 19.7342 12.2545 19.7342C13.9005 19.7342 15.5163 19.238 16.8839 18.3363C12.4723 14.7477 12.5026 7.97599 16.8839 4.3874C15.5163 3.47966 13.9005 2.98948 12.2545 2.98948C7.63105 2.98343 3.87302 6.74147 3.87302 11.3528ZM17.4286 17.9369C21.6949 14.6085 21.6768 8.12123 17.4286 4.7747C13.1804 8.12123 13.1622 14.6146 17.4286 17.9369ZM8.81716 22.5543C8.81716 22.0278 8.47222 21.6828 7.92758 21.6647C7.64921 21.6647 7.35268 21.7494 7.15298 22.058C7.00774 21.8099 6.75962 21.6647 6.41468 21.6647C6.18472 21.6647 5.95476 21.7494 5.77321 21.9915V21.7252H5.27698V23.9461H5.77321C5.77321 22.8024 5.62192 22.1186 6.31786 22.1186C6.93512 22.1186 6.81409 22.7358 6.81409 23.9461H7.29216C7.29216 22.8387 7.14087 22.1186 7.83681 22.1186C8.45407 22.1186 8.33304 22.7237 8.33304 23.9461H8.82927V22.5543H8.81716ZM11.5343 21.7252H11.0562V21.9915C10.8929 21.7918 10.6629 21.6647 10.3482 21.6647C9.7249 21.6647 9.24683 22.1609 9.24683 22.8326C9.24683 23.5104 9.7249 24.0006 10.3482 24.0006C10.6629 24.0006 10.8929 23.8856 11.0562 23.6738V23.9522H11.5343V21.7252ZM13.9852 23.2744C13.9852 22.3667 12.5994 22.7782 12.5994 22.3546C12.5994 22.0096 13.3195 22.0641 13.7189 22.288L13.9187 21.8946C13.3498 21.5255 12.0911 21.5315 12.0911 22.3909C12.0911 23.2563 13.4769 22.8932 13.4769 23.2986C13.4769 23.6799 12.6599 23.6496 12.2242 23.347L12.0124 23.7283C12.6902 24.1882 13.9852 24.0914 13.9852 23.2744ZM16.1275 23.8372L15.9943 23.4257C15.7644 23.5528 15.2561 23.692 15.2561 23.1776V22.173H16.0488V21.7252H15.2561V21.0474H14.7598V21.7252H14.2999V22.167H14.7598V23.1776C14.7598 24.2427 15.8067 24.049 16.1275 23.8372ZM16.9323 23.0263H18.5965C18.5965 22.0459 18.1487 21.6586 17.5436 21.6586C16.9021 21.6586 16.4422 22.1367 16.4422 22.8266C16.4422 24.0672 17.8098 24.2729 18.4876 23.6859L18.2576 23.3228C17.7856 23.7101 17.0715 23.6738 16.9323 23.0263ZM20.5088 21.7252C20.2305 21.6042 19.8068 21.6163 19.589 21.9915V21.7252H19.0928V23.9461H19.589V22.6935C19.589 21.9915 20.1639 22.0822 20.3636 22.1851L20.5088 21.7252ZM21.1503 22.8326C21.1503 22.1428 21.8523 21.9188 22.403 22.3243L22.6329 21.931C21.931 21.3803 20.6541 21.6828 20.6541 22.8387C20.6541 24.0369 22.0096 24.279 22.6329 23.7464L22.403 23.3531C21.8462 23.7464 21.1503 23.5104 21.1503 22.8326ZM25.1867 21.7252H24.6905V21.9915C24.1882 21.3258 22.8811 21.701 22.8811 22.8326C22.8811 23.9945 24.2366 24.3274 24.6905 23.6738V23.9522H25.1867V21.7252ZM27.2261 21.7252C27.0809 21.6526 26.5604 21.5497 26.3063 21.9915V21.7252H25.8282V23.9461H26.3063V22.6935C26.3063 22.0278 26.8509 22.0701 27.0809 22.1851L27.2261 21.7252ZM29.6649 20.8235H29.1868V21.9915C28.6906 21.3318 27.3774 21.6828 27.3774 22.8326C27.3774 24.0066 28.739 24.3213 29.1868 23.6738V23.9522H29.6649V20.8235ZM30.1248 16.2788V16.5571H30.1732V16.2788H30.2882V16.2304H30.0098V16.2788H30.1248ZM30.5242 23.7706C30.5242 23.7404 30.5242 23.7041 30.5061 23.6738C30.4879 23.6557 30.4758 23.6254 30.4576 23.6072C30.4395 23.5891 30.4092 23.577 30.3911 23.5588C30.3608 23.5588 30.3245 23.5407 30.2942 23.5407C30.2761 23.5407 30.2458 23.5588 30.2095 23.5588C30.1793 23.577 30.1611 23.5891 30.143 23.6072C30.1127 23.6254 30.0945 23.6557 30.0945 23.6738C30.0764 23.7041 30.0764 23.7404 30.0764 23.7706C30.0764 23.7888 30.0764 23.819 30.0945 23.8554C30.0945 23.8735 30.1127 23.9038 30.143 23.9219C30.1611 23.9401 30.1732 23.9522 30.2095 23.9703C30.2398 23.9885 30.2761 23.9885 30.2942 23.9885C30.3245 23.9885 30.3608 23.9885 30.3911 23.9703C30.4092 23.9522 30.4395 23.9401 30.4576 23.9219C30.4758 23.9038 30.4879 23.8735 30.5061 23.8554C30.5242 23.819 30.5242 23.7888 30.5242 23.7706ZM30.7179 16.2243H30.6331L30.5363 16.4361L30.4395 16.2243H30.3548V16.5511H30.4032V16.303L30.5 16.5148H30.5666L30.6513 16.303V16.5511H30.7179V16.2243ZM30.9841 11.3528C30.9841 6.74147 27.2261 2.98343 22.6027 2.98343C20.9566 2.98343 19.3409 3.47966 17.9732 4.38135C22.3364 7.96994 22.403 14.7598 17.9732 18.3303C19.3409 19.238 20.9688 19.7282 22.6027 19.7282C27.2261 19.7342 30.9841 15.9822 30.9841 11.3528Z" fill="white"/></svg>
            <svg width="36" height="28" viewBox="0 0 36 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M29.1199 12.0608C29.1199 12.0608 29.5798 14.312 29.6827 14.784H27.6615C27.8612 14.2454 28.6297 12.1516 28.6297 12.1516C28.6176 12.1697 28.8294 11.6009 28.9505 11.2499L29.1199 12.0608ZM35.5285 2.90476V24.2064C35.5285 25.81 34.2274 27.1111 32.6238 27.1111H3.57615C1.97248 27.1111 0.671387 25.81 0.671387 24.2064V2.90476C0.671387 1.30109 1.97248 0 3.57615 0H32.6238C34.2274 0 35.5285 1.30109 35.5285 2.90476ZM9.90006 18.1064L13.7247 8.71429H11.1527L8.77446 15.129L8.51424 13.8279L7.66702 9.50705C7.52783 8.90794 7.09817 8.7385 6.56563 8.71429H2.65026L2.60789 8.90189C3.56405 9.14395 4.41732 9.49494 5.16166 9.93671L7.32813 18.1064H9.90006ZM15.6128 18.1185L17.1378 8.71429H14.705L13.1861 18.1185H15.6128ZM24.0789 15.0443C24.091 13.9731 23.4375 13.1562 22.0395 12.4844C21.1863 12.0548 20.6658 11.7643 20.6658 11.3225C20.6779 10.9231 21.1076 10.5116 22.0637 10.5116C22.8565 10.4935 23.4375 10.6811 23.8732 10.8687L24.091 10.9715L24.4239 8.9382C23.9458 8.7506 23.1833 8.53879 22.2453 8.53879C19.8428 8.53879 18.1544 9.82173 18.1423 11.6493C18.1242 12.9988 19.3526 13.7492 20.2725 14.2031C21.2105 14.663 21.5312 14.9656 21.5312 15.371C21.5191 16.0004 20.7687 16.2909 20.0728 16.2909C19.1045 16.2909 18.5841 16.1396 17.7913 15.7886L17.4706 15.6373L17.1317 17.7493C17.7006 18.0095 18.7535 18.2395 19.8428 18.2516C22.3966 18.2576 24.0608 16.9929 24.0789 15.0443ZM32.6238 18.1185L30.6631 8.71429H28.781C28.2001 8.71429 27.7583 8.88373 27.5102 9.49494L23.8974 18.1185H26.4511C26.4511 18.1185 26.8687 16.9566 26.9595 16.7084H30.0821C30.1547 17.0413 30.3726 18.1185 30.3726 18.1185H32.6238Z" fill="white"/></svg>
          </div>
        </UButton>
        <UButton
          class="
            mt-2
          text-thirdColor
            w-full
            flex
            text-[13px]
            justify-between
            items-center
            font-bold
            font-secondaryFont
            rounded-full
            p-3
            uppercase
            border
            border-primary
            bg-primary
            focus:bg-primary
            focus:border-primary
            shadow-unset
            ease-in-out
            duration-500
          "
          :class="{ 'opacity-50': selectedPaymentMethod === 'visa' }"
          variant="solid"
          @click="handlePaymentMethodSelect('cash')"
        >
          <div>{{ $t('CASH ON DELIVERY') }}</div>
        </UButton>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123]">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans flex justify-center gap-1 items-center">
        <span class="rtl:mt-0.5">{{ $t('YOUR CART') }} ({{ totalItemss || '0' }})</span> |
        <span class="rtl:flex rtl:gap-2 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span>
        </span>
      </div>
      <div class="flex justify-center">
        <UButton
          class="rounded-full flex items-center rtl:font-notoSans justify-center font-bold text-[15px] w-full py-2 font-thirdFont text-thirdColor uppercase bg-orangeColor border-2 border-orangeColor hover:bg-orangeColor disabled:bg-orangeColor hover:border-orangeColor transition-all duration-500 ease-in-out"
          :disabled="!selectedPaymentMethod"
          :to="selectedPaymentMethod ? localePath('/cart/mobile/checkout') : undefined"
        >
          {{ $t('PLACE ORDER') }}
        </UButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const cartItems: any = useState("cartItems", () => ({}))
const selectedPaymentMethod: any = useState("selectedPaymentMethod", () => '')

const localePath = useLocalePath()

const totalItemss = ref(0)

totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)

watch(cartItems, async (current) => {
  if (cartItems?.value?.single?.[0]?.items?.length) {
    totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
      return acc + Number(item.quantity || 0)
    }, 0)
  }
})

const handlePaymentMethodSelect = (method: string) => {
  selectedPaymentMethod.value = method
  
  // Save selected payment method to localStorage
  if (process.client) {
    localStorage.setItem("selectedPaymentMethod", method)
  }
}
</script>
<style scoped>
</style>