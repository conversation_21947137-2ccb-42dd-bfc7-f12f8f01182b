/**
 * Interface for navigation links
 */
export interface NavigationLink {
  /**
   * Display name of the link
   */
  name: string;
  
  /**
   * Display name of the link
   */
  name_ar: string;
  
  /**
   * URL slug for the link
   */
  slug: string;
  
  /**
   * Category ID (0 for home page)
   */
  id: number;
  
  /**
   * Icon URL (false if no icon)
   */
  icon: string | false;
  
  /**
   * API endpoint for products (optional)
   */
  products?: string;
  
  /**
   * API endpoint for submenu (optional)
   */
  submenu?: string;
  
  /**
   * Product type (simple or variable)
   */
  product_type?: 'simple' | 'variable';
  
  /**
   * Description for the category
   */
  description?: string;
}

/**
 * Type for navigation links array
 */
export type NavigationLinks = NavigationLink[];
