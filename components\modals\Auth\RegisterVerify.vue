<template>
  <div>
    <UModal v-model="showVerificationModal" class="custom-color-for-modal">
      <div class="px-4 md:px-14 py-3 md:py-8 bg-thirdColor mb-[50px] md:mb-0">
        <h2 class="md:text-xl font-bold font-secondaryFont text-secondary mb-4 uppercase rtl:font-notoSans">{{ $t('verify phone number') }}</h2>
        <!-- Existing verification code inputs -->
        <div class="space-y-4">
          <div class="flex items-center justify-between gap-2">
            <template v-for="(_, index) in 6" :key="index">
              <div class="relative">
                <input
                  :ref="el => inputRefs[index] = el"
                  v-model="verificationCode[index]"
                  type="text"
                  :disabled="!codeSent"
                  maxlength="1"
                  :class="[
                    'md:w-12 md:h-12 w-[35px] h-[35px] border border-primary rounded text-center text-xl font-bold focus:outline-none relative z-10',
                    codeSent ? 'bg-thirdColor' : 'bg-gray-300 text-gray-500' // Change color if disabled
                  ]"
                  @input="handleInput(index)"
                  @keydown="handleKeydown($event, index)"
                />
                <span
                  class="
                    absolute
                    top-1/2
                    left-1/2
                    transform
                    -translate-x-1/2
                    -translate-y-1/2
                    text-primary
                    font-bold
                    text-2xl
                    pointer-events-none
                    z-0
                    transition-opacity
                    duration-200
                  "
                  :class="{
                    'opacity-0': verificationCode[index],
                    'opacity-100': !verificationCode[index]
                  }"
                >
                  -
                </span>
              </div>
            </template>
          </div>
          <div v-if="error" class="font-secondaryFont rtl:font-notoSans" style="color: #FF0101;">{{ $t('The code is incorrect. Please try again.') }}</div>
          <div v-if="success" class="font-secondaryFont rtl:font-notoSans" style="color: #4B9560;">{{ $t('The code is correct, and your number has been successfully verified.') }}</div>




        <!-- Add verification method selection -->
        <div class="mb-6 border-b border-primary">
          <div class="flex flex-col gap-3 mb-4">
            <label class="flex cursor-pointer">
              <input
                type="radio"
                v-model="verificationMethod"
                value="sms"
                class="hidden"
              />
              <span class="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center mr-2">
                <span
                  class="w-3 h-3 rounded-full bg-primary"
                  :class="{ 'opacity-0': verificationMethod !== 'sms' }"
                ></span>
              </span>
              <span class="text-primary font-secondaryFont md:text-base text-[13px] rtl:font-notoSans"><span class="font-bold">{{ $t('SMS') }}</span> {{ $t('“Receive a code via text message.”') }}</span>
            </label>
            <label class="flex cursor-pointer">
              <input
                type="radio"
                v-model="verificationMethod"
                value="whatsapp"
                class="hidden"
              />
              <span class="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center mr-2">
                <span
                  class="w-3 h-3 rounded-full bg-primary"
                  :class="{ 'opacity-0': verificationMethod !== 'whatsapp' }"
                ></span>
              </span>
              <span class="text-primary font-secondaryFont md:text-base text-[13px] rtl:font-notoSans"><span class="font-bold">{{ $t('WhatsApp') }}</span> {{ $t('“Receive a code via WhatsApp message.”') }}</span>
            </label>
          </div>
        </div>

        <div class="font-secondaryFont md:text-base text-[13px] rtl:font-notoSans" style="color: #1E1E1E;">
          {{ $t('You’ll receive a 6 digit code via your preferred method (SMS or WhatsApp). Enter the code above to complete the verification. The code is valid for 10 minutes.') }}
        </div>












          <div class="flex justify-center">
            <UButton
              class="bg-orangeColor text-thirdColor uppercase font-bold md:text-base text-[14px] md:py-3 py-2 px-8 md:px-14 rounded-full rtl:font-notoSans"
              @click="handleSendCode"
              :disabled="isCountdownActive || !verificationMethod"
              :loading="verifyLoading"
            >
              {{ $t('SEND CODE') }}
            </UButton>
          </div>
          <div class="text-center font-secondaryFont text-primary text-[13px]">
            {{ isCountdownActive ? `${$t('You can resend the code in')} ${countdown} ${$t('seconds.')}` : $t('You can now resend the code.') }}
          </div>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { UserData } from '~/types/user'
import type { CartData } from '~/types/defaultCart'
import { ElNotification } from 'element-plus'
import { safelyAccessCSSRules } from '~/utils/cssHelper'

const { t } = useI18n()
const localePath = useLocalePath()

const router = useRouter()
import { persistentCookieOptions } from '~/utils/cookieOptions'
const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

const userData = useState<UserData>("userData", () => ({}))
const cartItems = useState<CartData>("cartItems", () => ({}))

const showVerificationModal: any = useState("showVerificationModal", () => true)
const verificationCode = ref(Array(6).fill(''))
const inputRefs = ref<any>([])
const isCompleted = computed(() => verificationCode.value.every(digit => digit !== ''))
const codeStatus: any = useState("codeStatus", () => false)
const phoneNumber: any = useState("phoneNumber", () => '')
const error = ref(false)
const success = ref(false)
const formStatus: any = useState("formStatus", () => null)
const verifyLoading = ref(false)
const codeSent = ref(false) // Track if the code has been sent
const verificationMethod = ref('')
const countdown = ref(0)
const countdownInterval = ref<NodeJS.Timeout | null>(null)

// Computed property to check if countdown is active
const isCountdownActive = computed(() => {
  // Check local state first
  if (countdown.value > 0) return true

  // Then check localStorage as a fallback
  if (phoneNumber.value) {
    const savedEndTime = localStorage.getItem(`verification_countdown_${phoneNumber.value}`)
    if (savedEndTime) {
      const endTime = parseInt(savedEndTime)
      return endTime > Date.now()
    }
  }

  return false
})


const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  ElNotification({
    message: h('i', {
      style: `color: ${type === 'success' ? '#1D3C34' : '#ff4949'}; font-weight: bold;`
    }, message),
    position: 'top-right',
    duration: 3000,
    type
  })
}

const handleInput = async (index: number) => {
  // Ensure single character
  if (verificationCode.value[index]?.length > 1) {
    verificationCode.value[index] = verificationCode.value[index].slice(0, 1)
  }

  // Auto focus next input
  if (verificationCode.value[index] && index < 5) {
    inputRefs.value[index + 1]?.focus()
  }

  // Check if all digits are filled
  if (isCompleted.value) {
    verifyLoading.value = true
    const verified: any = await $fetch('/api/register/verifyCode', {
      method: 'post',
      body: {
        phoneNumber: `+2${phoneNumber.value}`,
        code: verificationCode.value.join('')
      }
    })

    if (!verified.valid) {
      error.value = true
      success.value = false
      codeStatus.value = false
      verificationCode.value = Array(6).fill('')
      inputRefs.value[0]?.focus()
      showNotification(t('The code is incorrect. Please try again.'), 'success')

    } else {
      codeStatus.value = true
      success.value = true
      error.value = false

      const loginResponse = await $fetch('/api/login', {
        method: 'post',
        body: {
          username: formStatus.value.username,
          password: formStatus.value.password
        }
      }) as any

      // Verify the user account
      await $fetch('/api/register/customVerify', {
        method: 'post',
        body: {
          user_id: loginResponse.user.data.ID
        }
      })

      if (loginResponse.response.login) {

        // Set cookies
        tokenCookie.value = loginResponse?.user?.data?.user_pass
        userIdCookie.value = loginResponse?.user?.data?.ID

        // Show success message
        showNotification(t('Login successful!'), 'success')

        // Fetch user data and cart items in parallel
        const userId = Number(loginResponse?.user?.data?.ID)
        const [ordersResponse, userDataResponse] = await Promise.all([
          fetchHock(`newBranch/orders/${userId}`),
          fetchHock(`newBranch/customer/${userId}`)
        ])

        // Update state
        userData.value = userDataResponse.items.value
        cartItems.value = ordersResponse.items.value
      }
      // Close modal
        closeModal()
    }
    verifyLoading.value = false
  }
}

const handleKeydown = (event: KeyboardEvent, index: number) => {
  // Handle backspace
  if (event.key === 'Backspace' && !verificationCode.value[index] && index > 0) {
    verificationCode.value[index - 1] = ''
    inputRefs.value[index - 1]?.focus()
  }
}

const closeModal = () => {
  router.push(localePath('/'))
  showVerificationModal.value = false
  verificationCode.value = Array(6).fill('')
}

const sendVerificationCode = async () => {
  try {
    if (verificationMethod.value === 'whatsapp') {
      await $fetch('/api/register/whatsAppVerify', {
        method: 'post',
        body: {
          phoneNumber: `+2${phoneNumber.value}`
        }
      })
      showNotification(`${t('The code is sent to')} ${phoneNumber.value}.`, 'success')
    } else {
      await $fetch('/api/register/smsVerify', {
        method: 'post',
        body: {
          phoneNumber: `+2${phoneNumber.value}`
        }
      })
      showNotification(`${t('The code is sent to')} ${phoneNumber.value}.`, 'success')
    }
  } catch (error) {
    console.error('Failed to send verification code:', error)
  }
}

const startCountdown = () => {
  // Calculate end time (current time + 120 seconds)
  const endTime = Date.now() + 120 * 1000

  // Store end time in localStorage with phone number as part of the key
  // This ensures different phone numbers have different countdowns
  localStorage.setItem(`verification_countdown_${phoneNumber.value}`, endTime.toString())

  // Set initial countdown value
  countdown.value = 120 // 2 minutes in seconds

  // Clear any existing interval
  if (countdownInterval.value) clearInterval(countdownInterval.value)

  // Start the countdown interval
  countdownInterval.value = setInterval(() => {
    // Calculate remaining time
    const remaining = Math.max(0, Math.floor((endTime - Date.now()) / 1000))
    countdown.value = remaining

    // Clear interval when countdown reaches zero
    if (remaining <= 0 && countdownInterval.value) {
      clearInterval(countdownInterval.value)
      // Remove from localStorage when expired
      localStorage.removeItem(`verification_countdown_${phoneNumber.value}`)
    }
  }, 1000)
}

const handleSendCode = async () => {
  try {
    // If countdown is active, don't allow sending a new code
    if (isCountdownActive.value) {
      // Just make sure the input fields are enabled
      codeSent.value = true
      return
    }

    // Send verification code
    await sendVerificationCode()

    // Set to true after sending the code
    codeSent.value = true

    // Start countdown timer
    startCountdown()
  } catch (error) {
    console.error('Failed to send code:', error)
  }
}

// Clean up interval when component is unmounted
onBeforeUnmount(() => {
  if (countdownInterval.value) clearInterval(countdownInterval.value)
})

onMounted(() => {
  // Safely handle CSS-related operations
  safelyAccessCSSRules(() => {
    // Apply any necessary CSS styles safely
    const modalElements = document.querySelectorAll('.custom-color-for-modal');
    modalElements.forEach(element => {
      const el = element as HTMLElement;
      if (el && el.style) {
        // Apply any necessary style changes safely
        el.style.transition = 'all 0.3s ease';
      }
    });

    // Apply styles to input fields
    const inputElements = document.querySelectorAll('input[type="text"]');
    inputElements.forEach(element => {
      const el = element as HTMLElement;
      if (el && el.style) {
        el.style.transition = 'all 0.3s ease';
      }
    });
  });

  // Focus first input when modal opens
  inputRefs.value[0]?.focus()

  // Check if there's a saved countdown for this phone number
  if (phoneNumber.value) {
    const savedEndTime = localStorage.getItem(`verification_countdown_${phoneNumber.value}`)

    if (savedEndTime) {
      const endTime = parseInt(savedEndTime)
      const now = Date.now()

      // If the end time is in the future, resume the countdown
      if (endTime > now) {
        // Calculate remaining time in seconds
        const remaining = Math.floor((endTime - now) / 1000)

        // If there's still time left, set the countdown and start the interval
        if (remaining > 0) {
          countdown.value = remaining
          codeSent.value = true // Enable input fields

          // Start the countdown interval
          countdownInterval.value = setInterval(() => {
            // Calculate remaining time
            const currentRemaining = Math.max(0, Math.floor((endTime - Date.now()) / 1000))
            countdown.value = currentRemaining

            // Clear interval when countdown reaches zero
            if (currentRemaining <= 0 && countdownInterval.value) {
              clearInterval(countdownInterval.value)
              // Remove from localStorage when expired
              localStorage.removeItem(`verification_countdown_${phoneNumber.value}`)
            }
          }, 1000)
        } else {
          // Countdown has expired, remove it from localStorage
          localStorage.removeItem(`verification_countdown_${phoneNumber.value}`)
        }
      } else {
        // Countdown has expired, remove it from localStorage
        localStorage.removeItem(`verification_countdown_${phoneNumber.value}`)
      }
    }
  }
})

watch(() => showVerificationModal.value, async (newVal) => {
  if (!newVal) {
    const loginResponse = await $fetch('/api/login', {
      method: 'post',
      body: {
        username: formStatus.value.username,
        password: formStatus.value.password
      }
    }) as any

    if (loginResponse.response.login) {

      // Set cookies
      tokenCookie.value = loginResponse?.user?.data?.user_pass
      userIdCookie.value = loginResponse?.user?.data?.ID

      // Show success message
      showNotification(t('Login successful!'), 'success')

      // Fetch user data and cart items in parallel
      const userId = Number(loginResponse?.user?.data?.ID)
      const [ordersResponse, userDataResponse] = await Promise.all([
        fetchHock(`newBranch/orders/${userId}`),
        fetchHock(`newBranch/customer/${userId}`)
      ])

      // Update state
      userData.value = userDataResponse.items.value
      cartItems.value = ordersResponse.items.value
      router.push(localePath('/'))
    }
  }
})
</script>

<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

input {
  background: transparent;
}

.custom-color-for-modal .fixed.transition-opacity {
  background-color: #1D3C34D9 !important;
}
</style>