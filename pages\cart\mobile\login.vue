<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="pt-10 relative mx-auto border-b border-primary pb-8">
        <div>
          <div class="text-primary font-bold font-secondaryFont md:hidden text-center rtl:font-notoSans">{{ $t('Welcome Back, Login!') }}</div>
          <div class="mt-5">
            <div v-if="serverStatus" class="mb-2 text-red-700 font-bold text-[15px] rtl:font-notoSans">{{ serverErrors === $t('username does not exist') ? $t('Phone number does not exist') : serverErrors }}</div>
            <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
              <div class="w-full mx-auto flex flex-col gap-4">
                <div class="text-field-nobg-mob relative">
                <UFormGroup name="username">
                  <input
                    class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                    variant="outline"
                    v-model="state.username"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-primaryFont
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      text-[14px]
                      ltr:left-[25px]
                      rtl:right-[25px]
                      rtl:font-notoSans
                      top-[10px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-mob' : state.username,
                      }"
                    >
                      {{ $t('Phone Number') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="relative text-field-nobg-mob">
                <UFormGroup name="password">
                  <input
                    class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                    variant="outline"
                    v-model="state.password"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-primaryFont
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      text-[14px]
                      ltr:left-[25px]
                      rtl:right-[25px]
                      rtl:font-notoSans
                      top-[10px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-mob' : state.password,
                      }"
                    >
                      {{ $t('Password') }}
                  </span>
                </UFormGroup>
              </div>
              </div>
              <div class="text-center !mt-8 !mb-0 flex gap-5 justify-center items-center">
                <UButton
                  type="submit"
                  class="
                    text-thirdColor
                    font-bold
                    font-thirdFont
                    rounded-full
                    text-[15px]
                    border
                    uppercase
                    border-primary
                    bg-primary
                    shadow-unset
                    w-[70%]
                    rtl:font-notoSans
                    flex items-center justify-center
                    py-2
                    hover:scale-[1.1]
                    ease-in-out
                    duration-500
                  "
                  :loading="isLoading"
                  variant="solid"
                >
                  <div v-if="!isLoading">{{ $t('LOGIN') }}</div>
                </UButton>
              </div>
            </UForm>
          </div>
          <div class="text-center">
            <ULink class="text-orangeColor font-primaryFont text-[15px] underline mt-3 rtl:font-notoSans">
              {{ $t('Forgot Password?') }}
            </ULink>
          </div>
        </div>
        <div class="text-center flex gap-5 justify-center items-center">
        </div>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123] md:hidden">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans flex justify-center gap-1 items-center">
        <span class="rtl:mt-0.5">{{ $t('YOUR CART') }} ({{ totalItems || '0' }})</span> |
        <span class="rtl:flex rtl:gap-2 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span>
        </span>
      </div>
      <div class="flex justify-center">
        <UButton
          class="
            rounded-full
            flex
            items-center
            justify-center
            font-bold
            text-[15px]
            w-full
            py-2
            font-thirdFont
            text-thirdColor
            uppercase
            bg-orangeColor
            border-2
            rtl:font-notoSans
            border-orangeColor
            hover:bg-orangeColor
            hover:border-orangeColor
            focus:bg-orangeColor
            focus:border-orangeColor
            transition-all
            duration-500
            ease-in-out"
            :to="localePath('/cart/mobile/userinfo')"
        >
          {{ $t('BACK TO GUEST') }}
        </UButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const router = useRouter()
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => {})
const { locale, t } = useI18n()
const localePath = useLocalePath()

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

const isLoading = ref<boolean>(false)

const schema = z.object({
  username: z.string()
  .min(11, t('Phone must be at least 11 characters')),
  password: z.string()
  .min(6, t('Password must be at least 6 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  username: undefined,
  password: undefined,
})

Object.assign(state, {
  username: undefined,
  password: undefined,
})


const serverErrors = ref('')
const serverStatus = ref(false)

async function onSubmit (event: FormSubmitEvent<Schema>) {
  isLoading.value = true
  serverStatus.value = false
// Do something with event.data
  const response: any = await $fetch('/api/login', {
    method: 'post',
    body: state
  }) as any

  if (response.user === null) {
    serverStatus.value = true
    serverErrors.value = response.response.msg
    isLoading.value = false
    window.scrollTo({ top: 0, behavior: 'smooth' })
    return
  }

  if (response.response.login) {
    const orders = ref()
    const completeOrder = localStorage.getItem("completeOrder")
    orders.value = completeOrder ? JSON.parse(completeOrder) : null

    const { items } = await fetchHock(`orders/${Number(response.user.data.ID)}?status=pending-cart`)
    if (items?.value?.single[0]?.id) {
      await $fetch(`/api/orders/delete/${items?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any

      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    } else {
      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    }
    // Delete Old Order
    const { items: usersData } = await fetchHock(`shipping/${Number(response.user.data.ID)}`)
    userData.value = usersData.value

    tokenCookie.value = response.user.data.user_pass
    userIdCookie.value = response.user.data.ID
    // Redirect to the home page
    router.push(localePath('/cart/mobile/address'))
  }
}

const totalItems = ref(0)

totalItems.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)
</script>