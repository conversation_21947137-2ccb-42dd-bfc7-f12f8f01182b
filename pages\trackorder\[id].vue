<template>
  <div>
    <div class="mt-[60px] hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="bg-cartColor rounded-2xl p-10 text-black">
          <div class="flex gap-40 justify-between items-center">
            <div class="font-secondaryFont text-sm sm:text-lg text-primary rtl:font-notoSans">
              {{ $t('Rest assured, your order is on its way! Keep an eye on your inbox for updates or track your shipment directly on our website.') }}
            </div>
            <UButton
              class="
                rounded-full
                flex
                items-center
                justify-center
                font-bold
                rtl:font-notoSans
                sm:text-xl
                py-3
                sm:px-20
                px-14
                font-thirdFont
                text-thirdColor
                uppercase
                bg-orangeColor
                border-2
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                transition-all
                duration-500
                ease-in-out"
              :to="localePath('/profile?tab=history')"
            >
              {{ $t('LIST OF ORDERS') }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
    <div class="md:hidden bg-thirdColor px-[30px] border-b border-primary py-2">
      <div class="font-secondaryFont text-[12px] font-bold text-primary uppercase text-center">{{ $t('track order') }} #{{ route.params.id }}</div>
    </div>
    <ClientOnly>
      <Trackorder v-if="!endsWithG" :order-data="trackOrderData" />
      <TrackorderGroup v-if="endsWithG" :order-data="trackOrderData" />
    </ClientOnly>
  </div>
</template>
<script setup lang="ts">
import type { UserData } from '~/types/user'

const route = useRoute()
const userData = useState<UserData>("userData", () => ({} as UserData))
const trackOrderData = useState<any>("trackOrderData", () => ({} as any))
const localePath = useLocalePath()

/**
 * Check if order ID ends with -G
 */
const orderId = route.params.id as string
const endsWithG = orderId.endsWith('-G')

/**
 * Fetch order data based on order type
 */
const { data } = await useAsyncData('orderData', async () => {
  const endpoint = endsWithG 
    ? `newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&order_type=group&order_id=${route.params.id}`
    : `newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&order_type=sinlge&order_id=${Number(route.params.id)}`
  
  const { items } = await fetchHock(endpoint)
  return endsWithG ? items?.value?.grouped?.[0] || {} : items?.value?.single?.[0] || {}
})

// Update the trackOrderData state with the fetched data
trackOrderData.value = data.value
</script>