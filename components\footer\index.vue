<template>
  <div>
    <div class="py-9 bg-secondary hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="grid lg:grid-cols-4 gap-10 lg:gap-4 ltr:italic font-roboto">
          <div>
            <div class="text-lg font-secondaryFont rtl:text-2xl text-primary font-bold uppercase">{{ locale === 'en' ? footer?.section_left?.section_title : footer?.section_left?.section_title_ar ? footer?.section_left?.section_title_ar : footer?.section_left?.sction_title_ar }}</div>
            <ul class="mt-4">
              <li v-for="(item, index) in footer?.section_left?.links ?? []" :key="index">
                <ULink
                    inactive-class="py-0 rtl:text-xl text-primaryFont ltr:italic text-primary font-medium"
                    active-class="py-0 rtl:text-xl text-primaryFont ltr:italic text-thirdColor font-medium"
                    :to="localePath(`/${item?.slug}`)"
                    v-html="locale === 'en' ? item?.name : item?.name_ar"
                  >
                  </ULink>
              </li>
            </ul>
          </div>
          <div>
            <div class="text-lg font-secondaryFont rtl:text-2xl text-primary font-bold uppercase">{{ locale === 'en' ? footer?.section_middle?.section_title : footer?.section_middle?.section_title_ar ? footer?.section_middle?.section_title_ar : footer?.section_middle?.sction_title_ar }}</div>
            <ul class="mt-4">
              <li v-for="(item, index) in footer?.section_middle?.links ?? []" :key="index">
                <ULink
                    inactive-class="py-0 rtl:text-xl text-primaryFont italic text-primary  font-medium"
                    active-class="py-0 rtl:text-xl text-primaryFont italic text-thirdColor font-medium"
                    :to="localePath(`/${item?.slug}`)"
                    v-html="locale === 'en' ? item?.name : item?.name_ar"
                  >
                  </ULink>
              </li>
            </ul>
          </div>
          <div class="col-span-2 lg:ms-10 lg:ps-10">
            <div class="text-lg font-secondaryFont rtl:text-2xl text-primary font-bold uppercase">{{ locale === 'en' ? footer?.section_right?.section_title : footer?.section_right?.section_title_ar ? footer?.section_right?.section_title_ar : footer?.section_right?.sction_title_ar }}</div>
            <div class="footer-input relative">
              <el-input class="mt-4" v-model="input" style="width: 100%" :placeholder="locale === 'en' ? footer?.section_right?.links[0]?.placeholder : footer?.section_right?.links[0]?.placeholder_ar" />
              <div
                class="
                  px-2
                  sm:px-16
                  py-[7px]
                  sm:py-[13px]
                  cursor-pointer
                  font-semibold
                  text-thirdColor
                  bg-primary
                  not-italic
                  text-xs
                  sm:text-base
                  absolute
                  top-[33%]
                  sm:top-[24%]
                  ltr:right-0
                  rtl:left-0
                  rounded-e-[30px]
                  border
                  border-primary"
                @click="sendEmail"
                >
              {{ locale === 'en' ? footer?.section_right?.links[1]?.button : footer?.section_right?.links[1]?.button_ar }}
              </div>
            </div>
            <div class="text-[12px] italic font-medium mt-4">
              {{ locale === 'en' ? footer?.section_right?.links[2]?.desc : footer?.section_right?.links[2]?.desc_ar }}
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="bg-primary py-4 hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="flex justify-between items-center">
            <div class="font-thirdFont text-xs text-thirdColor" v-html="locale === 'en' ? footer?.bottom_footer?.copyrights : footer?.bottom_footer?.copyrights_ar">
            </div>
          <div class="flex gap-3">
            <div class="cursor-pointer" v-for="(item, index) in footer?.bottom_footer?.socia_media ?? []" :key="index">
              <div>
                <a :href="item?.url" target="_blanc">
                  <img class="h-[25px]" :src="item?.icon" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>




    <div class="relative md:hidden w-full h-[59px]">
      <div class="fixed bottom-0 left-0 w-full z-[66] bg-thirdColor px-[30px] py-4 flex items-center justify-between">
        <ULink 
          active-class="border-r border-primary custom-active-footer border-left-none w-full flex items-center justify-center"
          inactive-class="border-r border-primary border-left-none w-full flex items-center justify-center"
          v-for="item in navLinks?.footer_menu ?? []"
          :to="localePath(`${item?.slug === 'tips-and-tricks' ? '/tips-and-tricks' : '/category/'+item?.id}`)"
        >
          <img class="w-[40px] h-[40px]" :src="item?.icon ? item?.icon : ''" :alt="item?.name" />
        </ULink>
      </div>
    </div>


    
    
  </div>
</template>

<script setup lang="ts">
import type { Footer } from '~/types/footer'
import type { UserData } from '~/types/user'
import type { MenuStructure } from '~/types/navLinks'
import { ElNotification } from 'element-plus'

const { locale, t } = useI18n()
const localePath = useLocalePath()
const input = ref('')
const userData = useState<UserData>("userData", () => ({}))

const footer = useState<Footer>("footer", () => ({} as Footer))

const navLinks = useState<MenuStructure>("navLinks", () => ({} as MenuStructure))

// Function to send email to the API
const sendEmail = async () => {
  if (!input.value) {
    ElNotification({
      // title: 'Create order',
      message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Please enter an email address.'),
      // type: 'success',
      position: 'top-right',
    })
    return;
  }
  const data = {
    email: input.value,
    first_name: userData?.value?.first_name,
    last_name: userData?.value?.last_name,
    phone_number: /^\d+$/.test(userData?.value?.username ?? '') ? userData?.value?.username : null
  }
  const dataEmail = {
    email: input.value,
  }

  if (userData?.value?.first_name) {
    const response: any = await $fetch('/api/newBranch/news', {
      method: 'post',
      body: data
    })

    if (response.status == 202) {
      ElNotification({
        // title: 'Create order',
        message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Email Subscribed Successfully.'),
        // type: 'success',
        position: 'top-right',
      })
      input.value = ''; // Clear the input field
    } else {
      ElNotification({
        // title: 'Create order',
        message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Invalid Email.'),
        // type: 'success',
        position: 'top-right',
      })
    }
  } else {
    const response: any = await $fetch('/api/newBranch/news', {
      method: 'post',
      body: dataEmail
    })

    if (response.status == 202) {
      ElNotification({
        // title: 'Create order',
        message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Email Subscribed Successfully.'),
        // type: 'success',
        position: 'top-right',
      })
      input.value = ''; // Clear the input field
    } else {
      ElNotification({
        // title: 'Create order',
        message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Invalid Email.'),
        // type: 'success',
        position: 'top-right',
      })
    }
  }
};
</script>

<style>
.custom-active-footer svg path {
  fill: #4B9560;
}

.border-left-none:last-child {
  border: none !important;
}
</style>