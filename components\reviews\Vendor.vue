<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">   
    <div class="text-secondary font-secondaryFont uppercase text-lg font-bold">
      Customers reviews
    </div>     
    <div class="relative sm:px-[50px] mt-2 sm:mt-7">
      <div class="top-slider-arrows hidden sm:block">
        <div
          class="
            swiper-button-prev-outside
            cursor-pointer
            absolute
            top-[50%]
            left-0
          "
          @click="swiperInstance.slideNext()"
        >
          <svg width="16" height="27" viewBox="0 0 16 27" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.5 1L2 13.5L14.5 26" stroke="#1D3C34" stroke-width="2"/>
          </svg>
        </div>
        <div
          class="
            swiper-button-next-outside
            cursor-pointer
            absolute
            top-[50%]
            right-0
          " 
          @click="swiperInstance.slidePrev()"
        >
          <svg width="16" height="27" viewBox="0 0 16 27" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.5 26L14 13.5L1.5 0.999998" stroke="#1D3C34" stroke-width="2"/>
          </svg>
        </div>
      </div>
      <Swiper
        @swiper="onSwiper"
        :modules="[ SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
        :loop="true"
        :space-between="20"
        :autoplay="{
          delay: 4000,
          disableOnInteraction: false,
        }"
        :breakpoints="{
          360: {
              slidesPerView: 1,
          },
          640: {
              slidesPerView: 1,
          },
          768: {
              slidesPerView: 1,
          },
          830: {
              slidesPerView: 2,
          },
          1024: {
              slidesPerView: 2,
          },
          1400: {
              slidesPerView: 2,
          },
          1600: {
              slidesPerView: 2,
          },
          1920: {
              slidesPerView: 2,
          },
          2560: {
              slidesPerView: 2,
          },
          3200: {
              slidesPerView: 2,
          },
        }"
      >
        <SwiperSlide v-for="(item, index) in items" :key="index">
          <div class="mx-auto w-full">
            <div
              class="
                border
                border-primary
                border-dashed
                rounded-md
                py-5
                px-5
                bg-filter
                overflow-hidden
                md:min-h-[350px]
                lg:min-h-[300px]
                xl:min-h-[277px]
              "
            >
              <div class="flex flex-col sm:flex-row sm:items-center font-medium text-primary text-xl uppercase">
                <div class="sm:pe-[13px] sm:h-[20px] mb-[6px] sm:border-r border-primary">{{ item.name }}</div>
                <div class="sm:ps-[14px] sm:h-[20px] mb-[6px]">{{ item.date }}</div>
              </div>
              <div class="card-rating sm:mt-3 mb-3 sm:mb-0">
                <el-rate v-model="item.rate" size="large" allow-half disabled />
              </div>
              <div>
                <p class="text-black font-lg">
                  {{ item.description }}
                </p>
              </div>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
    </div>
  </div>
</template>
<script setup lang="ts">
const items = [
  { name: 'Eslam foula', date: '27.05.2024', rate: 4, description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.' },
  { name: 'SHERIF RAFIK', date: '27.04.2024', rate: 2, description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.' },
  { name: 'Eslam foula', date: '27.05.2024', rate: 4, description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.' },
  { name: 'SHERIF RAFIK', date: '27.04.2024', rate: 2, description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.' },
]


// Swipper settings
const swiperInstance = ref()
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.recentlySwiper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', onMouseOver)
    slide.addEventListener('mouseleave', onMouseLeave)
    slide.addEventListener('touchstart', onMouseOver)
    slide.addEventListener('touchend', onMouseLeave)
  })
})

function onMouseOver(event: any) {
  swiperInstance.value.autoplay.stop()
}

function onMouseLeave(event: any) {
  swiperInstance.value.autoplay.start()
}
</script>

<style>
.card-rating .el-rate  {
  height: 20px;
  --el-rate-disabled-void-color: #0f030338;
  --el-rate-icon-size: 22px;
  --el-rate-icon-margin: 0;
}
.custom-ratingg .el-rate {
  height: 20px;
  --el-rate-disabled-void-color: #0f030338;
  --el-rate-icon-size: 30px;
  --el-rate-icon-margin: -6px;
}
</style>