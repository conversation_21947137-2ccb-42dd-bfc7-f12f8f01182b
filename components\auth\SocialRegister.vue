<template>
  <div class="pt-5 relative sm:max-w-[60%] mx-auto">
    <div class="flex justify-between items-center flex-col-reverse md:flex-row gap-5">
      <div class="text-primary font-bold text-lg">
        Register using Social Media 
      </div>
      <div class="flex gap-2 items-center">
        <div class="w-[50px] h-[50px] rounded-full bg-white border border-primary flex items-center justify-center">
          <UButton
            to="/api/auth/googleregister"
            external
            class="bg-transparent hover:bg-transparent"
          >
            <svg width="20" height="21" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23.8281 12.3926C23.8281 19.3018 19.0967 24.2188 12.1094 24.2188C5.41016 24.2188 0 18.8086 0 12.1094C0 5.41016 5.41016 0 12.1094 0C15.3711 0 18.1152 1.19629 20.2295 3.16895L16.9336 6.33789C12.6221 2.17773 4.60449 5.30273 4.60449 12.1094C4.60449 16.333 7.97852 19.7559 12.1094 19.7559C16.9043 19.7559 18.7012 16.3184 18.9844 14.5361H12.1094V10.3711H23.6377C23.75 10.9912 23.8281 11.5869 23.8281 12.3926Z" fill="#1D3C34"/>
            </svg>
          </UButton>
        </div>
        <div class="w-[50px] h-[50px] rounded-full bg-white border border-primary flex items-center justify-center">
          <UButton
            to="/api/auth/facebookregister"
            external
            class="bg-transparent hover:bg-transparent"
          >
            <svg width="25" height="25" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M30 15.0913C30 6.75456 23.2863 0 15 0C6.71371 0 0 6.75456 0 15.0913C0 22.6235 5.48528 28.8669 12.6562 30V19.4538H8.84577V15.0913H12.6562V11.7663C12.6562 7.98438 14.8942 5.89533 18.3218 5.89533C19.9633 5.89533 21.6798 6.18986 21.6798 6.18986V9.90183H19.7879C17.925 9.90183 17.3438 11.0653 17.3438 12.2586V15.0913H21.5038L20.8385 19.4538H17.3438V30C24.5147 28.8669 30 22.6235 30 15.0913Z" fill="#1D3C34"/>
            </svg>
          </UButton>
        </div>
        <div class="w-[50px] h-[50px] rounded-full bg-white border border-primary flex items-center justify-center">
          <UButton
            to="/api/auth/facebook"
            external
            class="bg-transparent hover:bg-transparent"
          >
            <svg width="25" height="25" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M30 15.0913C30 6.75456 23.2863 0 15 0C6.71371 0 0 6.75456 0 15.0913C0 22.6235 5.48528 28.8669 12.6562 30V19.4538H8.84577V15.0913H12.6562V11.7663C12.6562 7.98438 14.8942 5.89533 18.3218 5.89533C19.9633 5.89533 21.6798 6.18986 21.6798 6.18986V9.90183H19.7879C17.925 9.90183 17.3438 11.0653 17.3438 12.2586V15.0913H21.5038L20.8385 19.4538H17.3438V30C24.5147 28.8669 30 22.6235 30 15.0913Z" fill="#1D3C34"/>
            </svg>
          </UButton>
        </div>
        <!-- <div class="w-[50px] h-[50px] rounded-full bg-white border border-primary flex items-center justify-center">
          <svg width="21" height="25" viewBox="0 0 26 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21.0775 15.8533C21.0641 13.3953 22.1759 11.54 24.4263 10.1737C23.1672 8.37206 21.265 7.38081 18.7534 7.18658C16.3757 6.99904 13.7771 8.57299 12.826 8.57299C11.8213 8.57299 9.51736 7.25355 7.70899 7.25355C3.9717 7.31383 0 10.234 0 16.1748C0 17.9296 0.321487 19.7424 0.964461 21.6133C1.82176 24.0713 4.91607 30.0992 8.14434 29.9988C9.83214 29.9586 11.0243 28.7999 13.2212 28.7999C15.351 28.7999 16.4561 29.9988 18.3382 29.9988C21.5932 29.9519 24.3928 24.4732 25.2099 22.0085C20.8431 19.9523 21.0775 15.9806 21.0775 15.8533ZM17.2866 4.85579C19.1151 2.68576 18.9476 0.709951 18.8941 0C17.2799 0.0937671 15.4113 1.09841 14.3464 2.33748C13.1743 3.66361 12.4844 5.30454 12.6318 7.15309C14.3798 7.28704 15.9739 6.38956 17.2866 4.85579Z" fill="#1D3C34"/>
          </svg>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// const { loggedIn, user, clear } = useUserSession()
</script>