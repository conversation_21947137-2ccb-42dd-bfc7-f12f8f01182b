<template>
  <div>
    <div class="md:py-8 py-4 bg-primary relative">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <!-- Top Header Logo And Auth Or Profile Data -->
        <div class="flex justify-between items-center mb-4">
          <!-- Logo -->
          <div>
            <NuxtLink :to="localePath(`/`)">
              <img
                class="w-[140px] md:w-[190px]"
                src="~assets/images/logo.svg"
              />
            </NuxtLink>
          </div>
          <!-- Auth Or Profile Data -->
          <div class="flex items-center gap-3">
            <div>
              <NuxtLink
                class="
                  flex
                  md:hidden
                  bg-transparent
                  justify-center
                  p-0
                "
                variant="solid"
                :to="localePath(`/cart`)"
              >
                <div class="relative">
                  <div
                    class="
                      absolute
                      w-[20px]
                      h-[20px]
                      bg-orangeColor
                      rounded-full
                      border border-thirdColor
                      flex items-center justify-center
                      text-thirdColor
                      font-bold
                      text-[12px]
                      right-[-10%]
                      top-[-20%]
                      "
                      :class="{
                        'hidden': totalItems === 0
                      }">
                      {{ totalItems }}
                  </div>
                  <ClientOnly>
                    <font-awesome-icon class="text-[29px] text-thirdColor" icon="fa-solid fa-cart-shopping" />
                  </ClientOnly>
                </div>
              </NuxtLink>
            </div>
            <UButton
              class="
                text-[13px]
                font-semibold
                md:px-[18px]
                bg-thirdColor
                uppercase
                button-shadow
                h-[36px]
                rounded-full
                hover:scale-[1.1]
                hover:bg-orangeColor
                hover:text-thirdColor
                ease-in-out
                duration-500
              "
              :to="localePath(`/auth/login`)"
              color="white"
              variant="solid"
              v-if="!tokenCookie"
            >

              <div class="hidden md:block">{{ $t('SIGN UP / LOGIN') }}</div>
              <div class="md:hidden">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-user" class="text-[20px] text-primary" />
                </ClientOnly>
              </div>
            </UButton>
            <el-dropdown class="w-full" v-else>
              <el-button
                class="
                  !text-primary
                  !font-semibold
                  !font-primaryFont
                  !rounded-full
                  !uppercase
                  !bg-thirdColor
                  !md:h-[32px]
                  !h-[36px]
                  !w-[36px]
                  md:!w-[auto]
                  !text-[13px]
                  !lg:px-7
                  !md:px-3
                  !lg:py-4
                  !md:py-1
                  !border-primary
                  !outline-none
                  button-shadow
                "
              >
              <div class="flex items-center justify-center gap-2">
                <ULink class="md:hidden" :to="localePath(`/profile`)">
                  <ClientOnly>
                    <font-awesome-icon icon="fa-solid fa-user" class="text-[20px]" />
                  </ClientOnly>
                </ULink>
                <div class="font-bold hidden md:block">{{ userData?.first_name }} {{ userData?.last_name }}</div>
              </div>
              </el-button>
              <template #dropdown class="w-full md:hidden">
                <el-dropdown-menu class="w-full">
                  <el-dropdown-item>
                    <ULink :to="localePath(`/profile`)">
                      {{ $t('MY PROFILE') }}
                    </ULink>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <button @click="logout">
                      {{ $t('LOGOUT') }}
                    </button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <div>
              <Languages />
            </div>
          </div>
        </div>
        <!-- Search And Cart -->
        <div class="md:px-10 flex gap-14">
          <!-- Search Input -->
          <div class="relative w-full">
            <div class="select-ui select-ui-header">
              <el-select
                v-model="searchValue"
                filterable
                remote
                reserve-keyword
                :placeholder="$t('Search by Car Model, Tire Size, Spare Part')"
                class="w-100"
                :remote-method="searchFunc"
                :loading="searchLoading"
                @keyup.enter="searchLogic"
                :no-data-text="$t('No results found')"
                clearable
              >
                <el-option
                  v-for="item in searchItems"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                  class="select-ui-header"
                >
                  <ULink :to="localePath(`/product/${item.type}/${item.id}`)">
                    <div class="flex items-center gap-4">
                      <div>
                        <img v-if="item?.image" :src="item?.image" :alt="item?.name" class="w-[65px] h-[65px] rounded-full border border-primary" />
                      </div>
                      <div class="flex flex-col font-medium text-lg">
                        <span class="text-primary mt-[5px]">{{ item?.name }}</span>
                        <span class="mt-[-7px] text-orangeColor" v-if="item?.excerpt">{{ item?.excerpt }}</span>
                        <span class="mt-[-7px] text-primary font-bold text-[15]">{{ $t('EGP') }} {{ formatPrice(Number(item?.price)) }}</span>
                      </div>
                    </div>
                  </ULink>
                </el-option>
                <template #loading>
                <div class="py-10">
                  <ClientOnly>
                  <font-awesome-icon
                    icon="fa-solid fa-circle-notch"
                    class="text-primary text-3xl animate-spin"
                  />
                  </ClientOnly>
                </div>
                </template>
              </el-select>
            </div>
            <UButton
              class="
                box-shadow
                text-thirdColor
                rounded-e-full
                items-center
                justify-center
                bg-secondary
                py-2
                absolute
                top-[1px]
                ltr:right-[-2px]
                rtl:left-[-2px]
                uppercase
                hidden
                md:flex
                h-full
                max-h-[50px]
                font-semibold
                rtl:font-bold
                w-[80px]
                text-xs
                md:w-[130px]
                lg:w-[200px]
                hover:bg-orangeColor
                md:text-sm
                lg:text-lg
                !transition
                !ease-in-out
                !duration-500
              "
              variant="solid"
              @click="searchLogic"
              >
              <div>{{ $t('SEARCH') }}</div>
            </UButton>
          </div>
          <!-- Cart Items -->
          <div class="hidden md:block">
            <UButton
              class="
                bg-orangeColor
                py-3 px-6
                h-[50px]  min-w-[260px]
                rounded-full
                font-thirdFont font-semibold
                text-thirdColor
                button-shadow
                flex justify-between
                transition
                ease-in-out
                duration-500
                hover:bg-secondary
                hover:text-thirdColor
              "
              variant="solid"
              :to="localePath('/cart')"
            >
              <div class="flex items-center gap-4">
                <div>
                  <ClientOnly>
                    <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                  </ClientOnly>
                </div>
                <div>
                  <span>{{ totalItems || '0' }} {{ $t('Items') }}</span>
                </div>
              </div>
              <div class="border-l border-thirdColor thirdColor h-[16px]"></div>
              <div>
                <span>{{ cartItems?.single?.[0]?.total || '0' }} {{ $t('EGP') }}</span>
              </div>
            </UButton>
          </div>
        </div>
      </div>
    </div>






    <div class="bg-secondary w-full border-b border-primary py-4 hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="flex flex-col items-center gap-8 md:gap-4 lg:gap-8 md:flex-row mt-[10px] sm:mt-0">
          <ULink
            active-class="text-primary md:text-sm lg:text-lg font-extrabold text-base uppercase flex items-center gap-2"
            inactive-class="text-primary md:text-sm lg:text-lg font-medium text-base uppercase flex items-center gap-2"
            :to="localePath(`${link?.slug !== '' ? `/category/${link?.id}` : '/'}`)"
            v-for="link in links ?? []"
            
          >
            <img v-if="link?.icon" :src="link?.icon" :alt="link?.name" class="w-[25px] h-[25px]" />
            <span>
              {{ locale === 'ar' ? link?.name_ar ? link?.name_ar : link?.name : link?.name }}
            </span>
          </ULink>
          <ULink
            active-class="text-primary md:text-sm lg:text-lg font-extrabold text-base uppercase flex items-center gap-2"
            inactive-class="text-primary md:text-sm lg:text-lg font-medium text-base uppercase flex items-center gap-2"
            :to="localePath('/tips-and-tricks')"
          >
            {{ $t('tips & tricks') }}
          </ULink>
        </div>
      </div>
    </div>



    <div>
      <!-- Sticky Header -->
      <div class="sticky-header" :class="{ 'sticky-visible': showStickyHeader }">
        <div class="bg-primary py-3">
          <div class="px-[20px] max-w-[1280px] mx-auto">
            <div class="flex justify-between items-center">
              <!-- Logo -->
              <div class="me-4">
                <NuxtLink :to="localePath(`/`)">
                  <img class="w-[100px] sm:w-[120px]" src="~assets/images/logo.svg" />
                </NuxtLink>
              </div>
              <div class="flex items-center gap-4">
                <!-- Search Input -->
                <div class="relative w-[400px] hidden md:block">
                  <div class="select-ui scroll-select-ui">
                    <ClientOnly>
                      <el-select
                        v-model="searchValue"
                        filterable
                        remote
                        reserve-keyword
                        :placeholder="$t('Search by Car Model, Tire Size, Spare Part')"
                        class="custom-padding-header-scroll"
                        :remote-method="searchFunc"
                        :loading="searchLoading"
                        @keyup.enter="searchLogic"
                        :no-data-text="$t('No results found')"
                        clearable
                      >
                        <el-option
                          v-for="item in searchItems"
                          :key="item.id"
                          :label="item.name"
                          :value="item"
                          class="select-ui-header"
                        >
                          <ULink :to="localePath(`/product/${item.type}/${item.id}`)">
                            <div class="flex items-center gap-4">
                              <div>
                                <img v-if="item?.image" :src="item?.image" :alt="item?.name" class="w-[65px] h-[65px] rounded-full border border-primary" />
                              </div>
                              <div class="flex flex-col font-medium text-lg">
                                <span class="text-primary mt-[5px]">{{ item?.name }}</span>
                                <span class="mt-[-7px] text-orangeColor" v-if="item?.excerpt">{{ item?.excerpt }}</span>
                                <span class="mt-[-7px] text-primary font-bold text-[15]">EGP {{ formatPrice(Number(item?.price)) }}</span>
                              </div>
                            </div>
                          </ULink>
                        </el-option>
                        <!-- Options reused from main header -->
                        <template #loading>
                          <div class="py-5">
                            <ClientOnly>
                              <font-awesome-icon
                                icon="fa-solid fa-circle-notch"
                                class="text-primary text-xl animate-spin"
                              />
                            </ClientOnly>
                          </div>
                        </template>
                      </el-select>
                    </ClientOnly>
                  </div>
                </div>

                <div
                  @click="showSearch = !showSearch"
                  class="w-[36px] h-[36px] bg-thirdColor md:hidden rounded-full flex items-center justify-center cursor-pointer"
                >
                  <ClientOnly>
                    <div>
                      <font-awesome-icon class="text-[20px] text-primary" icon="fa-solid fa-search" />
                    </div>
                  </ClientOnly>
                </div>

                <!-- Cart Button -->
                <div>
                  <UButton
                    class="
                      bg-thirdColor
                      md:bg-orangeColor
                      md:py-2 md:px-4
                      w-[36px] h-[36px]
                      md:w-[auto]
                      md:h-[38px] md:min-w-[110px]
                      rounded-full
                      font-thirdFont font-semibold
                      text-thirdColor
                      text-sm
                      button-shadow
                      flex items-center justify-center md:justify-between
                      transition ease-in-out duration-300
                      hover:bg-secondary
                    "
                    variant="solid"
                    :to="localePath(`/cart`)"
                  >
                    <div class="flex items-center gap-2">
                      <ClientOnly>
                        <font-awesome-icon icon="fa-solid fa-cart-shopping" class="text-primary md:text-thirdColor text-[18px] md:text-base" />
                      </ClientOnly>
                      <span class="hidden md:block">{{ totalItems || '0' }} {{ $t('Items') }}</span>
                    </div>
                  </UButton>
                </div>

                <!-- Auth/Profile Button -->
                <UButton
                  class="
                    text-[12px]
                    flex items-center justify-center
                    font-semibold
                    sm:px-[18px]
                    bg-thirdColor
                    uppercase
                    button-shadow
                    md:h-[38px]
                    h-[36px]
                    w-[36px]
                    md:w-[auto]
                    rounded-full
                    hover:scale-[1.05]
                    hover:bg-orangeColor
                    transition ease-in-out duration-300
                  "
                  :to="localePath(`/auth/login`)"
                  color="white"
                  variant="solid"
                  v-if="!tokenCookie"
                >
                  <div class="hidden md:block">{{ $t('SIGN UP / LOGIN') }}</div>
                  <div class="md:hidden">
                    <ClientOnly>
                      <font-awesome-icon icon="fa-solid fa-user" class="text-[20px] text-primary" />
                    </ClientOnly>
                  </div>
                </UButton>
                <el-dropdown v-else>
                  <el-button
                    class="
                      !text-primary
                      !font-semibold
                      !font-primaryFont
                      !rounded-full
                      !uppercase
                      !bg-thirdColor
                      !h-[36px]
                      !w-[36px]
                      md:!w-[auto]
                      md:!h-[38px]
                      !text-[12px]
                      !md:px-4
                      !md:py-2
                      !border-primary
                      !outline-none
                      button-shadow
                    "
                  >
                    <div class="flex items-center justify-center gap-2">
                      <ULink class="md:hidden" :to="localePath(`/profile`)">
                        <ClientOnly>
                          <font-awesome-icon icon="fa-solid fa-user" class="text-[20px]" />
                        </ClientOnly>
                      </ULink>
                      <div class="font-bold hidden md:block">{{ userData?.first_name }} {{ userData?.last_name }}</div>
                    </div>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <ULink :to="localePath(`/profile`)">{{ $t('MY PROFILE') }}</ULink>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <button @click="logout">{{ $t('LOGOUT') }}</button>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <transition
              enter-active-class="transition duration-300 ease-out"
              enter-from-class="transform -translate-y-4 opacity-0"
              enter-to-class="transform translate-y-0 opacity-100"
              leave-active-class="transition duration-200 ease-in"
              leave-from-class="transform translate-y-0 opacity-100"
              leave-to-class="transform -translate-y-4 opacity-0"
            >
              <div v-show="showSearch" class="relative mt-2 md:hidden">
                <div class="select-ui scroll-select-ui">
                  <ClientOnly>
                    <el-select
                    v-model="searchValue"
                    filterable
                    remote
                    reserve-keyword
                    :placeholder="$t('Search by Car Model, Tire Size, Spare Part')"
                    class="custom-padding-header-scroll"
                    :remote-method="searchFunc"
                    :loading="searchLoading"
                    @keyup.enter="searchLogic"
                    :no-data-text="$t('No results found')"
                    clearable
                    >
                    <el-option
                      v-for="item in searchItems"
                      :key="item.id"
                      :label="item.name"
                      :value="item"
                      class="select-ui-header"
                    >
                      <ULink :to="localePath(`/product/${item.type}/${item.id}`)">
                        <div class="flex items-center gap-4">
                          <div>
                          <img v-if="item?.image" :src="item?.image" :alt="item?.name" class="w-[65px] h-[65px] rounded-full border border-primary" />
                          </div>
                          <div class="flex flex-col font-medium text-lg">
                          <span class="text-primary mt-[5px]">{{ item?.name }}</span>
                          <span class="mt-[-7px] text-orangeColor" v-if="item?.excerpt">{{ item?.excerpt }}</span>
                          <span class="mt-[-7px] text-primary font-bold text-[15]">EGP {{ formatPrice(Number(item?.price)) }}</span>
                          </div>
                        </div>
                      </ULink>
                    </el-option>
                    <template #loading>
                      <div class="py-5">
                      <ClientOnly>
                        <font-awesome-icon
                        icon="fa-solid fa-circle-notch"
                        class="text-primary text-xl animate-spin"
                        />
                      </ClientOnly>
                      </div>
                    </template>
                    </el-select>
                  </ClientOnly>
                </div>
              </div>
            </transition>
          </div>
        </div>

        <!-- Sticky Navigation Menu -->
        <div class="bg-secondary w-full border-b border-primary py-3.5 hidden md:block">
          <div class="px-[20px] max-w-[1280px] mx-auto">
            <div class="flex items-center gap-4 lg:gap-6">
              <ULink
                active-class="text-primary md:text-sm lg:text-base font-extrabold uppercase flex items-center gap-2"
                inactive-class="text-primary md:text-sm lg:text-base font-medium uppercase flex items-center gap-2"
                :to="localePath(`${link.slug !== '' ? `/category/${link.id}` : '/'}`)"
                v-for="link in links ?? []"
              >
                <img v-if="link.icon" :src="link.icon" :alt="link.name" class="w-[20px] h-[20px]" />
                <span>{{ locale === 'ar' ? link?.name_ar ? link?.name_ar : link?.name : link?.name }}</span>
              </ULink>
              <ULink
                active-class="text-primary md:text-sm lg:text-base font-extrabold uppercase flex items-center gap-2"
                inactive-class="text-primary md:text-sm lg:text-base font-medium uppercase flex items-center gap-2"
                :to="localePath(`/tips-and-tricks`)"
              >
                {{ $t('tips & tricks') }}
              </ULink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'
import type { CartData, CartItem } from '~/types/defaultCart'
import type { UserData } from '~/types/user'
import type { NavigationLinks } from '~/types/links'
const router = useRouter()
const { locale, t } = useI18n()
const localePath = useLocalePath()

const emits = defineEmits(['getGuestData'])
// Main Variables
import { persistentCookieOptions } from '~/utils/cookieOptions'

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)
const userData = useState<UserData>("userData", () => ({}))
const cartItems = useState<CartData>("cartItems", () => ({}))
const links = useState<NavigationLinks>("links", () => [])
const totalItems = ref(0)

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Sticky Header Variables
const lastScrollPosition = ref<number>(0)
const showStickyHeader = ref<boolean>(false)
const isScrollingDown = ref<boolean>(false)
const mainHeaderHeight = ref<number>(0)
const scrollTimeout = ref<NodeJS.Timeout | null>(null)

// Handle scroll event with improved logic and debouncing
const handleScroll = () => {
  const currentScrollPosition = window.scrollY

  // Calculate the height of main header if not already done
  if (mainHeaderHeight.value === 0) {
    const mainHeader = document.querySelector('.bg-primary')
    const navMenu = document.querySelector('.bg-secondary.w-full')

    if (mainHeader && navMenu) {
      const mainHeaderRect = mainHeader.getBoundingClientRect()
      const navMenuRect = navMenu.getBoundingClientRect()
      mainHeaderHeight.value = mainHeaderRect.height + navMenuRect.height
    }
  }

  // Clear any existing timeout
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }

  // Determine scroll direction
  isScrollingDown.value = currentScrollPosition > lastScrollPosition.value

  // Set a new timeout
  scrollTimeout.value = setTimeout(() => {
    if (currentScrollPosition > mainHeaderHeight.value) {
      showStickyHeader.value = !isScrollingDown.value
    } else {
      showStickyHeader.value = false
    }
  }, 50) // Small delay to prevent rapid changes

  lastScrollPosition.value = currentScrollPosition
}

// Use throttled scroll handler for better performance
const throttledHandleScroll = () => {
  window.requestAnimationFrame(handleScroll)
}

// Setup scroll event listener
onMounted(() => {
  window.addEventListener('scroll', throttledHandleScroll, { passive: true })

  // Calculate initial header height
  setTimeout(() => {
    const mainHeader = document.querySelector('.bg-primary')
    const navMenu = document.querySelector('.bg-secondary.w-full')

    if (mainHeader && navMenu) {
      const mainHeaderRect = mainHeader.getBoundingClientRect()
      const navMenuRect = navMenu.getBoundingClientRect()
      mainHeaderHeight.value = mainHeaderRect.height + navMenuRect.height
    }
  }, 100)

  // Calculate initial total items
  if (cartItems?.value?.single?.[0]?.items?.length) {
    totalItems.value = cartItems.value.single[0].items.reduce((acc: number, item: CartItem) => {
      return acc + Number(item.quantity || 0)
    }, 0)
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)

  // Clear any pending timeouts
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }

  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
})
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Logout Logic
const logout = () => {
  // Clear authentication cookies
  tokenCookie.value = null
  userIdCookie.value = null
  
  // Clear user data and cart
  userData.value = {}
  cartItems.value = {}
  
  // Clear address-related state
  const additionalAddress = useState("additionalAddress", () => [])
  const selectedAddress = useState("selectedAddress", () => 'default')
  additionalAddress.value = []
  selectedAddress.value = 'default'
  
  // Clear localStorage
  localStorage.removeItem("userData")
  
  ElNotification({
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, 'Logged out successfully.'),
    position: 'top-right',
  })
  emits('getGuestData')
  router.push(localePath('/'))
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Search Logic
// Vars
const showSearch = ref<boolean>(false)
const searchValue = ref<any>(null)
const searchItems = ref<any[]>([])
const searchLoading = ref<boolean>(false)
const searchTimeout = ref<NodeJS.Timeout | null>(null)
const abortController = ref<AbortController | null>(null)

// Search logic to get data with debounce
const searchFunc = async (value: string) => {
  // Clear any existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  // Cancel any pending request
  if (abortController.value) {
    abortController.value.abort()
  }

  // Set loading state immediately
  searchLoading.value = true

  // Set a new timeout
  searchTimeout.value = setTimeout(async () => {
    if (value !== '') {
      try {
        // Create new AbortController for this request
        abortController.value = new AbortController()
        
        searchValue.value = value
        const { items } = await fetchHock(`newBranch/search?keyword=${value}`, { signal: abortController?.value?.signal })
        searchItems.value = items?.value?.results || []
      } catch (error) {
        // Only update items if the error is not from abort
        if (error.name !== 'AbortError') {
          console.error('Search error:', error)
          searchItems.value = []
        }
      }
    } else {
      searchItems.value = []
    }
    searchLoading.value = false
  }, 500) // 500ms delay before executing the search
}

// Search redirect
const searchLogic = () => {
  if (searchValue.value && typeof searchValue.value === 'object') {
    // If an item is selected, navigate to its product page
    router.push(localePath(`/product/${searchValue.value.type}/${searchValue.value.id}`))
    searchValue.value = null
  } else if (typeof searchValue.value === 'string' && searchValue.value.trim()) {
    // If text is entered but no item selected, search for that text
    router.push({
      path: localePath('/products'),
      query: { search: searchValue.value.trim() }
    })
    searchValue.value = null
  } else {
    // Fallback to products page
    router.push({ path: localePath('/products') })
  }
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Cart Items Count Logic
// Calculate total items by summing up quantities
watch(cartItems, () => {
  if (cartItems?.value?.single?.[0]?.items?.length) {
    totalItems.value = cartItems.value.single[0].items.reduce((acc: number, item: CartItem) => {
      return acc + Number(item.quantity || 0)
    }, 0)
  } else {
    totalItems.value = 0
  }
})
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
</script>

<style>
.select-ui-header .el-select__selected-item.el-select__placeholder {
  color: #1D3C34 !important;
  font-weight: 400 !important;
  font-family: "Oxygen", sans-serif !important;
  font-size: 15px !important;
}

.select-ui-header.el-select-dropdown__item {
  height: 85px !important;
}

.select-ui.scroll-select-ui .el-select__selected-item.el-select__placeholder {
  font-size: 12px !important;
}

.select-ui.scroll-select-ui .el-select__wrapper {
  padding: 16px 32px !important;
  height: 35px;
}

/* Updated Sticky Header Animation */
.sticky-header {
  position: fixed;
  top: -1px;
  left: 0;
  width: 100%;
  z-index: 56;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transform: translateY(-100%);
  opacity: 0;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  pointer-events: none;
}

.sticky-header.sticky-visible {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}
</style>