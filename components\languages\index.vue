<template>
  <div class="cursor-pointer">
    <NuxtLink 
      :to="switchLocalePath('ar')"
      v-if="locale === 'en'"
      class="
      shadow-lg"
    >
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="20" fill="#E9EAC8"/>
        <path d="M20.831 29.5317C19.9366 29.5317 19.0622 29.4249 18.2078 29.2113C17.3668 28.9977 16.6059 28.6573 15.9251 28.1901C15.2443 27.7362 14.7036 27.1488 14.3031 26.428C13.9026 25.7071 13.7024 24.8394 13.7024 23.8248C13.7024 22.7302 13.9227 21.729 14.3632 20.8212C14.8037 19.9001 15.4245 19.0858 16.2254 18.3783C17.0264 17.6708 17.9742 17.0834 19.0689 16.6162C20.1769 16.1356 21.385 15.7952 22.6932 15.5949L23.354 18.2381C22.1793 18.4517 21.158 18.732 20.2903 19.0791C19.436 19.4262 18.7284 19.8334 18.1678 20.3006C17.6071 20.7678 17.1866 21.2684 16.9063 21.8024C16.6393 22.3497 16.5058 22.9238 16.5058 23.5245C16.5058 24.0051 16.5925 24.4256 16.7661 24.786C16.953 25.1464 17.1999 25.4468 17.507 25.6871C17.814 25.9407 18.1544 26.141 18.5282 26.2878C18.902 26.448 19.2891 26.5615 19.6896 26.6282C20.0901 26.695 20.4705 26.7283 20.831 26.7283C21.7521 26.7283 22.6065 26.6416 23.3941 26.468C24.195 26.2945 24.8959 26.0809 25.4966 25.8273L26.2976 28.4104C25.9772 28.5706 25.5233 28.7374 24.9359 28.911C24.3619 29.0845 23.7145 29.2314 22.9936 29.3515C22.2861 29.4716 21.5652 29.5317 20.831 29.5317ZM16.8462 18.4384C16.379 18.2515 15.9451 17.9845 15.5446 17.6374C15.1575 17.2903 14.8438 16.8631 14.6035 16.3558C14.3765 15.8352 14.2631 15.2412 14.2631 14.5737C14.2631 13.7327 14.4833 13.0051 14.9239 12.3911C15.3644 11.777 15.9518 11.3031 16.686 10.9694C17.4202 10.6356 18.2212 10.4688 19.0889 10.4688C19.5161 10.4688 19.9099 10.4954 20.2703 10.5488C20.6441 10.6022 21.0245 10.6823 21.4117 10.7891L20.9712 13.4523C20.6908 13.3856 20.4038 13.3322 20.1101 13.2922C19.8164 13.2521 19.5561 13.2321 19.3292 13.2321C18.8619 13.2321 18.4548 13.2988 18.1077 13.4323C17.774 13.5658 17.5137 13.7594 17.3268 14.013C17.1532 14.2533 17.0664 14.5403 17.0664 14.8741C17.0664 15.141 17.1465 15.3947 17.3067 15.635C17.4803 15.8753 17.7005 16.0889 17.9675 16.2757C18.2345 16.4493 18.5282 16.5961 18.8486 16.7163C19.169 16.8231 19.4827 16.8831 19.7897 16.8965L16.8462 18.4384Z" fill="#1D3C34"/>
      </svg>
    </NuxtLink>
    <NuxtLink 
      :to="switchLocalePath('en')"
      v-if="locale === 'ar'"
      class="
      shadow-lg"
    >
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="20" fill="#E9EAC8"/>
        <path d="M11.5848 19.0564H17.1528V20.7684H11.5848V19.0564ZM11.7448 23.8564H18.0648V25.6004H9.66479V14.4004H17.8408V16.1444H11.7448V23.8564Z" fill="#1D3C34"/>
        <path d="M20.3835 25.6004V14.4004H22.0955L29.1195 23.0244H28.2715V14.4004H30.3355V25.6004H28.6235L21.5995 16.9764H22.4475V25.6004H20.3835Z" fill="#1D3C34"/>
      </svg>
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
const switchLocalePath = useSwitchLocalePath()
const { locale } = useI18n()
</script>

<style scoped></style>