<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="ads-slider">
      <Swiper
        @swiper="onSwiper"
        :modules="[SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
        :loop="(products?.length ?? 0) > 2"
        :space-between="20"
        :pagination="{clickable: true, dynamicBullets: true}"
        :autoplay="{
          delay: 4000,
          disableOnInteraction: false,
        }"
        :breakpoints="{
          320: { slidesPerView: 1 },
          480: { slidesPerView: 1 },
          768: { slidesPerView: 2 },
          1024: { slidesPerView: 2 },
          1400: { slidesPerView: 2 },
          1600: { slidesPerView: 2 },
          1920: { slidesPerView: 2 },
          2560: { slidesPerView: 2 },
          3200: { slidesPerView: 2 },
        }"
      >
        <SwiperSlide v-for="(item, index) in products ?? []" :key="index">
          <div draggable="false" class="mx-auto">
            <NuxtLink :to="localePath(`${item.redirect_link}`)">
              <div class="relative mx-auto">
                <img :src="item.image" />
                <div class="absolute top-0 left-0 z-10 w-full h-full" v-if="locale === 'en' ? item.content : item.content_ar" style="background: linear-gradient(49.66deg, #1D3C34 27.04%, rgba(38, 50, 56, 0.88) 44.1%, rgba(38, 50, 56, 0.13) 79.53%);">
                </div>
                <div
                  v-if="locale === 'en' ? item.content : item.content_ar"
                  class="absolute z-20 bottom-10 left-10 text-thirdColor"
                >
                  <div class="font-primaryFont mb-2 uppercase ads-content" v-html="locale === 'en' ? item.content : item.content_ar"></div>
                </div>
              </div>
            </NuxtLink>
          </div>
        </SwiperSlide>
      </Swiper>
    </div>
  </div>
</template>
<script setup lang="ts">
const localePath = useLocalePath()
const { locale } = useI18n()

interface Advertisement {
  image: string
  content?: string
  content_ar?: string
  redirect_link: string
}

interface Props {
  products?: Advertisement[]
}

const props = defineProps<Props>()

// Swiper settings
const swiperInstance = ref<any>(null)
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

onMounted(async () => {
  await nextTick()

  const swiperSlides = document.querySelectorAll('.ads-slider')

  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', onMouseOver)
    slide.addEventListener('mouseleave', onMouseLeave)
    slide.addEventListener('touchstart', onMouseOver)
    slide.addEventListener('touchend', onMouseLeave)
  })
})

function onMouseOver(event: Event): void {
  if (swiperInstance.value?.autoplay) {
    swiperInstance.value.autoplay.stop()
  }
}

function onMouseLeave(event: Event): void {
  if (swiperInstance.value?.autoplay) {
    swiperInstance.value.autoplay.start()
  }
}
</script>
<style>
.ads-content h1 {
  font-size: 13px;
  font-weight: bold;
}
.ads-content p {
  font-size: 16px;
}

@media (min-width: 768px) {
  .ads-content h1 {
    font-size: 15px;
  }
  .ads-content p {
    font-size: 25px;
  }
}
</style>
