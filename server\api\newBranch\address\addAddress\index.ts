export default defineEventHandler(async (event) => {
   try {
    const bodyy = await readBody(event)
    const url = `${process.env.WP_API_URL}user_add_address`
    const method = 'post'
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

    const response = await $fetch(url, {
      method: method,
      body: bodyy,
      headers: headers,
    })

    const data = await response
    return data
  } catch (error: any) {
    console.log(error)
    // console.error('Error in register handler:', error?.data)
    return error
  }
 })
