<template>
  <div class="relative">
    <!-- Mobile header -->
    <div class="md:hidden sticky rtl:font-notoSans z-[55] top-0 left-0 w-full font-secondaryFont uppercase text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
      {{ locale === 'en' ? aboutContent?.title : aboutContent?.title_ar }}
    </div>
    <!-- Desktop banner section -->
    <div class="mt-[60px] hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="bg-cartColor rounded-2xl p-5 sm:p-10 text-black">
          <div class="flex md:gap-20 gap-4 justify-between flex-col md:flex-row items-center">
            <!-- Banner description -->
            <div class="font-secondaryFont text-sm sm:text-lg text-primary rtl:font-notoSans">
              {{ locale === 'en' ? aboutContent?.page_banner_desc : aboutContent?.page_banner_desc_ar }}
            </div>
            <!-- Contact button -->
            <UButton
              class="
                rounded-full
                flex
                items-center
                justify-center
                font-bold
                sm:text-xl
                py-3
                sm:px-20
                px-14
                font-thirdFont
                text-thirdColor
                uppercase
                bg-orangeColor
                border-2
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                transition-all
                duration-500
                ease-in-out"
                :to="localePath(`/${aboutContent.banner_link_slug}`)"
            >
              {{ locale === 'en' ? aboutContent?.banner_link_text : aboutContent?.banner_link_text_ar }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
    <!-- Main content section -->
    <div class="mt-5 md:mt-[60px] mb-[100px] md:mb-0">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <!-- Featured image -->
        <img
          class="rounded-[30px] max-h-[250px] md:max-h-full border border-primary mx-auto"
          :src="aboutContent.featured_image"
          :alt="aboutContent.title"
          loading="lazy"
        />

        <!-- Main content -->
        <div class="mt-5 md:mt-14 font-primaryFont text-primary rtl:font-notoSans text-[13px] md:text-xl text-center md:w-[75%] mx-auto mb-14 pb-14">
          <div class="mb-4" v-html="locale === 'en' ? aboutContent?.content : aboutContent?.content_ar"></div>
        </div>
      </div>
    </div>
    <!-- Mobile contact banner (fixed at bottom) -->
    <div class="md:hidden fixed bottom-[59px] w-full">
      <div class="bg-cartColor p-5">
        <div class="px-[20px] max-w-[1280px] mx-auto">
          <div class="flex gap-2 justify-between flex-col md:flex-row items-center">
            <div class="font-secondaryFont text-sm rtl:font-notoSans text-primary uppercase">
              {{ $t('contact us if you need any help!') }}
            </div>
            <!-- Mobile contact button -->
            <UButton
              class="
                rounded-full
                flex
                items-center
                justify-center
                font-bold
                py-2
                button-shadow
                px-14
                w-full
                font-thirdFont
                text-thirdColor
                uppercase
                bg-orangeColor
                border-2
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                transition-all
                duration-500
                ease-in-out"
                :to="localePath(`/${aboutContent.banner_link_slug}`)"
            >
              {{ locale === 'en' ? aboutContent.banner_link_text : aboutContent.banner_link_text_ar }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { PagesContent, PageContent } from '~/types/pagesContent'
const { locale } = useI18n()
const localePath = useLocalePath()

/**
 * Get page content from global state
 * Initialize with empty object to prevent errors when data is loading
 */
const pagesContent = useState<PagesContent>("pagesContent", () => ({} as PagesContent))

/**
 * Default content to use as fallback when API data is not available
 */
const defaultContent = {
  title: 'About Us',
  page_banner_desc: 'Learn more about our company and our mission to provide quality products and services.',
  banner_link_text: 'Contact Us',
  banner_link_slug: 'contact',
  content: '<p>Welcome to our company. We are dedicated to providing the best products and services to our customers.</p>',
  featured_image: '/images/placeholder.jpg'
}

/**
 * Get about page content with fallback to default content
 */
const aboutContent = computed<PageContent>(() => {
  return pagesContent.value?.about || defaultContent
})
</script>