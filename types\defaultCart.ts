export interface MetaData {
  name?: string;
  value?: string | number | boolean | object;
}

export interface TaxData {
  [key: string]: string;
}

export interface Taxes {
  total?: {
    [key: string]: string;
  };
  subtotal?: {
    [key: string]: string;
  };
}

export interface TaxLine {
  rate_id?: number;
  label?: string;
  compound?: string;
  tax_total?: number;
  tax_rate?: string;
}

export interface Address {
  first_name?: string;
  last_name?: string;
  address_1?: string;
  address_2?: string;
  city?: string;
  state?: string;
  postcode?: string;
  country?: string;
  email?: string;
  phone?: string;
}

export interface CartItem {
  // Basic product information
  product_id?: number;
  variation_id?: number;
  product_name?: string;
  quantity?: number;
  line_item_id?: number;

  // Price information
  price?: number;
  display_price?: number;
  single_price?: number;
  subtotal?: number;
  subtotal_tax?: number;
  discount?: number;
  total?: number;
  total_tax?: number;
  total_incl_tax?: number;

  // Tax information
  taxes?: Taxes;

  // Display information
  image?: string;
  brand_logo?: string;
  excerpt?: string;

  // Additional metadata
  meta_data?: MetaData[] | string;

  // Legacy/compatibility fields
  name?: string; // For backward compatibility
  order_title?: string;
  status?: string;
}

export interface CartOrder {
  id?: number;
  order_title?: string;
  status?: string;
  date?: string;
  total?: number | string;
  subtotal?: number;
  total_tax?: number | string;
  total_discount?: number;
  customer_id?: number;
  billing?: Address;
  shipping?: Address;
  payment_method?: string;
  payment_method_title?: string;
  items?: CartItem[];
  tax_lines?: TaxLine[];
  courier_track_link?: string;
}

export interface GroupedOrder {
  group?: string;
  total?: number;
  date?: string;
  orders?: CartOrder[];
}

export interface CartData {
  grouped?: GroupedOrder[];
  single?: CartOrder[];
  total_orders_count?: number;
  total_pages?: number;
}
