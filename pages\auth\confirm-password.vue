<template>
  <div class="bg-thirdColor pb-[60px]">
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="pt-10 relative sm:max-w-[60%] mx-auto border-b border-primary pb-8">
        <div class="md:mt-5">
          <!-- Title -->
          <div class="text-center mb-12"  v-if="phoneNumber">
            <h1 class="font-secondaryFont font-bold text-primary text-xl md:text-2xl mb-2 rtl:font-notoSans">
              {{ $t('Forgot Your Password?') }}
            </h1>
            <p class="font-secondaryFont font-bold text-primary text-xl md:text-2xl mb-2 rtl:font-notoSans">
              {{ $t("Don't worry—it happens to the best of us.") }}
            </p>
          </div>
  
          <!-- Error Message -->
          <div v-if="!phoneNumber" class="text-center">
            <p class="text-primary font-primaryFont text-lg mb-4 rtl:font-notoSans">
              {{ $t('Mobile number is missing. Please start the password reset process again.') }}
            </p>
            <ULink
              to="/auth/forgot-password"
              class="
                inline-block
                font-thirdFont
                font-bold
                text-lg
                text-thirdColor
                py-3
                px-12
                rounded-full
                border
                rtl:font-notoSans
                bg-primary
                border-primary
                hover:border-orangeColor
                hover:bg-orangeColor
                ease-in-out
                duration-500
                uppercase
              "
            >
              {{ $t('Go to Forgot Password') }}
            </ULink>
          </div>
  
          <!-- Form -->
          <UForm
            v-else
            ref="form"
            :schema="schema"
            :state="state"
            class="space-y-4"
            @submit="onSubmit"
            aria-label="Set new password form"
          >
            <div class="w-full mx-auto flex flex-col gap-6">
              <!-- Password field -->
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="password" label-class="sr-only">
                  <label for="password-input" class="sr-only">{{ $t('New Password') }}</label>
                  <input
                    id="password-input"
                    class="w-full border border-primary rounded-full !bg-thirdColor py-2 px-5 md:py-3 md:px-5 md:rounded-lg focus:outline-0"
                    :type="showPassword ? 'text' : 'password'"
                    v-model="state.password"
                    required
                    placeholder=" "
                    aria-label="New Password"
                  >
                  
                  <span
                    class="uppercase absolute text-primary rtl:font-notoSans font-primaryFont font-semibold md:text-base text-[13px] z-[4] left-[22px] md:top-[13px] top-[11px] transition-all duration-100 custom-auth-style ease-in-out"
                    :class="{ 'placeholder-animation-bg-13': state.password }"
                  >
                    {{ $t('New Password') }}
                  </span>
  
                  <button
                    type="button"
                    class="absolute right-4 top-1/2 -translate-y-1/2 text-primary hover:text-orangeColor transition-colors duration-300"
                    @click="showPassword = !showPassword"
                    aria-label="Toggle password visibility"
                  >
                    <ClientOnly>
                      <font-awesome-icon
                        :icon="showPassword ? 'fa-solid fa-eye-slash' : 'fa-solid fa-eye'"
                        class="text-xl"
                      />
                    </ClientOnly>
                  </button>
                </UFormGroup>
              </div>
  
              <!-- Confirm Password field -->
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="confirm_password" label-class="sr-only">
                  <label for="confirm-password-input" class="sr-only">{{ $t('Confirm Password') }}</label>
                  <input
                    id="confirm-password-input"
                    class="w-full border border-primary rounded-full !bg-thirdColor py-2 px-5 md:py-3 md:px-5 md:rounded-lg focus:outline-0"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    v-model="state.confirm_password"
                    required
                    placeholder=" "
                    aria-label="Confirm Password"
                  >
                  
                  <span
                    class="uppercase absolute text-primary rtl:font-notoSans font-primaryFont font-semibold md:text-base text-[13px] z-[4] left-[22px] md:top-[13px] top-[11px] transition-all duration-100 custom-auth-style ease-in-out"
                    :class="{ 'placeholder-animation-bg-13': state.confirm_password }"
                  >
                    {{ $t('Confirm Password') }}
                  </span>
  
                  <button
                    type="button"
                    class="absolute right-4 top-1/2 -translate-y-1/2 text-primary hover:text-orangeColor transition-colors duration-300"
                    @click="showConfirmPassword = !showConfirmPassword"
                    aria-label="Toggle confirm password visibility"
                  >
                    <ClientOnly>
                      <font-awesome-icon
                        :icon="showConfirmPassword ? 'fa-solid fa-eye-slash' : 'fa-solid fa-eye'"
                        class="text-xl"
                      />
                    </ClientOnly>
                  </button>
                </UFormGroup>
              </div>
  
              <!-- Submit button -->
              <div class="flex justify-center">
                <UButton
                  type="submit"
                  class="
                    font-thirdFont
                    font-bold
                    text-lg
                    text-thirdColor
                    py-3
                    px-12
                    mt-4
                    rounded-full
                    border
                    bg-primary
                    border-primary
                    hover:border-orangeColor
                    hover:bg-orangeColor
                    ease-in-out
                    duration-500
                    uppercase
                    w-full
                    md:w-auto
                    min-w-[200px]
                    flex
                    items-center
                    justify-center
                    rtl:font-notoSans
                    relative
                  "
                  variant="solid"
                  :disabled="isLoading"
                >
                  <span :class="{ 'opacity-0': isLoading }">{{ $t('CONFIRM PASSWORD') }}</span>
                  <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center">
                    <div class="w-5 h-5 border-2 border-thirdColor border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </UButton>
              </div>
            </div>
          </UForm>
                  <!-- Register/Login links -->
          <div class="text-center mt-8 mb-2" v-if="!tokenCookie">
            <div class="flex items-center justify-center gap-2 text-primary font-primaryFont rtl:font-notoSans text-sm md:text-base">
              <ULink
                :to="localePath('/auth/register')"
                class="underline hover:text-orangeColor transition-colors duration-300"
                aria-label="Register for new account"
              >
                {{ $t('Register') }}
              </ULink>
              <span>|</span>
              <ULink
                :to="localePath('/auth/login')"
                class="underline hover:text-orangeColor transition-colors duration-300"
                aria-label="Login to existing account"
              >
                {{ $t('Login') }}
              </ULink>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Social login options -->
      <AuthSocialLogin v-if="!tokenCookie" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import { ElNotification } from 'element-plus'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const phoneNumber: any = useState("phoneNumber", () => '')
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoading = ref(false)
const tokenCookie = useCookie('token', persistentCookieOptions)

const { t } = useI18n()
const localePath = useLocalePath()

/**
 * Notification helper
 */
const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  ElNotification({
    message: h('i', {
      style: `color: ${type === 'success' ? '#1D3C34' : '#ff4949'}; font-weight: bold;`
    }, message),
    position: 'top-right',
    duration: 3000,
    type
  })
}

/**
 * Form validation schema
 */
const schema = z.object({
  password: z.string()
    .min(6, t('Password must be at least 6 characters')),
  confirm_password: z.string()
}).refine((data) => data.password === data.confirm_password, {
  message: t("Passwords don't match"),
  path: ["confirm_password"]
})

type Schema = z.output<typeof schema>

/**
 * Form state
 */
const state = reactive({
  password: undefined,
  confirm_password: undefined
})

/**
 * Handle form submission
 */
async function onSubmit() {
  try {
    isLoading.value = true
    
    const response: any = await $fetch('/api/newBranch/forgotPassword/confirmPassword', {
      method: 'POST',
      body: {
        phone: phoneNumber.value,
        new_password: state.password
      }
    })

    if (response.error) {
      showNotification(response.error, 'error')
      return
    }

    if (response.message) {
      showNotification(t('Password updated successfully!'), 'success')
      
      // Check if user is logged in
      if (tokenCookie.value) {
        // User is logged in, redirect to profile
        navigateTo(localePath('/profile'))
      } else {
        // User is not logged in, redirect to login
        navigateTo(localePath('/auth/login'))
      }
    }
  } catch (error: any) {
    console.error('Error:', error)
    showNotification(error.message || 'An error occurred', 'error')
  } finally {
    isLoading.value = false
  }
}
</script>