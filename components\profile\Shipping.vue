<template>
  <div>
    <div>
      <div class="justify-between items-center hidden md:flex">
        <div class="text-secondary font-bold font-secondaryFont text-[22px] uppercase rtl:font-notoSans">{{ $t('SHIPPING ADDRESSES') }}</div>
        <div v-if="shppingData?.first_name" @click="addAddressModal = true" class="flex justify-center items-start bg-orangeColor w-6 h-6 rounded-full cursor-pointer">
          <div>
            <ClientOnly>
              <font-awesome-icon class="text-thirdColor" icon="fa-solid fa-plus" />
            </ClientOnly>
          </div>
        </div>
      </div>
      <div class="bg-thirdColor border-y border-primary text-center py-3 font-bold font-secondaryFont rtl:font-notoSans text-[14px] mt-10 text-primary uppercase md:hidden">
        {{ $t('SHIPPING ADDRESSES') }}
      </div>
      <div class="mt-7 px-[30px] md:px-0">
        <div class="border md:border-2 bg-thirdColor md:bg-white border-primary p-4 rounded-2xl md:rounded-lg md:mt-5">
          <div class="flex justify-between items-center">
            <div class="font-primaryFont md:font-secondaryFont font-bold text-[14px] md:text-lg text-primary uppercase rtl:font-notoSans">{{ locale === 'en' ? shppingData?.address_1 : shppingData?.address_1_ar }}</div>
            <div class="cursor-pointer">
              <UButton  @click="openModal = true" variant="link">
                <ClientOnly>
                  <font-awesome-icon class="text-primary text-xl md:text-[25px]" icon="fa-solid fa-pen-to-square" />
                </ClientOnly>
              </UButton>
            </div>
            <UModal v-model="openModal">
              <div class="bg-thirdColor">
                <div class="flex justify-between items-center p-4 md:p-8">
                  <div class="font-secondaryFont font-bold text-secondary text-[12px] md:text-xl rtl:font-notoSans">{{ $t('EDIT SHIPPING ADDRESS') }}</div>
                  <div class="text-end">
                    <ClientOnly>
                      <font-awesome-icon class="text-primary text-xl md:text-[25px] cursor-pointer" icon="fa-solid fa-xmark" @click="openModal = false" />
                    </ClientOnly>
                  </div>
                </div>

                <div class="p-4 md:p-8 custom-form-responsive">
                  <UForm ref="form" :schema="schema" :state="state" class="md:space-y-4" @submit="onSubmit">
                    <div class="w-full mx-auto flex flex-col gap-2 md:gap-4">
                      <div class="relative text-field">
                        <UFormGroup name="address_1">
                          <input
                            class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                            variant="outline"
                            v-model="state.address_1"
                            required
                            placeholder=" "
                          >
                          <span
                            class="
                              uppercase
                              absolute
                              text-primary
                              font-primaryFont
                              font-semibold
                              opacity-1
                              font-base
                              z-[4]
                              ltr:left-[22px]
                              rtl:right-[22px]
                              rtl:font-notoSans
                              top-[13px]
                              transition-all
                              duration-100
                              ease-in-out"
                              :class="{
                                'placeholder-animation-with-bg' : state.address_1,
                              }"
                            >
                              {{ $t('Title') }}
                          </span>
                        </UFormGroup>
                      </div>

                      <div class="relative text-field">
                        <UFormGroup name="first_name">
                          <input
                            class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                            variant="outline"
                            v-model="state.first_name"
                            required
                            placeholder=" "
                          >
                          <span
                            class="
                              uppercase
                              absolute
                              text-primary
                              font-primaryFont
                              font-semibold
                              opacity-1
                              font-base
                              z-[4]
                              ltr:left-[22px]
                              rtl:right-[22px]
                              rtl:font-notoSans
                              top-[13px]
                              transition-all
                              duration-100
                              ease-in-out"
                              :class="{
                                'placeholder-animation-with-bg' : state.first_name,
                              }"
                            >
                              {{ $t('first name') }}
                          </span>
                        </UFormGroup>
                      </div>
                      <div class="relative text-field">
                        <UFormGroup name="last_name">
                          <input
                            class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                            variant="outline"
                            v-model="state.last_name"
                            required
                            placeholder=" "
                          >
                          <span
                            class="
                              uppercase
                              absolute
                              text-primary
                              font-primaryFont
                              font-semibold
                              opacity-1
                              font-base
                              z-[4]
                              ltr:left-[22px]
                              rtl:right-[22px]
                              rtl:font-notoSans
                              top-[13px]
                              transition-all
                              duration-100
                              ease-in-out"
                              :class="{
                                'placeholder-animation-with-bg' : state.last_name,
                              }"
                            >
                              {{ $t('last name') }}
                          </span>
                        </UFormGroup>
                      </div>
                      <div class="relative text-field">
                        <UFormGroup name="phone">
                          <input
                            class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                            variant="outline"
                            v-model="state.phone"
                            required
                            placeholder=" "
                          >
                          <span
                            class="
                              uppercase
                              absolute
                              text-primary
                              font-primaryFont
                              font-semibold
                              opacity-1
                              font-base
                              z-[4]
                              ltr:left-[22px]
                              rtl:right-[22px]
                              rtl:font-notoSans
                              top-[13px]
                              transition-all
                              duration-100
                              ease-in-out"
                              :class="{
                                'placeholder-animation-with-bg' : state.phone,
                              }"
                            >
                              {{ $t('phone') }}
                          </span>
                        </UFormGroup>
                      </div>
                      <div class="relative text-field">
                        <UFormGroup name="email">
                          <input
                            class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                            variant="outline"
                            v-model="state.email"
                            required
                            placeholder=" "
                          >
                          <span
                            class="
                              uppercase
                              absolute
                              text-primary
                              font-primaryFont
                              font-semibold
                              opacity-1
                              font-base
                              z-[4]
                              ltr:left-[22px]
                              rtl:right-[22px]
                              rtl:font-notoSans
                              top-[13px]
                              transition-all
                              duration-100
                              ease-in-out"
                              :class="{
                                'placeholder-animation-with-bg' : state.email,
                              }"
                            >
                              {{ $t('email') }}
                          </span>
                        </UFormGroup>
                      </div>
                      <div class="relative text-field">
                        <UFormGroup name="address_2">
                          <input
                            class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                            variant="outline"
                            v-model="state.address_2"
                            required
                            placeholder=" "
                          >
                          <span
                            class="
                              uppercase
                              absolute
                              text-primary
                              font-primaryFont
                              font-semibold
                              opacity-1
                              font-base
                              z-[4]
                              ltr:left-[22px]
                              rtl:right-[22px]
                              rtl:font-notoSans
                              top-[13px]
                              transition-all
                              duration-100
                              ease-in-out"
                              :class="{
                                'placeholder-animation-with-bg' : state.address_2,
                              }"
                            >
                              {{ $t('Address') }}
                          </span>
                        </UFormGroup>
                      </div>
                      <div class="custom-text-input custom-field-input-third">
                        <UFormGroup name="city">
                          <div class="relative text-field information">
                            <span
                              class="
                                uppercase
                                absolute
                                text-primary
                                font-primaryFont
                                font-semibold
                                opacity-1
                                font-base
                                z-[4]
                                ltr:left-[22px]
                                rtl:right-[22px]
                                rtl:font-notoSans
                                top-[13px]
                                transition-all
                                duration-100
                                ease-in-out"
                                :class="{
                                  'placeholder-animation-with-bg !opacity-100' : state.city,
                                }"
                              >
                                {{ $t('City') }}
                            </span>
                            <el-select
                              v-model="state.city"
                              placeholder=" "
                              style="width: 100%"
                            >
                              <el-option
                                v-for="item in cities.response"
                                :key="item.name"
                                :label="item.name"
                                :value="item.name"
                              />
                            </el-select>
                          </div>
                        </UFormGroup>
                      </div>
                    </div>
                    <div class="flex justify-center mb-[100px]">
                      <UButton
                        type="submit"
                        class="
                          font-thirdFont
                          font-bold
                          text-[15px]
                          md:text-lg
                          py-2
                          px-20
                          mt-5
                          uppercase
                          rounded-full
                          border
                          md:border-orangeColor
                          md:bg-orangeColor
                          bg-primary
                          border-primary
                          rtl:font-notoSans
                          hover:bg-orangeColor
                          hover:border-orangeColor
                          ease-in-out
                          duration-500
                        "
                        variant="solid"
                        :loading="loading"
                      >
                        <span v-if="!loading">{{ $t('UPDATE') }}</span>
                      </UButton>
                    </div>
                  </UForm>
                </div>
              </div>
            </UModal>
          </div>
          <div v-if="shppingData?.first_name" class="font-primaryFont md:font-secondaryFont md:text-base text-[14px] text-primary mt-1">
            {{ shppingData?.first_name }} {{ shppingData?.last_name }}, {{ shppingData?.city }}, {{ shppingData?.address_2 }}, {{ /^\d+$/.test(shppingData?.phone) ? shppingData?.phone : '' }}
          </div>
          <div v-else class="font-primaryFont md:text-base rtl:font-notoSans text-[14px] text-primary mt-1">
            {{ $t('Add Your Default Shipping Address') }}
          </div>
        </div>
        <div
          v-for="(address, index) in additionalAddress"
          :key="index"
          class="border md:border-2 bg-thirdColor md:bg-white border-primary p-4 rounded-2xl md:rounded-lg mt-5"
        >
          <div class="flex justify-between items-center">
            <div class="font-primaryFont md:font-secondaryFont font-bold text-[14px] md:text-lg text-primary rtl:font-notoSans uppercase">{{ locale === 'en' ? address.ship_address_1 : address.ship_address_1_ar }}</div>
            <div class="flex items-center gap-2">
              <div class="cursor-pointer">
                <UButton v-if="!loadingDelete" class="p-0" @click="deleteAddressFunc(index)" variant="link">
                  <ClientOnly>
                    <font-awesome-icon class="text-primary text-xl md:text-[25px]" icon="fa-solid fa-trash-can" />
                  </ClientOnly>
                </UButton>
                <div v-else>
                  <el-skeleton style="--el-skeleton-circle-size: 20px" animated>
                    <template #template>
                      <el-skeleton-item variant="circle" animated />
                    </template>
                  </el-skeleton>
                </div>
              </div>
              <div class="cursor-pointer">
                <UButton class="p-0" @click="editAddressFunc(index, address)" variant="link">
                  <ClientOnly>
                    <font-awesome-icon class="text-primary text-xl md:text-[25px]" icon="fa-solid fa-pen-to-square" />
                  </ClientOnly>
                </UButton>
              </div>
            </div>
          </div>
          <div class="font-primaryFont md:font-secondaryFont md:text-base text-[14px] text-primary mt-1">
            {{ address?.ship_fname }} {{ address?.ship_lname }}, {{ address?.ship_address_city }}, {{ address?.ship_address_2 }}, {{ address?.ship_address_phone }}
          </div>
        </div>
        <div class="flex justify-center md:hidden">
          <UButton
            type="submit"
            class="
              font-thirdFont
              font-bold
              text-lg
              text-thirdColor
              py-2
              px-10
              mt-6
              rounded-full
              border
              rtl:font-notoSans
              bg-primary
              border-primary
              hover:border-orangeColor
              hover:bg-orangeColor
              ease-in-out
              duration-500
              uppercase
            "
            @click="addAddressModal = true"
            variant="solid"
          >
            <span>{{ $t('ADD A NEW ADDRESS') }}</span>
          </UButton>
        </div>
      </div>
    </div>
    <ModalsNewaddress />
    <ModalsEditaddress />
  </div>
</template>
<script setup lang="ts">
const openModal = ref(false)
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { ElNotification } from 'element-plus'

const userData: any = useState("userData", () => [])
const profileData: any = useState("profileData", () => {})
const loading = ref(false)
const shppingData = ref<any>({})
const additionalAddress: any = useState("additionalAddress", () => [])


const { t, locale } = useI18n()

const schema = z.object({
  first_name: z.string()
  .min(3, t('First Name must be at least 3 characters')),
  last_name: z.string()
  .min(3, t('Last Name must be at least 3 characters')),
  address_1: z.string()
  .min(3, t('Title must be at least 3 characters')),
  address_2: z.string()
  .min(3, t('Adress must be at least 3 characters')),
  email: z.string()
  .min(3, t('Email must be at least 3 characters')),
  phone: z.string()
  .min(11, t('Phone Number must be at least 11 characters')),
  city: z.string()
  .min(1, t('City is required')),
})


type Schema = z.output<typeof schema>

const state = reactive({
  first_name: undefined,
  last_name: undefined,
  phone: undefined,
  address_1: undefined,
  address_2: undefined,
  email: undefined,
  city: undefined,
})

async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true
// // Do something with event.data
  const response = await $fetch(`/api/newBranch/customer/update/${Number(userData?.value?.id)}`, {
    method: 'post',
    body: { shipping: state, billing: state }
  }) as any

  const { items: usersData } = await fetchHock(`newBranch/address/${Number(userData?.value?.id)}`)
  profileData.value = usersData.value
  userData.value = usersData.value
  localStorage.setItem("userData", JSON.stringify(usersData.value))

  // Update shppingData to rerender the address
  shppingData.value = profileData?.value?.shipping

  successPopUp('Address Updated Successfully')

  openModal.value = false
  loading.value = false
}


const cities = useState("cities", () => {})
shppingData.value = profileData?.value?.shipping

if (profileData?.value?.shipping?.first_name) {
  Object.assign(state, {
    first_name: profileData?.value?.shipping?.first_name,
    last_name: profileData?.value?.shipping?.last_name,
    address_1: profileData?.value?.shipping?.address_1,
    address_2: profileData?.value?.shipping?.address_2,
    email: profileData?.value?.billing?.email,
    phone: /^\d+$/.test(profileData?.value?.shipping?.phone) ? profileData?.value?.shipping?.phone : '',
    city: profileData?.value?.shipping?.city,
  })
} else {
  Object.assign(state, {
    first_name: profileData?.value?.first_name,
    last_name: profileData?.value?.last_name,
    email: profileData?.value?.email,
    phone: /^\d+$/.test(profileData?.value?.username) ? profileData?.value?.username : '',
  })
}

const successPopUp = (message: string) => {
  ElNotification({
    // title: message,
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, message),
    // type: 'success',
    position: 'top-right',
  })
}


// Add new Address logic
const addAddressModal = useState("addAddressModal", () => false)
const editAddressModal = useState("editAddressModal", () => false)
const addAddressIndex = useState("addAddressIndex", () => false)
const addAddressItem = useState("addAddressItem", () => false)

addAddressIndex.value = false
const editAddressFunc = (index: any, item: any) => {
  addAddressIndex.value = index
  addAddressItem.value = item
  editAddressModal.value = true
}

const loadingDelete = ref(false)

const deleteAddressFunc = async (index: any) => {
  loadingDelete.value = true
  const response = await $fetch(`/api/newBranch/address/deleteAddress`, {
    method: 'delete',
    body: {
      userid: userData?.value?.id,
      row_index: index
    }
  }) as any
  
  const { items: usersData } = await fetchHock(`newBranch/address/${Number(userData?.value?.id)}`)
  profileData.value = usersData.value
  userData.value = usersData.value
  localStorage.setItem("userData", JSON.stringify(usersData.value))

  usersData.value.meta_data.map((el: any) => {
    if (el.key === 'additional_shipping_addresses') {
      additionalAddress.value = el.value
    }
  })
  
  successPopUp(t('Address Deleted Successfully'))
  loadingDelete.value = false
}
</script>