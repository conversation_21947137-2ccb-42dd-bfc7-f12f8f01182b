<template>
  <div>
    <UModal v-model="reviewModal" class="orderModal">
      <div class="rounded-[30px]">
        <div class="bg-thirdColor text-end px-5 pt-2 md:hidden cursor-pointer" @click="reviewModal = false">
          <ClientOnly>
            <font-awesome-icon class="text-primary text-[20px]" icon="fa-solid fa-circle-xmark" />
          </ClientOnly>
        </div>
        <div class="bg-thirdColor p-5 flex justify-between items-center">
          <div class="text-secondary font-bold text-xl font-secondaryFont uppercase">
            {{ $t('Order') }} {{ reviewOrder?.id }} | {{ $t('Products Review') }} ({{ reviewOrder.items.length }})
          </div>
          <div class="flex items-center gap-2">
            <div class="cursor-pointer w-[30px] h-[30px] bg-orangeColor rounded-full flex justify-center items-center" @click="previousProduct">
              <Icon class="text-thirdColor cursor-pointer" name="carbon:chevron-left" size="25" />
            </div>
            <div class="cursor-pointer w-[30px] h-[30px] bg-orangeColor rounded-full flex justify-center items-center" @click="nextProduct">
              <Icon class="text-thirdColor cursor-pointer" name="carbon:chevron-right" size="25" />
            </div>
          </div>
        </div>

        <div class="p-5">
          <div v-if="reviewOrder.items.length > 0" :key="reviewOrder.items[currentProductIndex].variation_id" class="border-none-custom mt-[20px] flex items-center justify-between h-fit w-full pb-5">
            <div class="flex items-center gap-3 md:gap-10">
              <div class="relative w-[30%] md:w-[125px] md:h-[110px] flex items-center">
                <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary hidden md:block" :src="reviewOrder.items[currentProductIndex]?.brand_logo"/>
                <img class="md:absolute md:top-0 md:left-[35px]" :src="reviewOrder.items[currentProductIndex]?.image"/>
              </div>
              <div class="w-[70%]">
                <div class="text-[14px] md:text-xl italic font-semibold text-primary">{{ reviewOrder.items[currentProductIndex].product_name }}</div>
                <div class="text-[12px] md:text-lg italic font-semibold text-orangeColor">
                  <span>
                    {{ reviewOrder.items[currentProductIndex].excerpt }} 
                  </span>
                </div>
                <div class="flex gap-4">
                </div>
              </div>
            </div>
          </div>

          <!-- Rating Section -->
          <div class="mt-4 flex justify-between items-center flex-col md:flex-row">
            <div class="font-secondaryFont md:text-lg text-orangeColor">{{ $t('RATE THIS PRODUCT') }}</div>
            <el-rate v-model="currentProductFeedback.rating" :colors="colors" size="large" @change="updateRating" />
          </div>

          <!-- Comment Selection Section -->
          <div class="mt-4">
            <div class="flex gap-2 mt-2 flex-wrap uppercase">
              <button
                v-for="comment in predefinedComments"
                :key="comment"
                :class="[ 
                  'py-4 px-2 rounded font-secondaryFont text-xs text-primary font-bold w-[48%] md:w-[32%]',
                  currentProductFeedback?.comment === comment
                    ? 'bg-orangeColor text-white'
                    : 'bg-filter text-primary'
                ]"
                @click="toggleComment(comment)"
              >
                {{ comment }}
              </button>
            </div>
          </div>
        </div>

        <div class="bg-thirdColor p-5 text-center pb-[60px] uppercase">
          <UButton
            class="disabled:border-primary uppercase font-thirdFont font-bold text-lg py-2 px-20 rounded-full box-shadow border border-orangeColor bg-orangeColor hover:bg-orangeColor hover:border-orangeColor ease-in-out duration-500"
            variant="solid"
            @click="submitFeedback"
            :loading="loading"
          >
            {{ $t('Submit') }}
          </UButton>
        </div>
      </div>
    </UModal>
  </div>
</template>
<script setup lang="ts">
// Modal and Product Review State
const reviewModal = useState("reviewModal", () => false);
const userData: any = useState("userData", () => [])
const reviewOrder: any = useState("reviewOrder", () => {})
const currentProductIndex = ref(0);
const { items } = await fetchHock(`newBranch/comments`)

// Function to go to the previous product
const previousProduct = () => {
  currentProductIndex.value = (currentProductIndex.value - 1 + reviewOrder.value.items.length) % reviewOrder.value.items.length;
};

// Function to go to the next product
const nextProduct = () => {
  currentProductIndex.value = (currentProductIndex.value + 1) % reviewOrder.value.items.length;
};

// Object to hold rating and comment data for each product by product ID
const productFeedback: any = reactive({});

// Initialize feedback data when the component is loaded or when `reviewOrder` changes
const initializeFeedback = () => {
  reviewOrder?.value?.items?.forEach((item: any) => {
    if (!productFeedback[item.variation_id]) {
      productFeedback[item.variation_id] = { rating: 0, comment: null }; // Set initial rating to 0 and no selected comment
    }
  });
};

// Ensure that `initializeFeedback` runs on component mount and when `reviewOrder` changes
watch(reviewOrder, initializeFeedback, { immediate: true });

// Colors for the rating component
const colors = ref(['#FF671F', '#FF671F', '#FF671F', '#FF671F', '#FF671F']);

// Dynamic comments based on star rating
const dynamicComments = ref();

dynamicComments.value = items.value
// Predefined comment labels
const predefinedComments = ref([]);


// Computed property to get the current product's feedback safely
const currentProductFeedback = computed(() => {
  const productId = reviewOrder.value.items[currentProductIndex.value]?.variation_id;
  return productFeedback[productId] || { rating: 0, comment: null };
});

// Function to handle comment selection (single selection mode)
const toggleComment = (comment: any) => {
  const currentProductId = reviewOrder.value.items[currentProductIndex.value].variation_id;
  // // Set selected comment, or deselect if already selected
  productFeedback[currentProductId].comment = productFeedback[currentProductId].comment === comment ? null : comment;
};

// Function to handle rating change
const updateRating = (value: any) => {
  const currentProductId = reviewOrder.value.items[currentProductIndex.value].variation_id;
  productFeedback[currentProductId].rating = value;

  // Dynamically update predefined comments based on the rating
  predefinedComments.value = dynamicComments.value[value] || [];
};


const loading = ref(false)
const order: any = useState("order", () => {})
const paginationPages: any = useState("paginationPages", () => 0)
const ordersCount: any = useState("ordersCount", () => 0)
const perPage: any = useState("perPage", () => 10)
const page: any = useState("page", () => 1)

// Function to handle submission
const submitFeedback = async () => {
  loading.value = true
  const feedbackData = Object.entries(productFeedback).map(([id, feedback]: any) => ({
    variation_id: id,
    rate: feedback.rating,
    comment_content: feedback.comment,
  }))


  const data = ref({
    "user_id": Number(userData?.value?.id),
    "reviews": feedbackData,
    "order_id": reviewOrder?.value?.id
  })

  const response = await $fetch(`/api/newBranch/reviews`, {
    method: 'post',
    body: data.value
  }) as any

  const { items: orders } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&per_page=${perPage.value}&page=${page.value}`)
  ordersCount.value = orders?.value?.total_orders_count
  paginationPages.value = orders.value.total_pages
  const filteredItems = orders?.value?.data?.filter((item: any) => item.status !== 'pending-cart')

  order.value = orders.value

  reviewModal.value = false
  loading.value = false
};
</script>
<style scoped>
.orderModal .relative.text-left {
  border-radius: 20px !important;
  overflow: hidden !important;
}
.el-rate {
  --el-rate-void-color: #FF671F !important;
}
</style>
