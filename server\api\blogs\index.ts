export default defineEventHandler(async (event) => {
  try {
    const url = `${process.env.WP_API_URL}posts`
    const method = 'GET'

    const queryObject = getQuery(event)

    const queryString = Object.entries(queryObject).map(([key, value]) => {
       // Check if the value is not undefined, not null, and not an array
       if (value!== undefined && value!== null &&!Array.isArray(value)) {
         // If the value is defined, encoded, and not an array, encode both the key and the value
         // Convert value to a string if it's an object
         const stringValue = typeof value === 'object'? JSON.stringify(value) : value;
         return `${encodeURIComponent(key)}=${encodeURIComponent(stringValue)}`;
       }
       // If the value is undefined, null, or an array, return an empty string
       return '';
     }).filter(Boolean).join('&'); // Filter out empty strings and join the array of strings into a single string with '&' as the separator

    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

    const response = await fetch(`${url}?${queryString}`, {
      method: method,
      headers: headers,
    })

    const data = await response.json()
    const totalPages = response.headers.get('X-WP-TotalPages')
    const totalPosts = response.headers.get('X-WP-Total')
    
    return {
      data: data,
      pages: totalPages,
      posts: totalPosts,
    }

    return data
  } catch (error) {
    console.error(error)
    return {
      error: (error as Error).message,
    }
  }
})