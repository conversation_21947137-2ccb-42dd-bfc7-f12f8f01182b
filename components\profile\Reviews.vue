<template>
  <div class="md:px-[30px] md:max-w-[1200px] md:mx-auto relative">
    <div class="md:mt-10" v-if="!loading">
      <div class="justify-between items-center mb-10 hidden md:flex">
        <div class="text-secondary font-bold font-secondaryFont text-[22px] uppercase rtl:font-notoSans">
          {{ $t('My REVIEWS') }} ({{ reviews?.total_comments }})
        </div>
        <div class="reviews-select-status flex gap-2">
          <el-select
            v-model="filterValue"
            :placeholder="$t('RATE')"
            size="large"
            @change="handleFilterChange"
          >
            <el-option
              label="All"
              value=""
            />
            <el-option
              v-for="item in [1,2,3,4,5]"
              :key="item"
              :label="`${item} Stars`"
              :value="item"
            />
          </el-select>
        </div>
      </div>

      <!-- Mobile Header -->
      <div class="flex justify-between items-center mb-10 bg-thirdColor border-b border-primary px-[30px] py-3 md:hidden">
        <div class="text-primary font-bold font-secondaryFont text-[15px] uppercase rtl:font-notoSans">
          {{ $t('My reviews') }} ({{ reviews?.total_comments }})
        </div>
        <!-- ... rest of mobile header ... -->
      </div>

      <!-- Desktop Reviews -->
      <div v-if="!loadingReviews && reviews?.comments.length" class="mb-10 hidden md:block">
        <div v-for="review in reviews?.comments" :key="review.product.id" class="border border-primary rounded-[12px] flex justify-between overflow-hidden mt-5">
          <div class="p-6 w-[49%]">
            <div class="font-primaryFont text-primary text-xl font-bold rtl:font-notoSans">{{ locale === 'en' ? review?.title : review?.title_ar }}</div>
            <div class="flex items-center gap-10 mt-4">
              <div class="relative w-[125px] h-[110px] flex items-center">
                <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary" :src="review.product?.brand_logo"/>
                <img class="absolute top-0 ltr:left-[35px] rtl:right-[35px]" :src="review.product.image"/>
              </div>
              <div>
                <div class="font-primaryFont text-xl italic font-semibold text-primary rtl:font-notoSans">{{ review?.product?.brand_name }}</div>
                <div class="font-primaryFont text-lg italic font-semibold text-orangeColor">{{ locale === 'en' ? review?.product?.excerpt : review?.product?.excerpt_ar }}</div>
                <div class="flex gap-4">
                  <div class="flex flex-col italic font-medium font-primaryFont text-primary">
                    <span class="text-[12px] rtl:font-notoSans">{{ locale === 'en' ? review?.product?.load_index?.name : review?.product?.load_index?.name_ar }}: </span>
                    <span class="text-[13px] mt-[-4px]">{{ review?.product?.load_index?.value }}</span>
                  </div>
                  <div class="flex flex-col italic font-medium font-primaryFont text-primary">
                    <span class="text-[12px] rtl:font-notoSans">{{ locale === 'en' ? review?.product?.speed_index?.name : review?.product?.speed_index?.name_ar }}: </span>
                    <span class="text-[13px] mt-[-4px]">{{ review?.product?.speed_index?.value }}</span>
                  </div>
                  <div class="flex flex-col italic font-medium font-primaryFont text-primary">
                    <span class="text-[12px] rtl:font-notoSans">{{ locale === 'en' ? review?.product?.origin?.name : review?.product?.origin?.name_ar }}: </span>
                    <span class="text-[13px] mt-[-4px]">{{ review?.product?.origin?.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="p-6 w-[49%] bg-cartColor border-l border-primary">
            <div class="flex justify-between items-center">
              <div class="flex items-center gap-1">
                <template v-for="star in 5" :key="star">
                  <svg width="26" height="25" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.74471 7.69468L9.00476 7.65676L9.12101 7.42107L12.2508 1.07514C12.6304 0.306413 13.7258 0.310046 14.1056 1.07553C14.1057 1.07564 14.1057 1.07576 14.1058 1.07587L17.2353 7.42107L17.3515 7.65676L17.6116 7.69468L24.6144 8.71562L24.6147 8.71567C25.4599 8.83825 25.8 9.88194 25.1861 10.4797L25.1859 10.4798L20.1196 15.4167L19.9312 15.6003L19.9757 15.8595L21.174 22.8332C21.3186 23.6787 20.4268 24.3167 19.6755 23.9216L19.6754 23.9216L13.4108 20.6287L13.1781 20.5064L12.9455 20.6287L6.68086 23.9216L6.67929 23.9224C5.93146 24.3189 5.03712 23.6817 5.18231 22.8332L6.38054 15.8595L6.42508 15.6003L6.23671 15.4167L1.17035 10.4798L1.17021 10.4797C0.556323 9.88193 0.896404 8.83825 1.74156 8.71567L1.74192 8.71562L8.74471 7.69468Z" 
                    :fill="star <= review.rating ? '#FF671F' : 'none'" 
                    stroke="#FF671F"/>
                  </svg>
                </template>
              </div>
            </div>
            <div class="mt-6 font-medium text-lg text-primary font-primaryFont rtl:font-notoSans">
              {{ locale === 'en' ? review?.comment_content : review?.comment_content_ar }}
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="!loadingReviews" class="text-primary rtl:font-notoSans font-bold text-3xl text-center font-primaryFont mt-14 mb-20 hidden md:block">
        {{ $t('No Reviews Found') }}
      </div>
      <div v-else class="text-center py-20 rtl:font-notoSans font-bold text-3xl text-orangeColor hidden md:block">
        {{ $t('Loading....') }}
      </div>
      <!-- Mobile Reviews -->
      <div class="mb-[150px] px-[30px] md:hidden">
        <div  v-if="!loadingReviews && reviews?.comments.length"  v-for="review in reviews?.comments" :key="review.product.id" class="border border-primary rounded-[12px] overflow-hidden mt-5">
          <div>
            <div class="bg-thirdColor border-b border-primary p-3 flex justify-between items-center">
              <div class="font-primaryFont text-primary text-[14px] font-semibold rtl:font-notoSans">{{ locale === 'en' ? review?.title : review?.title_ar }}</div>
              <!-- <div>
                <ClientOnly>
                  <font-awesome-icon class="text-primary text-xl md:text-[25px]" icon="fa-solid fa-pen-to-square" />
                </ClientOnly>
              </div> -->
            </div>
            <div class="flex items-center gap-4 mt-2 p-3">
              <div class="relative flex items-center">
                <img class="w-[50px]" :src="review.product.image"/>
              </div>
              <div>
                <div class="font-primaryFont text-[13px] italic font-semibold text-primary rtl:font-notoSans">{{ locale === 'en' ? review?.product?.brand_name : review?.product?.brand_name_ar }}</div>
                <div class="font-primaryFont text-[12px] italic font-semibold text-orangeColor">{{ review?.product?.excerpt }}</div>
              </div>
            </div>
            <div class="flex items-center gap-1 px-3">
              <template v-for="star in 5" :key="star">
                <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.80736 1.32148L3.49279 3.98686L0.551607 4.41566C0.0241671 4.49216 -0.187212 5.1424 0.195283 5.51483L2.32316 7.58835L1.81988 10.5174C1.72929 11.0469 2.28693 11.4435 2.75397 11.1959L5.38513 9.81285L8.01629 11.1959C8.48334 11.4415 9.04098 11.0469 8.95039 10.5174L8.4471 7.58835L10.575 5.51483C10.9575 5.1424 10.7461 4.49216 10.2187 4.41566L7.27747 3.98686L5.9629 1.32148C5.72736 0.846384 5.04491 0.840345 4.80736 1.32148Z" 
                  :fill="star <= review.rating ? '#FF671F' : 'none'" 
                  stroke="#FF671F"/>
                </svg>
              </template>
            </div>
            <div class="font-primaryFont text-[17px] text-primary px-3 pb-3 mt-1 rtl:font-notoSans">
              {{ locale === 'en' ? review.comment_content : review.comment_content_ar }}
            </div>
          </div>
        </div>
        <div v-else-if="!loadingReviews" class="text-primary rtl:font-notoSans font-bold text-3xl text-center font-primaryFont mt-14 mb-20">
          {{ $t('No Reviews Found') }}
        </div>
        <div v-else class="text-center py-20 font-bold text-3xl text-orangeColor rtl:font-notoSans">
          {{ $t('Loading....') }}
        </div>
      </div>

      <!-- Pagination -->
      <div class="md:flex justify-center mt-4 pb-10 border-b border-primary hidden" v-if="reviews?.total_pages > 1">
        <div class="flex gap-3">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              uppercase
              h-[40px]
              border-2
              border-primary
              rounded-full
              flex
              items-center
              justify-center"
          >
            <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
            </svg>
            <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
            </svg>
          </button>
          <button
            v-for="pageNumber in totalPagesArray" 
            :key="pageNumber"
            @click="selectPage(pageNumber)"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              uppercase
              border-2
              border-primary
              rounded-full"
            :class="{ 'bg-orangeColor !border-orangeColor text-thirdColor': currentPage === pageNumber }"
          >
            {{ pageNumber }}
          </button>
          <button
            @click="nextPage"
            :disabled="currentPage === paginationPages"
            class="
              text-primary
              font-secondaryFont
              font-bold
              w-[40px]
              h-[40px]
              border-2
              border-primary
              uppercase
              rounded-full
              flex
              items-center
              justify-center"
          >
            <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
            </svg>
            <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div v-else class="text-center py-20 font-bold text-3xl text-orangeColor rtl:font-notoSans">
      {{ $t('Loading....') }}
    </div>
    

    <div v-if="!buttonDisabled" class="bg-thirdColor rtl:font-notoSans text-sm text-center font-secondaryFont py-5 uppercase px-[50px] max-w-[1200px] mx-auto fixed w-full bottom-[59px] z-[100] md:hidden">
        {{ $t('There are 10 reviews to see.') }}
        <div>
          <UButton
            class="
              mt-2
              font-bold
              text-[15px]
              py-2
              w-full
              flex items-center justify-center
              text-thirdColor
              font-primaryFont
              border-2
              border-orangeColor
              uppercase
              bg-orangeColor
              rounded-full
              button-shadow
              transition-all
              duration-500
              ease-in-out
              rtl:font-notoSans
              hover:text-primary
              hover:bg-secondary
              hover:border-secondary
              hover:text-thirdColor"
              @click="loadMore"
              :disabled="buttonDisabled"
              >
              {{ $t('LOAD MORE') }}
            </UButton>
        </div>
      </div>
  </div>
</template>
<script setup lang="ts">
const filterValue = ref()
const currentPage = ref(1)
const postsPerPage = ref(5)
const { locale } = useI18n()

const paginationPages: any = ref()
const reviews = ref<any>()
const buttonDisabled = ref(false)
const loading: any = useState("loading", () => false)
const loadingReviews: any = useState("loadingReviews", () => false)
const userData: any = useState("userData", () => [])

const fetchReviews = async () => {
  loadingReviews.value = true
  const { items } = await fetchHock(
    `newBranch/reviews/${Number(userData?.value?.id)}?page=${currentPage.value}&posts_per_page=${postsPerPage.value}${filterValue.value ? `&rate=${filterValue.value}` : ''}`
  )
  paginationPages.value = items?.value?.total_pages
  reviews.value = items?.value
  loadingReviews.value = false
}

const handleFilterChange = async () => {
  currentPage.value = 1 // Reset to first page when filter changes
  await fetchReviews()
}

const updatePage = async (newPage: number) => {
    // Sample object
  currentPage.value = newPage
  await fetchReviews()
}

const pagesPerSet = 5

// Function to get the current set of pages to display (Sliding Window)
const getCurrentPageSet = () => {
  const start = Math.max(1, currentPage.value - Math.floor(pagesPerSet / 2))
  const end = Math.min(start + pagesPerSet - 1, paginationPages.value)

  // Adjust the start if the end is less than 10 pages away from paginationPages
  const adjustedStart = Math.max(1, end - pagesPerSet + 1)

  return Array.from({ length: end - adjustedStart + 1 }, (_, i) => adjustedStart + i)
}

// Get the current set of pages
const totalPagesArray = computed(() => {
  return getCurrentPageSet()
})

// Function to go to the previous page
const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    updatePage(currentPage.value)
  }
};

// Function to go to the next page
const nextPage = () => {
  if (currentPage.value < paginationPages.value) {
    currentPage.value++
    updatePage(currentPage.value)
  }
}
const selectPage = (pageNumber: number) => {
  if (currentPage.value !== pageNumber) {
    currentPage.value = pageNumber
    updatePage(pageNumber)
  }
}


// Load more handler for mobile
const loadMore = async () => {
  if (reviews.value.total_pages == currentPage.value) {
    buttonDisabled.value = true
    return
  }
  currentPage.value++
  await fetchReviews()
}

onMounted(async () => {
  await fetchReviews()
})
</script>

<style>
.reviews-select-status {
  width: 150px;
}
.reviews-select-status .el-select__wrapper {
  border: 1px solid #263238 !important;
  background-color: #E9EAC8 !important;
}
.reviews-select-status .el-select__wrapper .el-select__selected-item.el-select__placeholder {
  font-style: italic;
  font-weight: 600;
  font-size: 16px;
  color: #1D3C34;
  font-family: "Montserrat", sans-serif !important;
}
</style>