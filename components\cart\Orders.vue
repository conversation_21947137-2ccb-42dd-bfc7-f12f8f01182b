<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto hidden md:block">
      <div class="border-b border-primary pb-20">
        <div class="table-responsive" style="overflow-x:auto;">
          <table class="w-full cartTable">
            <tr class="border-b border-primary">
              <th class="font-bold text-primary text-lg text-start pb-3">
                <div class="flex items-center gap-5">
                  <div class="cartCheckBox ms-4">
                    <el-checkbox v-model="checkedAll" size="large" @change="toggleAllCheckboxes" />
                  </div>
                  {{ $t('PRODUCT') }}
                </div>
              </th>
              <th class="font-bold text-primary text-lg text-center pb-3 toHidden">{{ $t('PRICE') }}</th>
              <th class="font-bold text-primary text-lg text-center pb-3 toHidden">{{ $t('QUANTITY') }}</th>
              <th class="font-bold text-primary text-lg text-center pb-3 toHidden">{{ $t('TOTAL') }}</th>
            </tr>
            <tr v-for="(product, index) in pendingCart?.length ? pendingCart[0]?.items : []" :key="product.product_id" class="border-b border-primary">
              <td class="md:py-6 py-3 lg:w-[620px] w-[410px]">
                <div class="flex items-center gap-5 forResponsive">
                  <div class="cartCheckBox ms-4">
                    <el-checkbox v-model="checkboxStates[index]" size="large" @change="updateSelectedProducts(index)" />
                  </div>
                  <div class="flex items-center gap-14">
                    <div class="relative w-[125px] h-[110px] flex items-center cursor-pointer" @click="navigateToProduct(product)">
                      <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary" :src="product?.brand_logo" :alt="locale === 'en' ? product?.product_name : product?.product_name_ar"/>
                      <img class="absolute top-0 rtl:right-[35px] ltr:left-[35px]" :src="product?.image"/>
                    </div>
                    <div class="cursor-pointer" @click="navigateToProduct(product)">
                      <div class="text-xl italic font-semibold text-primary">{{ locale === 'en' ? product?.product_name : product?.product_name_ar }}</div>
                      <div class="text-lg italic font-semibold text-orangeColor">
                        {{ locale === 'en' ? product?.excerpt : product?.excerpt_ar }}
                      </div>
                      <div class="flex gap-4">
                        <template v-if="Array.isArray(product?.meta_data)">
                          <div
                            class="flex flex-col italic font-medium text-primary"
                            v-for="(varData, index) in product?.meta_data"
                            :key="index"
                          >
                            <span class="text-[12px]">{{ locale === 'en' ? varData?.name : varData?.name_ar }}: </span>
                            <span class="text-[13px] mt-[-4px]">{{ locale === 'en' ? varData?.value : varData?.value_ar }} </span>
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </td>
              <td class="md:py-6 py-3">
                <div class="text-center font-thirdFont rtl:font-notoSans font-bold text-xl lg:text-[25px] text-primary">
                  <div>{{ $t('EGP') }}</div>
                  <div class="rtl:font-secondaryFont">{{ formatPrice(Number(product.price)) }}</div>
                </div>
              </td>
              <td class="md:py-6 py-3">
                <div class="flex justify-center items-center">
                  <div class="flex items-center gap-2 lg:gap-4" v-if="!loading[index]">
                    <span class="font-bold text-[30px] leading-3 text-primary cursor-pointer" @click="incrementCounter(index, product)">+</span>
                    <span class="font-bold text-[25px] leading-3 text-primary font-thirdFont">{{ product.quantity }}</span>
                    <span class="font-bold text-[40px] leading-3 text-primary cursor-pointer" @click="decreaseCounter(index, product)">-</span>
                  </div>
                  <div v-else>
                    <UButton
                        class="
                          text-primary
                          uppercase
                          bg-transparent
                          disabled:border-transparent
                          disabled:bg-transparent"
                          :loading="true"
                      >
                      </UButton>
                  </div>
                </div>
              </td>
              <td class="md:py-6 py-3">
                <div class="text-center font-thirdFont font-bold text-xl lg:text-[25px] text-orangeColor">
                  <div class="rtl:font-notoSans">{{ $t('EGP') }}</div>
                  <div class="rtl:font-secondaryFont">{{ formatPrice(Number(product?.total_incl_tax)) }}</div>
                </div>
              </td>
            </tr>
          </table>
        </div>
        <div class="bg-thirdColor border-b border-primary py-10">
          <div class="flex items-center justify-between px-10">
            <h2 class="text-primary font-bold text-2xl">
              {{ $t('SUB TOTAL') }}
            </h2>
            <h2 class="text-primary font-bold font-thirdFont text-[25px] rtl:flex rtl:gap-2 rtl:flex-row-reverse">
              <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(subTotal)) }}</span>
            </h2>
          </div>
        </div>

        <div class="flex justify-center mt-10">
          <UButton
            class="
              rounded-full
              flex
              items-center
              justify-center
              font-bold
              text-xl
              py-3
              px-20
              lg:px-32
              font-thirdFont
              text-thirdColor
              uppercase
              bg-orangeColor
              border-2
              border-orangebg-orangeColor
              hover:bg-primary
              hover:border-primary
              transition-all
              duration-500
              ease-in-out"
              @click="buyOrder"
          >
            {{ $t('Proceed to Buy') }}
          </UButton>
        </div>
      </div>
    </div>
    <div class="md:hidden mb-[170px]">
      <div class="w-full">
        <div
          v-for="(product, index) in pendingCart?.length ? pendingCart[0]?.items : []"
          :key="product.product_id"
          class="w-full"
        >
          <div class="flex gap-2 items-center px-[30px] cursor-pointer" @click="navigateToProduct(product)">
            <div class="relative">
              <img class="mx-auto w-[200px] md:w-full" :src="product?.image" :alt="locale === 'en' ? product?.product_name : product?.product_name_ar"/>
              <img class="absolute bottom-0 rtl:right-[25%] ltr:left-[25%] w-[60px] h-[60px] rounded-full border border-primary" :src="product?.brand_logo" :alt="locale === 'en' ? product?.product_name : product?.product_name_ar" />
            </div>
            <div>
              <h2 class="text-primary italic font-semibold text-[15px] lines-1">{{ locale === 'en' ? product?.product_name : product?.product_name_ar }}</h2>
              <div class="text-orangeColor italic font-semibold text-sm mt-[-5px] lines-1">{{ locale === 'en' ? product?.excerpt : product?.excerpt_ar }}</div>
              <div class="flex flex-col mt-1">
                <template v-if="Array.isArray(product?.meta_data)">
                  <div
                    class="flex gap-2 italic text-xs text-primary"
                    v-for="(varData, index) in product?.meta_data"
                    :key="index"
                  >
                    <span class="font-semibold">{{ locale === 'en' ? varData?.name : varData?.name_ar }}: </span>
                    <span class="font-bold">{{ locale === 'en' ? varData?.value : varData?.value_ar }} </span>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="bg-thirdColor px-[30px] py-2 mt-4 mb-4">
            <div class="flex justify-between items-center border-b border-primary pb-1">
              <div class="text-[13px] text-primary">{{ $t('PRICE') }}</div>
              <div class="font-semibold text-primary text-[13px] rtl:flex rtl:gap-2 rtl:flex-row-reverse">
                <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(product.price)) }}</span>
              </div>
            </div>
            <div class="mt-2 flex justify-between items-center">
              <div>
                <h2 class="text-[11px] text-orangeColor">
                  {{ $t('SUB TOTAL') }}
                </h2>
                <h2 class="text-[15px] font-bold text-orangeColor rtl:flex rtl:gap-2 rtl:flex-row-reverse">
                  <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(product.price)) }}</span>
                </h2>
              </div>
              <div class="flex items-center gap-3">
                <span
                  @click="decreaseCounter(index, product)"
                  class="w-[18px] h-[18px] bg-primary rounded-full flex items-center justify-center"
                >
                  <ClientOnly>
                    <font-awesome-icon class="text-thirdColor text-[14px]" icon="fa-solid fa-minus"/>
                  </ClientOnly>
                </span>
                <span v-if="!loading[index]" class="font-bold text-[20px] w-[32px] h-[32px] leading-3 text-primary font-thirdFont flex items-center justify-center">{{ product.quantity }}</span>
                <div v-else>
                  <UButton
                    class="
                      text-primary
                      uppercase
                      bg-transparent
                      disabled:border-transparent
                      disabled:bg-transparent"
                      :loading="true"
                  >
                  </UButton>
                </div>
                <span
                  @click="incrementCounter(index, product)"
                  class="w-[18px] h-[18px] bg-primary rounded-full flex items-center justify-center"
                >
                  <ClientOnly>
                    <font-awesome-icon class="text-thirdColor text-[14px]" icon="fa-solid fa-plus"/>
                  </ClientOnly>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123] md:hidden">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans flex justify-center gap-1 items-center">
        <span class="rtl:mt-0.5">{{ $t('YOUR CART') }} ({{ totalItemss || '0' }})</span> |
        <span class="rtl:flex rtl:gap-2 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span>
        </span>
      </div>
      <div class="flex justify-center">
        <NuxtLink
          class="
            rounded-full
            flex
            items-center
            justify-center
            font-bold
            text-[15px]
            w-full
            py-2
            font-thirdFont
            text-thirdColor
            uppercase
            bg-orangeColor
            border-2
            border-orangeColor
            rtl:font-notoSans
            hover:bg-orangeColor
            hover:border-orangeColor
            focus:bg-orangeColor
            focus:border-orangeColor
            transition-all
            duration-500
            ease-in-out"
            @click="mobileBuyOrder"
        >
          {{ $t('CHECKOUT') }}
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()

const totalPrices = ref(0)

// Cart Logic
const userData: any = useState("userData", () => {})
const pendingCart = ref<any>([])
const subTotal = ref<any>(0)
const checkedAll = ref(false)
const checkboxStates = ref()
const selectedProducts = ref<any>([])
const cartItems: any = useState("cartItems", () => {})
const loading = ref<boolean[]>([])

const { locale } = useI18n()
const localePath = useLocalePath()

// Function to navigate to product detail page
const navigateToProduct = (product: any) => {
  // Determine product type (simple or variable)
  const productType = product.variation_id ? 'variable' : 'simple'
  // Navigate to product page
  router.push(localePath(`/product/${productType}/${product.variation_id ?? product.product_id}`))
}

// const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
// cartItems.value = items?.value
if(process.client){
  if (userData?.value?.id) {
    setTimeout(async () => {
      const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
      pendingCart.value = items?.value?.single
      cartItems.value = items?.value
      subTotal.value = items?.value?.single?.[0]?.total

      checkboxStates.value = pendingCart.value.length ? pendingCart.value[0]?.items.map(() => true) : []  // Initialize as an empty array if pendingCart is empty
    }, 1)
  }
}

const totalItemss = ref(0)

totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)

watch(cartItems, async (current) => {
  if (cartItems?.value?.single?.[0]?.items?.length) {
    totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
      return acc + Number(item.quantity || 0)
    }, 0)
  }
})

const toggleAllCheckboxes = () => {
  const newState = checkedAll.value
  checkboxStates.value = checkboxStates.value.map(() => newState)
  if (newState && pendingCart.value?.length) {
    selectedProducts.value = []
    selectedProducts.value = [...pendingCart.value[0].items]
  } else {
    selectedProducts.value = []
  }
}

// Update selected products when individual checkboxes change
const updateSelectedProducts = (index: number) => {
  if (!pendingCart.value || !pendingCart.value.length) return

  const product = pendingCart.value[0].items[index]
  const selectedIndex = selectedProducts.value.findIndex((p: any) => p.id === product.product_id)


  if (checkboxStates.value[index]) {
    if (selectedIndex === -1) {
      selectedProducts.value.push(product)
    }
  } else {
    if (selectedIndex !== -1) {
      selectedProducts.value.splice(selectedIndex, 1)
    }
  }
}

const incrementCounter = async (index: number, product: any) => {
  loading.value[index] = true
  const quantityPrice = product.quantity += 1
  const response = await $fetch(`/api/orders/update/${pendingCart.value[0].id}`, {
    method: 'put',
    body: {
      customer_id: Number(userData?.value?.id),
      line_items: [
        {
          id: product.line_item_id,
          quantity: quantityPrice,
          variation_id: product.variation_id ? product.variation_id : null,
          product_id: product.product_id,
          subtotal: String(product.price * quantityPrice),
          total: String(product.price * quantityPrice)
        }
      ]
    }
  }) as any

  const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
  cartItems.value = items?.value

  pendingCart.value = items?.value?.single
  loading.value[index] = false
}

const decreaseCounter = async (index: number, product: any) => {
  if (pendingCart.value[0].items[index].quantity > 1) {
    loading.value[index] = true
    const quantityPrice = product.quantity -= 1
    const response = await $fetch(`/api/orders/update/${pendingCart.value[0].id}`, {
      method: 'put',
      body: {
        customer_id: Number(userData?.value?.id),
        line_items: [
          {
            id: product.line_item_id,
            quantity: quantityPrice,
            variation_id: product.variation_id ? product.variation_id : null,
            product_id: product.product_id,
            subtotal: String(product.price * quantityPrice),
            total: String(product.price * quantityPrice)
          }
        ]
      }
    }) as any
    const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
    cartItems.value = items?.value

    pendingCart.value = items.value.single
    loading.value[index] = false
  } else {
    loading.value[index] = true
    const response = await $fetch(`/api/orders/update/${pendingCart.value[0].id}`, {
      method: 'put',
      body: {
        customer_id: Number(userData?.value?.id),
        line_items: [
          {
            id: product.line_item_id,
            quantity: 0,
            variation_id: product.variation_id ? product.variation_id : null,
            product_id: product.product_id,
          }
        ]
      }
    }) as any

    const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)

    if (items?.value?.single?.items?.length === 0) {
        // Delete Old Order
      const response = await $fetch(`/api/orders/delete/${pendingCart.value[0].id}`, {
        method: 'delete',
      }) as any
      const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)

      pendingCart.value = items.value.single
      checkboxStates.value = pendingCart.value.length ? pendingCart.value[0]?.items.map(() => true) : []  // Initialize as an empty array if pendingCart is empty

      cartItems.value = items?.value
    } else {
      const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)

      pendingCart.value = items.value.single
      checkboxStates.value = pendingCart.value.length ? pendingCart.value[0]?.items.map(() => true) : []  // Initialize as an empty array if pendingCart is empty

      cartItems.value = items?.value

    }
    loading.value[index] = false

  }
}


// Watcher for checkboxStates to update "select all" checkbox and selected products
watch(checkboxStates, (newValues) => {
  if (Array.isArray(newValues)) {  // Ensure newValues is an array
    checkedAll.value = newValues.every(Boolean)
    selectedProducts.value = pendingCart.value?.length
      ? pendingCart.value[0].items.filter((_: any, index: any) => checkboxStates.value[index])
      : []

    // Calculate the total price for the selected products
    totalPrices.value = selectedProducts?.value?.reduce((acc: number, item: any) => {
      return acc + (Number(item.price) * item.quantity)
    }, 0)
  }
}, { deep: true })


const buyOrder = async () => {
  const cartUrl = '/checkout'

  // Get all unselected items
  const unselectedItems = pendingCart.value[0].items.filter((item: any, index: number) => !checkboxStates.value[index])

  // If there are unselected items, update their quantities to 0
  if (unselectedItems.length > 0) {
    const lineItems = unselectedItems.map((item: any) => ({
      id: item.line_item_id,
      quantity: 0,
      variation_id: item.variation_id ? item.variation_id : null,
      product_id: item.product_id,
    }))

    await $fetch(`/api/orders/update/${pendingCart.value[0].id}`, {
      method: 'put',
      body: {
        customer_id: Number(userData?.value?.id),
        line_items: lineItems
      }
    })

    // Refresh cart data
    const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
    cartItems.value = items?.value
  }

  localStorage.setItem("completeOrder", JSON.stringify(selectedProducts.value))
  const completeOrder = localStorage.getItem("completeOrder")

  const order = completeOrder ? JSON.parse(completeOrder) : null
  if (order.length !== 0) {
    router.push(localePath(cartUrl))
  }
}

const tokenCookie = useCookie('token', persistentCookieOptions)

const mobileBuyOrder = async () => {
  let cartUrl = '';
  if (tokenCookie.value) {
    cartUrl = '/cart/mobile/address'  // Generate the localized path to the cart
  }  else {
    cartUrl = '/cart/mobile/userinfo'  // Generate the localized path to the cart
  }

  // Get all unselected items
  const unselectedItems = pendingCart.value[0].items.filter((item: any, index: number) => !checkboxStates.value[index])

  // If there are unselected items, update their quantities to 0
  if (unselectedItems.length > 0) {
    const lineItems = unselectedItems.map((item: any) => ({
      id: item.line_item_id,
      quantity: 0,
      variation_id: item.variation_id ? item.variation_id : null,
      product_id: item.product_id,
    }))

    await $fetch(`/api/orders/update/${pendingCart.value[0].id}`, {
      method: 'put',
      body: {
        customer_id: Number(userData?.value?.id),
        line_items: lineItems
      }
    })

    // Refresh cart data
    const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
    cartItems.value = items?.value
  }

  localStorage.setItem("completeOrder", JSON.stringify(selectedProducts.value))
  const completeOrder = localStorage.getItem("completeOrder")

  const order = completeOrder ? JSON.parse(completeOrder) : null
  if (order.length !== 0) {
    router.push(localePath(cartUrl))
  }
}


watch(cartItems, async (current) => {
  const { items } = await fetchHock(`orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)

  const oldLength = pendingCart.value?.[0]?.items?.length || 0
  const newItems = items.value.single?.[0]?.items || []
  const newLength = newItems.length

  pendingCart.value = items.value.single
  subTotal.value = items?.value?.single?.[0]?.total
  
  // Preserve existing checkbox states and add new ones as checked
  if (newLength > oldLength) {
    const existingStates = checkboxStates.value || []
    checkboxStates.value = newItems.map((_: any, index: any) => {
      return index < oldLength ? existingStates[index] : true
    })
  }
})
</script>

<style>
</style>
