<template>
  <div>
    <NuxtLoadingIndicator class="z-[99999999999999999999]"></NuxtLoadingIndicator>
    <div class="font-primaryFont rtl:font-notoSans text-primary overscroll-none">
      <Header @getGuestData="getGuestData" />
      <NuxtPage />
      <Footer />
      <ScrollToTop />
    </div>
  </div>
</template>
<script setup lang="ts">
// Cookies Data with persistent storage (30 days expiration)
import { persistentCookieOptions } from '~/utils/cookieOptions'
import type { PagesContent } from '~/types/pagesContent'
import type { Footer } from '~/types/footer'
import type { NavigationLinks } from '~/types/links'
import type { MenuStructure } from '~/types/navLinks'
import type { UserData } from '~/types/user'
import type { CartData } from '~/types/defaultCart'

// Set HTML dir attribute based on locale with safe i18n usage
const { locale } = useSafeI18n()
const direction = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const phoneNumber: any = useState("phoneNumber", () => '')
const selectedPaymentMethod: any = useState("selectedPaymentMethod", () => '')

// Client-side only code for document manipulation
onMounted(() => {
  // Set initial direction
  if (direction && direction.value) {
    document.documentElement.setAttribute('dir', direction.value)
    
    // Watch for locale changes and update HTML dir attribute
    watch(direction, (newDir) => {
      document.documentElement.setAttribute('dir', newDir)
    })
  }
})


const visitorTokenCookie = useCookie('visitor_token', persistentCookieOptions)
const visitorIdCookie = useCookie('visitor_id', persistentCookieOptions)
const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

// Main Variables
const userData = useState<UserData>("userData", () => ({}))
const socialData = useState<number | null>("socialData", () => null)
const cartItems = useState<CartData>("cartItems", () => ({}))

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// VISITOR LOGIC
// Create a unique ID for the visitor
const uniqueid = (): string => {
  // always start with a letter (for DOM friendlyness)
  let idstr = String.fromCharCode(Math.floor((Math.random() * 25) + 65));
  do {
    // between numbers and characters (48 is 0 and 90 is Z (42-48 = 90)
    const ascicode = Math.floor((Math.random() * 42) + 48);
    if (ascicode < 58 || ascicode > 64) {
      // exclude all chars between : (58) and @ (64)
      idstr += String.fromCharCode(ascicode)
    }
  } while (idstr.length < 32)

  return idstr
}

if (!visitorTokenCookie.value)  {
  visitorTokenCookie.value = uniqueid()
  const response = await $fetch<{ id: string }>(`/api/newBranch/guest/${visitorTokenCookie.value}`, {
    method: 'post'
  })
  visitorIdCookie.value = response.id
}

// Get the Data for guest user and orders
if (visitorIdCookie.value && visitorTokenCookie.value && !tokenCookie.value && !userIdCookie.value) {
  const { items: pendingOrder } = await fetchHock(`orders/userOrders/?user_id=${Number(visitorIdCookie.value)}&status=pending`)
    
  if(pendingOrder?.value?.single?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.single[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any
    }
  } else if(pendingOrder?.value?.grouped?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.grouped[0]?.orders[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.grouped[0]?.group}`, {
        method: 'delete',
      }) as any
    }
  }

  const { items } = await fetchHock(`newBranch/orders/${Number(visitorIdCookie.value)}`)
  const { items: usersData } = await fetchHock(`newBranch/customer/${Number(visitorIdCookie.value)}`)
  userData.value = usersData.value
  cartItems.value = items.value
}

// Get the Data for Guest user and orders
const getGuestData = async () => {
  if (visitorIdCookie.value) {
    visitorTokenCookie.value = uniqueid()

    const { items: pendingOrder } = await fetchHock(`orders/userOrders/?user_id=${Number(visitorIdCookie.value)}&status=pending`)

    if(pendingOrder?.value?.single?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.single[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any
    }
  } else if(pendingOrder?.value?.grouped?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.grouped[0]?.orders[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.grouped[0]?.group}`, {
        method: 'delete',
      }) as any
    }
  }

    const { items } = await fetchHock(`newBranch/orders/${Number(visitorIdCookie.value)}`)
    const { items: usersData } = await fetchHock(`newBranch/customer/${Number(visitorIdCookie.value)}`)
    userData.value = usersData.value
    cartItems.value = items.value
  }
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// LOGGED IN LOGIC
// Get the Data for logged in user and orders
if (tokenCookie.value && userIdCookie.value) {
  const { items: pendingOrder } = await fetchHock(`orders/userOrders/?user_id=${Number(userIdCookie.value)}&status=pending`)
    
  if(pendingOrder?.value?.single?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.single[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any
    }
  } else if(pendingOrder?.value?.grouped?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.grouped[0]?.orders[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.grouped[0]?.group}`, {
        method: 'delete',
      }) as any
    }
  }
  
  const { items } = await fetchHock(`newBranch/orders/${Number(userIdCookie.value)}`)
  const { items: usersData } = await fetchHock(`newBranch/customer/${Number(userIdCookie.value)}`)
  userData.value = usersData.value
  cartItems.value = items.value
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Static content and links
const navLinks = useState<MenuStructure>("navLinks", () => ({} as MenuStructure))
const { items: sideLinks } = await fetchHock("newBranch/menus")
navLinks.value = sideLinks.value

const links = useState<NavigationLinks>("links", () => [])
const { items: linksData } = await fetchHock("newBranch/links")
links.value = linksData.value


const pagesContent = useState<PagesContent>("pagesContent", () => ({} as PagesContent))
const { items: pageContent } = await fetchHock(`newBranch/content`)
pagesContent.value = pageContent.value

const footer = useState<Footer>("footer", () => ({} as Footer))
const { items: footerData } = await fetchHock("newBranch/footer")
footer.value = footerData.value
</script>