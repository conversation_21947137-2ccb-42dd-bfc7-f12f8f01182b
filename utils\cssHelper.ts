/**
 * Safely access CSS rules from a stylesheet
 * This function handles CORS issues and ensures stylesheets are loaded
 */
export const safelyAccessCSSRules = (callback: () => void) => {
  // Wait for DOM to be fully loaded
  if (document.readyState === 'complete') {
    try {
      callback();
    } catch (error) {
      console.warn('Error accessing CSS rules:', error);
    }
  } else {
    // If DOM is not loaded yet, add event listener
    window.addEventListener('load', () => {
      try {
        callback();
      } catch (error) {
        console.warn('Error accessing CSS rules:', error);
      }
    });
  }
};

/**
 * Safely get computed style for an element
 * This avoids direct access to cssRules which can cause CORS issues
 */
export const getComputedStyleSafely = (element: HTMLElement, property: string): string => {
  try {
    return window.getComputedStyle(element).getPropertyValue(property);
  } catch (error) {
    console.warn('Error getting computed style:', error);
    return '';
  }
};
