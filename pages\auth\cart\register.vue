<template>
  <div class="bg-thirdColor pb-[60px]">
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="pt-10 relative sm:max-w-[60%] mx-auto pb-8">
        <div class="md:mt-5">
          <div class="font-secondaryFont font-bold text-primary text-center mx-auto sm:text-lg sm:max-w-[80%] hidden md:block rtl:font-notoSans">{{ $t('Welcome to Spare Finders Please complete the form below to create a new account and enjoy a variety of features.') }}</div>
            <div class="text-primary font-bold font-secondaryFont text-[15px] md:hidden text-center rtl:font-notoSans">{{ $t('Create A New Account!') }}</div>
          <div class="mt-5">
            <div v-if="serverStatus" class="mb-2 text-red-700 font-bold text-lg rtl:font-notoSans">{{ serverErrors === $t('Sorry, that username already exists!') ? $t('Sorry, that Phone Number already exists!') : serverErrors }}</div>
            <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
              <div class="w-full mx-auto flex flex-col gap-6">
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="first_name">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        px-5
                        !bg-thirdColor
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      variant="outline"
                      v-model="state.first_name"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.first_name,
                        }"
                      >
                        {{ $t('First Name') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="last_name">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        px-5
                        !bg-thirdColor
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      variant="outline"
                      v-model="state.last_name"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.last_name,
                        }"
                      >
                        {{ $t('last Name') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="username">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        px-5
                        !bg-thirdColor
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      variant="outline"
                      v-model="state.username"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.username,
                        }"
                      >
                        {{ $t('Phnoe Number') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="email">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        !bg-thirdColor
                        px-5
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      variant="outline"
                      v-model="state.email"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.email,
                        }"
                      >
                        {{ $t('email') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="password">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        !bg-thirdColor
                        px-5
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      type="password"
                      variant="outline"
                      v-model="state.password"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.password,
                        }"
                      >
                        {{ $t('password') }}
                    </span>
                  </UFormGroup>
                </div>
                <div class="text-field-bg-13 relative login-register">
                  <UFormGroup name="password_confirm">
                    <input
                      class="
                        w-full
                        border
                        border-primary
                        rounded-full
                        py-2
                        px-5
                        !bg-thirdColor
                        md:py-3
                        md:px-5
                        md:rounded-lg
                        focus:outline-0
                      "
                      type="password"
                      variant="outline"
                      v-model="state.password_confirm"
                      required
                      placeholder=" "
                    >
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-primaryFont
                        font-semibold
                        !bg-thirdColor
                        opacity-1
                        md:text-base
                        text-[13px]
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        rtl:font-notoSans
                        md:top-[13px]
                        top-[11px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-bg-13' : state.password_confirm,
                        }"
                      >
                        {{ $t('confirm password') }}
                    </span>
                  </UFormGroup>
                </div>
              </div>
  
              <UFormGroup class="md:block hidden custom-checkbox">
                <UCheckbox :model-value="state.updates" class="mt-4 bg-thirdColor">
                  <template #label>
                    <span class="font-primaryFont text-primary text-lg items-center underline rtl:font-notoSans">{{ $t('Get the latest updates.') }}</span>
                  </template>
                </UCheckbox>
              </UFormGroup>
              
  
  
              <UFormGroup class="md:block hidden custom-checkbox">
                <UCheckbox :model-value="state.terms" class="mt-4 bg-thirdColor">
                  <template #label>
                    <span class="font-primaryFont text-primary text-lg items-center underline rtl:font-notoSans">{{ $t('I agree to all terms and conditions.') }}</span>
                  </template>
                </UCheckbox>
              </UFormGroup>
  
  
  
  
  
  
              <div class="text-center mt-6 flex gap-5 justify-center items-center flex-col sm:flex-row">
                <UButton
                  type="submit"
                  class="
                    text-thirdColor
                    font-bold
                    font-thirdFont
                    uppercase
                    rounded-full
                    md:text-lg
                    border
                    border-primary
                    bg-primary
                    shadow-unset
                    lg:px-[140px]
                    md:px-[100px]
                    px-[90px]
                    rtl:font-notoSans
                    lg:py-3
                    md:py-1
                    hover:scale-[1.1]
                    ease-in-out
                    duration-500
                  "
                  variant="solid"
                  :loading="isLoading"
                >
                  <div v-if="!isLoading">{{ $t('REGISTER') }}</div>
                </UButton>
                <UButton
                  class="
                    text-thirdColor
                    font-bold
                    font-thirdFont
                    rounded-full
                    md:text-lg
                    uppercase
                    border
                    border-orangeColor
                    bg-orangeColor
                    hover:border-primary
                    shadow-unset
                    lg:px-[30px]
                    md:px-[30px]
                    px-5
                    lg:py-3
                    md:py-1
                    hover:scale-[1.1]
                    ease-in-out
                    duration-500
                  "
                  variant="solid"
                  :to="localePath('/auth/cart/login')"
                >
                  {{ $t('Existing User? Log In') }}
                </UButton>
              </div>
            </UForm>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const router = useRouter()
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => {})

const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

const isLoading = ref<boolean>(false)
const codeStatus: any = useState("codeStatus", () => false)
const phoneNumber: any = useState("phoneNumber", () => '')
const formStatus: any = useState("formStatus", () => null)

const { locale, t } = useI18n()
const localePath = useLocalePath()

const showVerificationModal: any = useState("showVerificationModal", () => false)

const schema = z.object({
  email: z.string().email(t('Invalid email')),
  username: z.string()
    .min(11, t('Phone must be at least 11 characters')),
    password: z.string()
    .min(6, t('Password must be at least 6 characters')),
    password_confirm: z.string()
    .min(6, t('Password confirm must be at least 6 characters')),
    first_name: z.string()
    .min(3, t('First name must be at least 3 characters')),
    last_name: z.string()
    .min(3, t('Last name must be at least 3 characters')),
    // updates: z.boolean().refine((value: any) => value === true, {
    //   message: t('Updates is required'),
    // }),
    // terms: z.boolean().refine((value: any) => value === true, {
    //   message: t('Terms is required'),
    // }), 
})

type Schema = z.output<typeof schema>

const state = reactive({
  username: undefined,
  email: undefined,
  password: undefined,
  password_confirm: undefined,
  first_name: undefined,
  last_name: undefined,
  updates: false,
  terms: false,
  roles: 'customer'
})


Object.assign(state, {
  username: undefined,
  email: undefined,
  password: undefined,
  password_confirm: undefined,
  first_name: undefined,
  last_name: undefined,
  updates: false,
  terms: false,
  roles: 'customer'
})

const serverErrors = ref('')
const serverStatus = ref(false)

async function onSubmit (event: FormSubmitEvent<Schema>) {
  isLoading.value = true
  serverStatus.value = false
  
  try {
    const response: any = await $fetch('/api/register', {
      method: 'post',
      body: state
    })

    if(response.statusCode === 500) {
      serverStatus.value = true
      serverErrors.value = response.error.message
      window.scrollTo({ top: 0, behavior: 'smooth' })
      isLoading.value = false
      return
    }

    // Handle cart functionality after successful registration
    const orders = ref()
    const completeOrder = localStorage.getItem("completeOrder")
    orders.value = completeOrder ? JSON.parse(completeOrder) : null

    const { items } = await fetchHock(`orders/${Number(response.user.data.ID)}?status=pending-cart`)
    if (items?.value?.single[0]?.id) {
      await $fetch(`/api/orders/delete/${items?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any

      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    } else {
      const transformedItems = orders.value.map((item: any) => ({
        product_id: item.product_id,
        quantity: item.quantity,
        variation_id: item.variation_id
      }))

      if (orders.value.length !== 0) {
        const createDefaultOrder = ref({
          customer_id: response.user.data.ID,
          status: 'pending-cart',
          line_items: transformedItems
        })

        const responsee = await $fetch('/api/orders/create', {
          method: 'post',
          body: createDefaultOrder.value
        }) as any

        cartItems.value = responsee
      }
    }

    // Load user data
    const { items: usersData } = await fetchHock(`shipping/${Number(response.user.data.ID)}`)
    userData.value = usersData.value

    // Set cookies
    tokenCookie.value = response.user.data.user_pass
    userIdCookie.value = response.user.data.ID

    // Redirect to checkout
    router.push(localePath('/checkout'))
    
  } catch (err) {
    // Handle error
    serverStatus.value = true
    serverErrors.value = t('Registration failed. Please try again.')
    window.scrollTo({ top: 0, behavior: 'smooth' })
    isLoading.value = false
  }
}
</script>