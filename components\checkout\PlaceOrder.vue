<template>
  <div>
    <div class="border-2 border-primary rounded-t-[20px] py-6 ps-6 pe-2 pt-10 relative">
      <UBadge
        class="
        bg-secondary
        font-secondaryFont
        font-bold
        text-thirdColor
        text-lg
        rounded-full
        px-4
        py-1
        absolute
        top-[-20px]
        min-w-[263px]"
      >
        <div>{{ $t('4. PLACE YOUR ORDER') }}</div>
      </UBadge>
      <div class="place-order-container pe-4">
        <div class="border-b border-primary pb-5 mt-5 border-none-custom" v-for="item in orders ?? []" :key="item.id">
          <div class="flex justify-between">
            <div class="flex items-center gap-5 pe-3">
              <div @click="navigateToProduct(item)" class="cursor-pointer">
                <template v-if="Array.isArray(item?.meta_data)">
                  <div class="relative w-[110px] h-[95px] flex items-center">
                    <img class="w-[95px] h-[95px]" :src="item?.image"/>
                    <img class="absolute bottom-0 ltr:left-[0%] rtl:right-[0%] w-[60px] h-[60px] rounded-full border border-primary" :src="item?.brand_logo" :alt="locale === 'en' ? item?.product_name : item?.product_name_ar" />
                  </div>
                </template>
                <template v-else>
                  <div class="relative w-[110px] h-[95px] flex items-center">
                    <img class="w-[95px] h-[95px]" :src="item?.image"/>
                    <img class="absolute bottom-0 ltr:left-[0%] rtl:right-[0%] w-[60px] h-[60px] rounded-full border border-primary" :src="item?.brand_logo" :alt="locale === 'en' ? item?.product_name : item?.product_name_ar" />
                  </div>
                </template>
              </div>
              <div @click="navigateToProduct(item)" class="cursor-pointer">
                <div class="text-[16px] italic font-semibold text-primary">{{ locale === 'en' ? item?.product_name : item?.product_name_ar }}</div>
                <div class="text-[14px] italic font-semibold text-orangeColor">
                  {{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}
                </div>
                <div class="font-bold text-xl lg:text-[20px] text-primary">
                  <div class="flex items-center gap-2 rtl:flex-row-reverse">
                    <span>{{ $t('EGP') }}</span>
                    <span class="rtl:font-secondaryFont">{{ formatPrice(Number(item?.single_price)) }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="
                text-[20px]
                font-thirdFont
                text-orangeColor
                flex
                flex-col
                justify-center
                text-center
                border-s
                border-primary
                min-w-[130px]"
            >
              <div class="font-extrabold text-center">x{{ item.quantity }}</div>
              <div class="font-bold text-center flex items-center gap-2 rtl:flex-row-reverse justify-center">
                <span class="rtl:text-base">{{ $t('EGP') }}</span>
                <span class="rtl:font-secondaryFont rtl:text-base">{{ formatPrice(Number(item.total)) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-thirdColor pt-6 px-6 border-2 border-t-0 border-primary">
        <div class="mb-5 font-bold text-primary text-lg">{{ $t('Do you have a coupon code?') }}</div>
        <div class="text-red-700 font-bold text-lg mb-2" v-if="couponInvalid">{{ couponInvalid }}</div>
        <div class="relative pb-8">
          <input
            v-model="couponCode"
            class="
              text-lg
              w-full
              font-secondaryFont
              py-2
              px-4
              border-2
              border-primary
              rounded-lg
              focus:outline-none
            "
            :placeholder="$t('ENTER PROMO CODE')"
          />
          <UButton
            class="
              text-thirdColor
              font-bold
              font-thirdFont
              text-lg
              rounded-s-none
              rounded-e-lg
              border
              border-primary
              bg-primary
              uppercase
              shadow-unset
              lg:px-10
              md:px-3
              lg:py-2
              md:py-1
              ease-in-out
              duration-500
              absolute
              ltr:right-0
              rtl:left-0
              top-0
            "
            @click="couponCodeCheck"
            variant="solid"
          >
            {{ $t('REDEEM') }}
          </UButton>
        </div>
        <div v-if="cartFulltemNew?.coupon_lines?.length" class="flex gap-5 flex-wrap mb-10">
          <div
            v-for="disc in cartFulltemNew?.coupon_lines ?? []"
            :key="disc.rate_id"
            class="border border-primary rounded-[6px] flex relative"
            >
            <div class="bg-orangeColor rounded-s-[6px] text-thirdColor font-semibold py-1 px-4">{{ disc?.code }}</div>
            <div class="text-orangeColor font-semibold py-1 px-4">-{{ $t('EGP') }} {{ disc?.discount }}</div>
            <div
              @click="removeCoupon(disc.code)"
              class="absolute z-[22] right-[-10px] top-[-10px] border border-primary rounded-full p-1 bg-thirdColor cursor-pointer"
            >
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.51636 4L7.79068 1.72568C8.06977 1.44659 8.06977 0.994091 7.79068 0.714773L7.28523 0.209318C7.00614 -0.0697727 6.55364 -0.0697727 6.27432 0.209318L4 2.48364L1.72568 0.209318C1.44659 -0.0697727 0.994091 -0.0697727 0.714773 0.209318L0.209318 0.714773C-0.0697727 0.993864 -0.0697727 1.44636 0.209318 1.72568L2.48364 4L0.209318 6.27432C-0.0697727 6.55341 -0.0697727 7.00591 0.209318 7.28523L0.714773 7.79068C0.993864 8.06977 1.44659 8.06977 1.72568 7.79068L4 5.51636L6.27432 7.79068C6.55341 8.06977 7.00614 8.06977 7.28523 7.79068L7.79068 7.28523C8.06977 7.00614 8.06977 6.55364 7.79068 6.27432L5.51636 4Z" fill="#1D3C34"/>
              </svg>
            </div>
          </div>
        </div>
    </div>
    <div class="bg-primary p-6 rounded-b-[20px]">
      <div class="flex justify-between items-center text-thirdColor text-xl mt-5">
        <span>{{ $t('Sub Total') }}</span>
        <span class="flex gap-1 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span>
          <span>{{ formatPrice(Number(cartFulltemNew?.subtotal)) }}</span>
        </span>
      </div>
      <div class="flex justify-between items-center text-thirdColor text-xl mt-2" v-if="cartFulltemNew?.total_discount">
        <span>Discount</span>
        <span class="flex gap-1 rtl:flex-row-reverse">
          <span>-{{ $t('EGP') }}</span>
          <span>{{ formatPrice(Number(cartFulltemNew?.total_discount)) }}</span>
        </span>
      </div>
      <div v-for="(item, index) in cartFulltemNew?.tax_lines ?? []" :key="index" class="flex justify-between items-center text-thirdColor text-xl mt-2">
        <span>{{ locale === 'en' ? item?.label : item?.label_ar }} <span>({{ item?.tax_rate }})</span></span>
        <span class="flex gap-1 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span>
          <span>{{ formatPrice(Number(item?.tax_total)) }}</span>
        </span>
      </div>
      <div class="flex justify-between items-center text-thirdColor text-2xl font-bold mt-5">
        <span>{{ $t('TOTAL') }}</span>
        <span class="flex gap-1 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span>
          <span>{{ formatPrice(Number(cartFulltemNew?.total)) }}</span>
        </span>
      </div>
      <UButton
        class="
          border-orangeColor
          bg-orangeColor
          font-thirdFont
          text-thirdColor
          font-bold
          text-xl
          py-4
          w-full
          items-center
          flex
          uppercase
          justify-center
          rounded-full
          mt-5
          ease-in-out
          duration-500
          hover:bg-secondary
          hover:border-secondary
          disabled:bg-secondary
          disabled:border-secondary
        "
        v-if="user?.shipping?.address_1 && tokenCookie && selectedPaymentMethod"
        variant="solid"
        :loading="loading"
        @click="placeOrder"
      >
        <div v-if="!loading">{{ $t('PLACE ORDER') }}</div>
      </UButton>
      <UButton
        class="
          border-orangeColor
          bg-orangeColor
          font-thirdFont
          text-thirdColor
          font-bold
          text-xl
          py-4
          w-full
          uppercase
          items-center
          flex
          justify-center
          rounded-full
          mt-5
          ease-in-out
          duration-500
          hover:bg-secondary
          hover:border-secondary
          disabled:bg-secondary
          disabled:border-secondary
        "
        v-else-if="submitedButton"
        variant="solid"
        :loading="loading"
        @click="placeOrder"
      >
        <div v-if="!loading">{{ $t('PLACE ORDER') }}</div>
      </UButton>
      <UButton
        class="
          border-orangeColor
          bg-orangeColor
          text-thirdColor
          font-thirdFont
          font-bold
          text-xl
          py-4
          w-full
          uppercase
          items-center
          flex
          justify-center
          rounded-full
          mt-5
          ease-in-out
          duration-500
          disabled:bg-secondary
        "
        disabled
        v-else
        variant="solid"
      >
        {{ $t('PLACE ORDER') }}
      </UButton>
    </div>
    <ModalsCheckoutVerify />
  </div>
</template>
<script setup lang="ts">
import { ElNotification } from 'element-plus'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const router = useRouter()
const orders: any = useState("orders", () => [])
const userData: any = useState("userData", () => [])
const submitedButton = useState("submitedButton", () => false)
const FormPhoneNumber = useState("FormPhoneNumber", () => "")
const cartItems = useState("cartItems", () => ({}))
const tokenCookie = useCookie('token', persistentCookieOptions)

const cartFulltemNew = ref()
const cartItemsNew = ref()
const user = ref()
const loading = ref(false)

const { t } = useI18n()
const localePath = useLocalePath()

// Function to navigate to product detail page
const navigateToProduct = (item: any) => {
  // Determine product type (simple or variable)
  const productType = item.variation_id ? 'variable' : 'simple'
  // Navigate to product page
  router.push(localePath(`/product/${productType}/${item.variation_id ?? item.product_id}`))
}

loading.value = false

const { items: newItem } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
orders.value = newItem?.value?.single[0]?.items
cartItems.value = newItem?.value
cartItemsNew.value = newItem?.value?.single[0]?.items
cartFulltemNew.value = newItem?.value?.single[0]

const successPopUp = () => {
  ElNotification({
    // title: 'Create order',
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('Order has been created successfully')),
    // type: 'success',
    position: 'top-right',
  })
}


if (tokenCookie.value) {
  const { items } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
  user.value = items.value
  FormPhoneNumber.value = items.value.username
}

watch(userData, async (current) => {
  if (tokenCookie.value) {
    const { items } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
    user.value = items.value
  }
})

const showVerificationModal: any = useState("showVerificationModal", () => false)

const selectedAddress: any = useState("selectedAddress", () => 'default')
const additionalAddress: any = useState("additionalAddress", () => [])
const selectedPaymentMethod: any = useState("selectedPaymentMethod", () => '')
const { locale } = useI18n()
const oldOrderId = ref()
const realOrderId = ref()

const onSuccess = async (result: any) => {
  try {
    // First, make the API request and wait for it to complete
    const { items: pendingOrder } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending`)
    
    // Ensure the data is fully loaded by waiting a bit
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // Delete Old Order
    await $fetch(`/api/orders/delete/${oldOrderId.value}`, {
      method: 'delete',
    }) as any

    const data = ref({
      order_id: null,
      transaction_id: result.orderId
    })
    
    // Now check the conditions with the properly awaited data
    if (pendingOrder?.value?.single?.length) {
      data.value.order_id = pendingOrder.value.single[0]?.id
    } 
    if (pendingOrder?.value?.grouped?.length) {
      data.value.order_id = pendingOrder.value.grouped[0]?.group
    }

    const getOrderId = await $fetch('/api/newBranch/payment/save', {
      method: 'post',
      body: data.value
    }) as any

    // Don't update the UI with new cart data until after redirect
    const { items: newItem } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
    // We'll update these values after redirect
    cartItemsNew.value = newItem?.value?.single[0]?.items
    cartFulltemNew.value = newItem?.value?.single[0]
    cartItems.value = newItem?.value

    submitedButton.value = false
    successPopUp()

    localStorage.removeItem("completeOrder")

    // Prepare the redirect URL first
    let cartUrl = ''
    if(realOrderId.value.type === 'single') {
      cartUrl = `/thanks/${realOrderId.value.created_orders[0]}`
    } else {
      cartUrl = `/thanks/${realOrderId.value.grouped_order_id}`
    }

    loading.value = false
    // Use nextTick to ensure the redirect happens before resetting cart data
    // This prevents the brief display of empty values
    router.push(localePath(cartUrl))
  } catch (error) {
    console.error('Error in onSuccess function:', error)
    loading.value = false
    // Handle error appropriately
    ElNotification({
      message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('An error occurred while processing your payment')),
      type: 'error',
      position: 'top-right',
    })
  }
}

const onError = async (error: any) => {
  if(realOrderId.value.type === 'single') {
    await $fetch(`/api/orders/delete/${realOrderId.value.created_orders[0]}`, {
      method: 'delete',
    }) as any
    loading.value = false
  } else {
    await $fetch(`/api/orders/delete/${realOrderId.value.grouped_order_id}`, {
      method: 'delete',
    }) as any
    loading.value = false
  }
}

const onCancel = async () => {
  if(realOrderId.value.type === 'single') {
    await $fetch(`/api/orders/delete/${realOrderId.value.created_orders[0]}`, {
      method: 'delete',
    }) as any
    loading.value = false
  } else {
    await $fetch(`/api/orders/delete/${realOrderId.value.grouped_order_id}`, {
      method: 'delete',
    }) as any
    loading.value = false
  }
}

const paymentModal = async (sessionId: string) => {
  // initialize the payment object
  const payment = new (window as any).GeideaCheckout(onSuccess, onError, onCancel);

  // start the payment
  payment.startPayment(sessionId);
}

const placeOrder = async () => {
  loading.value = true

  const { items: pendingOrder } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending`)

  if(pendingOrder?.value?.single?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.single[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.single[0]?.id}`, {
        method: 'delete',
      }) as any
    }
  } else if(pendingOrder?.value?.grouped?.length) {
    // Delete Old Order
    if (!pendingOrder?.value?.grouped[0]?.orders[0]?.geidea_transaction_id) {
      await $fetch(`/api/orders/delete/${pendingOrder?.value?.grouped[0]?.group}`, {
        method: 'delete',
      }) as any
    }
  }

  const { items: verify } = await fetchHock(`profile/verify/${Number(userData?.value?.id)}`)

  if (!verify.value.verified) {
    ElNotification({
      message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, t('Please verify your account first')),
      type: 'error',
      position: 'top-right',
    })
    loading.value = false
    showVerificationModal.value = true
    return
  }

  const { items } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
  const { items: allCartItems } = await fetchHock(`orders/${Number(userData?.value?.id)}`)

  // Get the correct shipping address based on selection
  let shippingAddress
  if (selectedAddress.value === 'default') {
    shippingAddress = items.value.shipping
  } else if (typeof selectedAddress.value === 'number') {
    const selectedAddressData = additionalAddress.value[selectedAddress.value]
    shippingAddress = {
      first_name: selectedAddressData.ship_fname,
      last_name: selectedAddressData.ship_lname,
      address_1: selectedAddressData.ship_address_1,
      address_2: selectedAddressData.ship_address_2,
      phone: selectedAddressData.ship_address_phone,
      city: selectedAddressData.ship_address_city,
      email: selectedAddressData.ship_address_email || items.value.billing.email,
    }
  }

  const transformedItemsForOrder = orders.value.map((item: any) => ({
    id: item.id,
    line_item_id: item.line_item_id,
    extra_variation_data: item.extra_variation_data,
    product_id: item.product_id,
    variation_id: item.variation_id,
    product_name: item.product_name,
    quantity: item.quantity,
    subtotal: item.subtotal,
    subtotal_tax: item.subtotal_tax,
    discount: item.discount,
    total: item.total,
    total_tax: item.total_tax,
    taxes: item.taxes,
    price: item.price,
    display_price: item.display_price,
    single_price: item.single_price,
    total_incl_tax: item.total_incl_tax,
    image: item.image,
    meta_data: item.meta_data,
    brand_logo: item.brand_logo,
    excerpt: item.excerpt,
    reviews: item.reviews,
    name: item.name,
    parent_name: item.parent_name,
    sku: item.sku,
    tax_class: item.tax_class,
    variation_excerpt: item.variation_excerpt,
  }))


  const data = ref<any>({
    customer_id: items.value.id,
    status: 'confirmation',
    line_items: transformedItemsForOrder,
    shipping: shippingAddress,
    billing: shippingAddress,
    payment_method: "cod",
    payment_method_title: "Cash On Delivery",
    coupon_lines: [],
    total: cartFulltemNew?.value?.total,
    subtotal: cartFulltemNew?.value?.subtotal,
    tax_lines: cartFulltemNew?.value?.tax_lines,
    total_tax: cartFulltemNew?.value?.total_tax,
    total_discount: cartFulltemNew?.value?.total_discount,
  })

  const getCoupons = allCartItems?.value?.single[0].coupon_lines?.map((coupon: any) => ({
    code: coupon.code
  })) || []

  allCartItems?.value?.single[0].coupon_lines?.map((coupon: any) => {
    removeCoupon(coupon.code)
  })

  oldOrderId.value = allCartItems?.value?.single[0].id

  data.value.coupon_lines = getCoupons

  if (selectedPaymentMethod.value === 'visa') {
    data.value.payment_method = 'geidea_card'
    data.value.payment_method_title = 'Bank Card'
    data.value.status = 'pending'

    // Create the order
    const getOrderId = await $fetch('/api/orders/createRealOrder', {
      method: 'post',
      body: data.value
    }) as any
    
    realOrderId.value = getOrderId

    if(getOrderId.type === 'single') {
      const { items: paymentSession } = await fetchHock(`newBranch/payment?language=${locale.value}&order_id=${getOrderId.created_orders[0]}`)
      paymentModal(paymentSession.value.session_id)
    } else {
      const { items: paymentSession } = await fetchHock(`newBranch/payment?language=${locale.value}&order_id=${getOrderId.grouped_order_id}`)
      paymentModal(paymentSession.value.session_id)
    }
  } else {
    const getOrderId = await $fetch('/api/orders/createRealOrder', {
      method: 'post',
      body: data.value
    }) as any

    // Delete Old Order
    await $fetch(`/api/orders/delete/${oldOrderId.value}`, {
      method: 'delete',
    }) as any

      // Don't update the UI with new cart data until after redirect
    const { items: newItem } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
    // We'll update these values after redirect
    cartItemsNew.value = newItem?.value?.single[0]?.items
    cartFulltemNew.value = newItem?.value?.single[0]
    cartItems.value = newItem?.value

    submitedButton.value = false
    successPopUp()

    localStorage.removeItem("completeOrder")

    // Prepare the redirect URL first
    let cartUrl = ''
    if(getOrderId.type === 'single') {
      cartUrl = `/thanks-cod/${getOrderId.created_orders[0]}`
      loading.value = false
    } else {
      cartUrl = `/thanks-cod/${getOrderId.grouped_order_id}`
      loading.value = false
    }

    // Use nextTick to ensure the redirect happens before resetting cart data
    // This prevents the brief display of empty values
    router.push(localePath(cartUrl))
  }
}

const couponCode = ref('')
const couponInvalid = ref('')

const couponCodeCheck = async () => {
  if (couponCode.value) {
    const data = ref({
      user_id: userData.value.id,
      coupon_code: couponCode.value,
      order_id: cartFulltemNew.value.id
    })
    const updateData = ref({
      customer_id: userData.value.id,
      order_id: cartFulltemNew.value.id,
      line_items: [],
      coupon_lines: [{
        code: couponCode.value
      }]
    })
    const res = await $fetch('/api/coupon/validation', {
      method: 'post',
      body: data.value
    }) as any
    if (res.statusCode === 400) {
      couponInvalid.value = res.error.error
    } else {
      couponInvalid.value = ''
      const response = await $fetch(`/api/orders/updateDiscount`, {
        method: 'put',
        body: updateData.value
      }) as any
      const { items: newItem } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
      orders.value = newItem?.value?.single[0]?.items
      cartItemsNew.value = newItem?.value?.single[0]?.items
      cartFulltemNew.value = newItem?.value?.single[0]
      cartItems.value = newItem?.value
      couponCode.value = ''
    }
  }
}

const removeCoupon = async (code: string) => {
  if (code) {
    const data = ref({
      user_id: userData.value.id,
      coupon_code: code,
      order_id: cartFulltemNew.value.id
    })

    const res = await $fetch('/api/coupon/remove', {
      method: 'put',
      body: data.value
    }) as any

    const { items: newItem } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&status=pending-cart`)
    orders.value = newItem?.value?.single[0]?.items
    cartItemsNew.value = newItem?.value?.single[0]?.items
    cartFulltemNew.value = newItem?.value?.single[0]
    cartItems.value = newItem?.value
  }
}
</script>
<style scoped>
.place-order-container {
  overflow-y: scroll;
  max-height: 480px;
}

/* width */
::-webkit-scrollbar {
  width: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #D9D9D9;
  border-radius: 10px;
}
</style>