<template>
  <div>
    <UModal v-model="reviewModalGroup" class="orderModal">
      <div class="rounded-[30px]">
        <div class="bg-thirdColor text-end px-5 pt-2 md:hidden cursor-pointer" @click="reviewModalGroup = false">
          <ClientOnly>
            <font-awesome-icon class="text-primary text-[20px]" icon="fa-solid fa-circle-xmark" />
          </ClientOnly>
        </div>
        <div class="bg-thirdColor p-5 flex justify-between items-center">
          <div class="text-secondary font-bold text-xl font-secondaryFont uppercase">
            Product Review
          </div>
        </div>

        <div class="p-5">
          <div class="border-none-custom mt-[20px] flex items-center justify-between h-fit w-full pb-5">
            <div class="flex items-center gap-3 md:gap-10">
              <div class="relative w-[30%] md:w-[125px] md:h-[110px] flex items-center">
                <img class="relative z-[20] w-[60px] h-[60px] rounded-full border border-primary hidden md:block" :src="reviewOrderGroup.brand_logo" />
                <img class="md:absolute md:top-0 md:left-[35px]" :src="reviewOrderGroup.image" />
              </div>
              <div class="w-[70%]">
                <div class="text-[14px] md:text-xl italic font-semibold text-primary">{{ reviewOrderGroup.product_name }}</div>
                <div class="text-[12px] md:text-lg italic font-semibold text-orangeColor">
                  <span>{{ reviewOrderGroup.excerpt }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Rating Section -->
          <div class="mt-4 flex justify-between items-center flex-col md:flex-row">
            <div class="font-secondaryFont md:text-lg text-orangeColor">RATE THIS PRODUCT</div>
            <el-rate v-model="productFeedback.rating" :colors="colors" size="large" @change="updateRating" />
          </div>

          <!-- Comment Selection Section -->
          <div class="mt-4">
            <div class="flex gap-2 mt-2 flex-wrap uppercase">
              <button
                v-for="comment in predefinedComments"
                :key="comment"
                :class="[ 
                  'py-4 px-2 rounded font-secondaryFont text-xs text-primary font-bold w-[48%] md:w-[32%]',
                  productFeedback.comment === comment
                    ? 'bg-orangeColor text-white'
                    : 'bg-filter text-primary'
                ]"
                @click="toggleComment(comment)"
              >
                {{ comment }}
              </button>
            </div>
          </div>
        </div>

        <div class="bg-thirdColor p-5 text-center pb-[60px] uppercase">
          <UButton
            class="disabled:border-primary uppercase font-thirdFont font-bold text-lg py-2 px-20 rounded-full box-shadow border border-orangeColor bg-orangeColor hover:bg-orangeColor hover:border-orangeColor ease-in-out duration-500"
            variant="solid"
            @click="submitFeedback"
            :loading="loading"
          >
            Submit
          </UButton>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Modal and Product Review State
const reviewModalGroup = useState("reviewModalGroup", () => false);
const userData: any = useState("userData", () => []);
const reviewOrderGroup: any = useState("reviewOrderGroup", () => ({}));
const { items } = await fetchHock(`newBranch/comments`)

// Object to hold rating and comment data for the product
const productFeedback = reactive({ rating: 0, comment: null });

// Colors for the rating component
const colors = ref(['#FF671F', '#FF671F', '#FF671F', '#FF671F', '#FF671F']);

// Dynamic comments based on star rating
const dynamicComments = ref();

dynamicComments.value = items.value
// Predefined comment labels (initially empty)
const predefinedComments = ref([]);

// Function to handle comment selection (single selection mode)
const toggleComment = (comment: any) => {
  productFeedback.comment = productFeedback.comment === comment ? null : comment;
};

// Function to handle rating change and update comments dynamically
const updateRating = (value: any) => {
  productFeedback.rating = value;
  predefinedComments.value = dynamicComments.value[value] || [];
};

const loading = ref(false);
const order: any = useState("order", () => {})
const paginationPages: any = useState("paginationPages", () => 0)
const ordersCount: any = useState("ordersCount", () => 0)
const perPage: any = useState("perPage", () => 10)
const page: any = useState("page", () => 1)

// Function to handle submission
const submitFeedback = async () => {
  loading.value = true;
  const feedbackData = {
    variation_id: reviewOrderGroup.value.variation_id ?? reviewOrderGroup.value.product_id,
    rate: productFeedback.rating,
    comment_content: productFeedback.comment,
  };

  const data = ref({
    user_id: Number(userData?.value?.id),
    reviews: [feedbackData],
    order_id: reviewOrderGroup.value.id,
  });

  const response = await $fetch(`/api/newBranch/reviews`, {
    method: 'post',
    body: data.value,
  }) as any;

  const { items: orders } = await fetchHock(`newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&per_page=${perPage.value}&page=${page.value}`)
  ordersCount.value = orders?.value?.total_orders_count
  paginationPages.value = orders.value.total_pages
  const filteredItems = orders?.value?.data?.filter((item: any) => item.status !== 'pending-cart')

  order.value = orders.value

  reviewModalGroup.value = false;
  loading.value = false;
};
</script>

<style scoped>
.orderModal .relative.text-left {
  border-radius: 20px !important;
  overflow: hidden !important;
}
.el-rate {
  --el-rate-void-color: #FF671F !important;
}
</style>
