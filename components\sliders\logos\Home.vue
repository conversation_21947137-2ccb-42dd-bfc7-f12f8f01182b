<template>
  <div class="px-[20px] max-w-[1280px] mx-auto logoSwipper">
    <div class="w-full mx-auto relative md:w-[90%]">
      <div class="top-slider-arrows hidden md:block">
        <div class="swiper-button-prev-outside cursor-pointer absolute left-[-11%] sm:left-[-10%] md:left-[-8%] lg:left-[-6.4%] top-[37%]" @click="swiperInstance?.slideNext()">
          <Icon class="cursor-pointer" name="carbon:chevron-left" size="30" />
        </div>
        <div class="swiper-button-next-outside cursor-pointer absolute right-[-11%] sm:right-[-10%] md:right-[-8%] lg:right-[-6.4%] top-[37%]" @click="swiperInstance?.slidePrev()">
          <Icon class="cursor-pointer" name="carbon:chevron-right" size="30" />
        </div>
      </div>
      <div class="text-[14px] rtl:font-notoSans font-bold font-secondaryFont text-primary mb-4 uppercase md:hidden">
        {{ title }}
      </div>
      <Swiper
        @swiper="onSwiper"
        :modules="[SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
        :loop="true"
        :space-between="10"
        :autoplay="{
          delay: 9000,
          disableOnInteraction: false,
        }"
        :breakpoints="{
          310: {
              slidesPerView: 2,
          },
          400: {
              slidesPerView: 2,
          },
          550: {
              slidesPerView: 3,
          },
          768: {
              slidesPerView: 4,
          },
          1024: {
              slidesPerView: 5,
          },
          1060: {
              slidesPerView: 7,
          },
          1600: {
              slidesPerView: 7,
          },
          1920: {
              slidesPerView: 7,
          },
          2560: {
              slidesPerView: 7,
          },
          3200: {
              slidesPerView: 7,
          },
        }"
      >
        <SwiperSlide v-for="(item, index) in brandss ?? []" :key="index">
          <div class="mx-auto" v-if="item.cat_id">
            <ULink
              draggable="false"
              class="
                cursor-pointer
                bg-white
                truncate
                mx-auto
                text-center
                w-[120px]
                h-[120px]
                p-5
                rounded-full
                flex
                items-center
                justify-between
                border
                border-primary"
                :to="localePath(item.cat_id ? `/category/${item.cat_id}?brand=${item.brand_id}` : '')"
            >
              <img :src="item.brand_logo" :alt="item.brand_name">
            </ULink>
          </div>
        </SwiperSlide>
      </Swiper>
    </div>
  </div>
</template>
<script setup lang="ts">
const localePath = useLocalePath()

// Define the interface for brand items
interface Brand {
  brand_name: string;
  brand_logo: string;
  brand_id: number;
  cat_id: string;
  status: boolean;
}

// Define the props interface
interface Props {
  title?: string;
  logos?: Brand[];
}

const props = defineProps<Props>();
const brandss = ref<Brand[]>();

brandss.value = props.logos?.filter((brand) => brand.cat_id);

// Swiper settings
const swiperInstance = ref<any>();
const onSwiper = (swiper: any) => (swiperInstance.value = swiper);

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.logoSwipper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', () => onMouseOver());
    slide.addEventListener('mouseleave', () => onMouseLeave());
    slide.addEventListener('touchstart', () => onMouseOver());
    slide.addEventListener('touchend', () => onMouseLeave());
  });
});

function onMouseOver() {
  swiperInstance.value?.autoplay.stop();
}

function onMouseLeave() {
  swiperInstance.value?.autoplay.start();
}
</script>
