<template>
  <div class="border-2 border-primary rounded-[20px] p-6 pt-10 bg-thirdColor relative">
    <UBadge
      class="
      bg-secondary
      font-secondaryFont
      font-bold
      text-thirdColor
      text-lg
      rounded-full
      px-4
      py-1
      absolute
      top-[-20px]
      min-w-[263px]"
    >
      <div>{{ $t('1. USER INFORMATION') }}</div>
    </UBadge>
    <div class="border-b border-primary" :class="{'pb-0 border-none': userData?.id}">
      <div class="w-[82%]">
        <div class="mb-3" v-if="visist_id && !tokenCookie">
          <div class="font-monsterrat font-bold text-xl text-primary mb-3">{{ $t('Existing customer or new registration?') }}</div>
          <div class="font-oxygen text-sm text-black">{{ $t('If you have an account, please use on of the below methods in order to log-in.') }}</div>
        </div>
        <div v-else class="font-bold text-xl text-primary mb-3" :class="{'mb-0': userData?.id}">
         <span>{{ $t('Welcome,') }}</span> <span>{{ userData?.first_name }} {{ userData?.last_name }}</span>
        </div>
        <div v-if="titleError" class="text-red-700 font-bold text-[12px] mb-1">
          {{ titleError }}
        </div>
        <div v-if="urlError" class="text-red-700 font-bold text-[12px] mb-2">
          {{ urlError }}
        </div>
        <div class="flex gap-2 items-center my-4" v-if="visist_id && !tokenCookie">
          <AuthSocialLoginCart v-if="!socialLoading" />
          <div v-else class="flex justify-center items-center">
            <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-primary"/>
          </div>
          <div>
            <UButton
              class="
                text-thirdColor
                font-bold
                font-reddit
                rounded-full
                text-lg
                bg-primary
                min-h-[50px]
                lg:px-7
                md:px-3
                lg:py-2
                md:py-1
                ease-in-out
                duration-500
                hover:bg-primary
              "
              color="white"
              variant="solid"
              :to="localePath('/auth/cart/login')"
            >
              {{ $t('SIGN UP / LOGIN') }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-5" v-if="visist_id && !tokenCookie">
      <div class="font-monsterrat font-bold text-primary text-lg">{{ $t('Guest Check-out') }}</div>
      <div class="mt-5">
        <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
          <div class="w-full mx-auto flex flex-col gap-4">
            <div class="relative text-field">
              <UFormGroup name="first_name">
                <input
                  class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                  variant="outline"
                  v-model="state.first_name"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-with-bg' : state.first_name,
                    }"
                  >
                    {{ $t('First Name') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field">
              <UFormGroup name="last_name">
                <input
                  class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                  variant="outline"
                  v-model="state.last_name"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-with-bg' : state.last_name,
                    }"
                  >
                    {{ $t('Last Name') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field">
              <UFormGroup name="mobile_number">
                <input
                  class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                  variant="outline"
                  v-model="state.mobile_number"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    ltr:left-[22px]
                    rtl:right-[22px]
                    top-[13px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-with-bg' : state.mobile_number,
                    }"
                  >
                    {{ $t('Phone') }}
                </span>
                <div v-if="validationError" class="mb-1 text-red-700">{{ $t('Phone number Is Already Used Before') }}</div>
              </UFormGroup>
            </div>
          </div>
          <div class="text-center mt-6">
            <UButton
              type="submit"
              class="
                text-thirdColor
                font-bold
                font-thirdFont
                rounded-full
                text-lg
                uppercase
                border
                border-primary
                bg-primary
                shadow-unset
                ltr:left-[22px]
                rtl:right-[22px]
                lg:px-10
                md:px-3
                lg:py-3
                md:py-1
                hover:scale-[1.1]
                ease-in-out
                duration-500
              "
              variant="solid"
              v-if="!formStatus"
              :loading="loading"
            >
              <div v-if="!loading">{{ $t('SUBMIT') }}</div>
            </UButton>
          </div>
        </UForm>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { persistentCookieOptions } from '~/utils/cookieOptions'

const urlError: any = useState("urlError", () => '')
const titleError: any = useState("titleError", () => '')
const userData: any = useState("userData", () => {})
const socialLoading: any = useState("socialLoading", () => false)

const { t } = useI18n()
const localePath = useLocalePath()

const token = ref()
const visist_id = ref()
const isGuestUser = computed(() => {
  if (!userData.value?.username) return false
  return userData.value.username.startsWith('Guest-')
})

const visitorTokenCookie = useCookie('visitor_token', persistentCookieOptions)
const visitorIdCookie = useCookie('visitor_id', persistentCookieOptions)
const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

onMounted(() => {
  const tokens = tokenCookie.value
  const visitor_id = visitorIdCookie.value
  if (tokens) {
    token.value = tokens
    // Check if user is a guest and clear form fields if needed
    if (isGuestUser.value) {
      state.first_name = undefined
      state.last_name = undefined
      state.mobile_number = undefined
      dataDone.value = false
    }
  }
  if (visitor_id) {
    visist_id.value = visitor_id
  }
})

watch(token, async (current) => {
  if (current) {
    // Check if user is a guest and clear form fields if needed
    if (isGuestUser.value) {
      state.first_name = undefined
      state.last_name = undefined
      state.mobile_number = undefined
      dataDone.value = false
    }
  }
})

// Check-out form
const dataDone = ref(false)
const loading = ref(false)
const formStatus = ref(false)
const schema = z.object({
  first_name: z.string()
  .min(3, t('First Name must be at least 3 characters')),
  last_name: z.string()
  .min(3, t('Last Name must be at least 3 characters')),
  mobile_number: z.string()
  .min(11, t('Phone Number must be at least 11 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  first_name: undefined,
  last_name: undefined,
  mobile_number: undefined,
})


const validationError = ref(false)
const FormPhoneNumber = useState<any>("FormPhoneNumber", () => "")

async function onSubmit (_event: FormSubmitEvent<Schema>) {
  loading.value = true
  validationError.value = false

  // Do something with event.data
  const response = await $fetch(`/api/newBranch/profile/update?username=${userData?.value?.username}`, {
    method: 'post',
    body: state
  }) as any


  if(response.response.valid) {
    const { items: usersData } = await fetchHock(`newBranch/customer/${Number(userData?.value?.id)}`)
    localStorage.setItem("visitor_data", JSON.stringify(usersData.value))
    const userDataString = localStorage.getItem('visitor_data')
    userData.value = userDataString ? JSON.parse(userDataString) : null
    dataDone.value = true
    FormPhoneNumber.value = state.mobile_number
    validationError.value = false
    loading.value = false
  } else {
    validationError.value = true
    loading.value = false
  }

}

// Only populate form fields if user is not a guest
if (userData.value.first_name && !isGuestUser.value) {
  state.first_name = userData.value.first_name
  state.last_name = userData.value.last_name
  state.mobile_number = userData.value.username
  dataDone.value = true
} else if (isGuestUser.value) {
  // Clear form fields if user is a guest
  state.first_name = undefined
  state.last_name = undefined
  state.mobile_number = undefined
  dataDone.value = false
}

watch(userData, async (current) => {
  // Check if the username is a guest username
  const isGuest = current.username && current.username.startsWith('Guest-');

  if (isGuest) {
    // Clear form fields for guest users
    state.first_name = undefined
    state.last_name = undefined
    state.mobile_number = undefined
    dataDone.value = false
  } else if (current.first_name) {
    // Only populate form fields for non-guest users
    state.first_name = current.first_name
    state.last_name = current.last_name
    state.mobile_number = current.username
    dataDone.value = true
  }
})
</script>
<style>
</style>