export default defineEventHandler(async (event) => {
  try {
   const bodyy = await readBody(event)
   const url = `https://twilio-five.vercel.app/verify-code`;
   const method = 'POST'
   
   const headers = {
     'Content-Type': 'application/json', // or 'text/plain', depending on your API
   }

     const response = await $fetch(url, {
       method: method,
       body: bodyy,
       headers: headers,
     })
 
     const data = await response
     return data
 } catch (error: any) {
   console.error('Error in register handler:', error?.data)
   return {
     error: error?.data,
     statusCode: error?.statusCode,
   }
 }
})
