// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  modules: [
    '@nuxt/ui',
    '@nuxtjs/i18n',
    '@sidebase/nuxt-pdf',
    'nuxt-swiper',
    '@element-plus/nuxt',
    '@nuxtjs/device',
    'nuxt-icon',
  ],
  runtimeConfig: {
    public: {
      wpApiUrl: process.env.WP_API_URL,
      wcApiUrl: process.env.WC_API_URL
    }
  },
  colorMode: {
    preference: 'light'
  },  tailwindcss: {
    viewer: false
  },
  i18n: {
    vueI18n: './i18n/i18n.config.ts',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
    },
    strategy: 'prefix_except_default',
    locales: [
      { code: 'en', iso: 'en-US', file: 'en.json', name: 'English', dir: 'ltr' },
      { code: 'ar', iso: 'ar', file: 'ar.json', name: 'العربية', dir: 'rtl' }
    ],
    lazy: true,
    langDir: 'locales',
    defaultLocale: 'en',
    experimental: {
      jsTsFormatResource: true
    },
    debug: process.env.NODE_ENV === 'development'
  },
  css: [
    '@fortawesome/fontawesome-svg-core/styles.css',
    '~/assets/css/tailwind.css',
    '~/assets/css/custom.css',
    '~/assets/css/rtl.css',
  ],

  app: {
    head: {
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'My amazing site.' },
      ],
      link: [
        { rel: "icon", type: "image/x-icon", href: "/favicon.ico" },
        { rel: "icon", type: "image/x-icon", href: "/favicon.ico" },
      ],
      script: [
        { src: 'https://www.merchant.geidea.net/hpp/geideaCheckout.min.js' }
      ]
    },
  },
  // routeRules: {
  //   '/': { swr: 3600 }
  // },
})