<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="pt-10 relative sm:max-w-[60%] mx-auto border-b border-primary pb-8">
      <div class="md:mt-5">
        <div class="font-secondaryFont font-bold text-primary mb-10 text-center mx-auto sm:text-lg sm:max-w-[80%] hidden md:block">{{ $t("Welcome to Spare Finders Please complete the form below to create a new account and enjoy a variety of features.") }}</div>
          <div v-if="titleError" class="text-red-700 font-bold text-lg text-center uppercase">
            {{ titleError }}
          </div>
          <div v-if="urlError" class="mb-2 text-red-700 font-bold text-center mt-2 uppercase text-sm">
            {{ urlError }}
          </div>
          <div class="text-primary font-bold font-secondaryFont text-[15px] md:hidden text-center">{{ $t('Create A New Account!') }}</div>
        <div class="mt-5">
          <div v-if="serverStatus" class="mb-2 text-red-700 font-bold text-lg">{{ serverErrors === $t('Sorry, that username already exists!') ? $t('Sorry, that Phone Number already exists!') : serverErrors }}</div>
          <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
            <div class="w-full mx-auto flex flex-col gap-6">
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="first_name">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      !bg-thirdColor
                      rounded-full
                      py-2
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    variant="outline"
                    v-model="state.first_name"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      !bg-thirdColor
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      custom-auth-style
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-bg-13' : state.first_name,
                      }"
                    >
                      {{ $t('First Name') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="last_name">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      !bg-thirdColor
                      py-2
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    variant="outline"
                    v-model="state.last_name"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      custom-auth-style
                      font-semibold
                      !bg-thirdColor
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-bg-13' : state.last_name,
                      }"
                    >
                      {{ $t('last Name') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="username">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      !bg-thirdColor
                      py-2
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    variant="outline"
                    v-model="state.username"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      !bg-thirdColor
                      custom-auth-style
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-bg-13' : state.username,
                      }"
                    >
                      {{ $t('Phone Number') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="email">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      !bg-thirdColor
                      py-2
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    variant="outline"
                    v-model="state.email"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      !bg-thirdColor
                      opacity-1
                      md:text-base
                      custom-auth-style
                      text-[13px]
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-bg-13' : state.email,
                      }"
                    >
                    {{ $t('email') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="password">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      py-2
                      !bg-thirdColor
                      px-5
                      md:py-3
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    type="password"
                    variant="outline"
                    v-model="state.password"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      !bg-thirdColor
                      opacity-1
                      md:text-base
                      text-[13px]
                      z-[4]
                      custom-auth-style
                      ltr:left-[22px]
                      rtl:right-[22px]
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-bg-13' : state.password,
                      }"
                    >
                      {{ $t('password') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="text-field-bg-13 relative login-register">
                <UFormGroup name="password_confirm">
                  <input
                    class="
                      w-full
                      border
                      border-primary
                      rounded-full
                      py-2
                      px-5
                      md:py-3
                      !bg-thirdColor
                      md:px-5
                      md:rounded-lg
                      focus:outline-0
                    "
                    type="password"
                    variant="outline"
                    v-model="state.password_confirm"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      !bg-thirdColor
                      opacity-1
                      md:text-base
                      text-[13px]
                      custom-auth-style
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      md:top-[13px]
                      top-[11px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-bg-13' : state.password_confirm,
                      }"
                    >
                      {{ $t('confirm password') }}
                  </span>
                </UFormGroup>
              </div>
            </div>
            <UFormGroup class="md:block hidden custom-checkbox" :error="!state.terms && serverStatus ? 'Terms is required' : ''">
              <UCheckbox v-model="state.terms" class="mt-4 bg-thirdColor">
                <template #label>
                  <span class="text-primary text-lg items-center underline">{{ $t('I agree to all terms and conditions.') }}</span>
                </template>
              </UCheckbox>
            </UFormGroup>






            <div class="text-center !mt-10 flex gap-3 justify-center items-center flex-col">
              <UButton
                type="submit"
                class="
                  text-thirdColor
                  font-bold
                  font-thirdFont
                  uppercase
                  rounded-full
                  md:text-lg
                  border
                  border-primary
                  bg-primary
                  shadow-unset
                  lg:px-[140px]
                  md:px-[100px]
                  px-[90px]
                  lg:py-3
                  md:py-1
                  hover:scale-[1.1]
                  rtl:font-notoSans
                  ease-in-out
                  duration-500
                "
                variant="solid"
                :loading="isLoading"
              >
                <div v-if="!isLoading">{{ $t('REGISTER') }}</div>
              </UButton>
              <UButton
                class="
                  text-orangeColor
                  text-base
                  underline
                "
                variant="link"
                :to="localePath('/auth/login')"
              >
                {{ $t('Existing User? Log In') }}
              </UButton>
            </div>
          </UForm>
        </div>
      </div>
    </div>

    <AuthSocialLogin />
    <ModalsAuthRegisterVerify />
  </div>
</template>
<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { ElNotification } from 'element-plus'
import { onMounted } from 'vue'
import { safelyAccessCSSRules } from '~/utils/cssHelper'

const { t } = useI18n()
const localePath = useLocalePath()

const isLoading = ref<boolean>(false)
const phoneNumber: any = useState("phoneNumber", () => '')
const formStatus: any = useState("formStatus", () => null)

const urlError: any = useState("urlError", () => '')
const titleError: any = useState("titleError", () => '')

urlError.value = ''
titleError.value = ''

const showVerificationModal: any = useState("showVerificationModal", () => false)

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  ElNotification({
    message: h('i', {
      style: `color: ${type === 'success' ? '#1D3C34' : '#ff4949'}; font-weight: bold;`
    }, message === 'Sorry, that username already exists!' ? 'Sorry, that Phone Number already exists!' : message),
    position: 'top-right',
    duration: 3000,
    type
  })
}

const schema = z.object({
  email: z.string().email(t('Invalid email')),
  username: z.string()
    .min(11, t('Phone must be at least 11 characters'))
    .regex(/^\d+$/, t('Phone number must contain only digits')),
    password: z.string()
    .min(6, t('Password must be at least 6 characters')),
    password_confirm: z.string()
    .min(6, t('Password confirm must be at least 6 characters')),
    first_name: z.string()
    .min(3, t('First name must be at least 3 characters')),
    last_name: z.string()
    .min(3, t('Last name must be at least 3 characters')),
}).refine((data) => data.password === data.password_confirm, {
  message: t("Passwords don't match"),
  path: ["password_confirm"],
})

type Schema = z.output<typeof schema>

const state = reactive({
  username: undefined,
  email: undefined,
  password: undefined,
  password_confirm: undefined,
  first_name: undefined,
  last_name: undefined,
  updates: false,
  terms: false,
  roles: 'customer'
})


Object.assign(state, {
  username: undefined,
  email: undefined,
  password: undefined,
  password_confirm: undefined,
  first_name: undefined,
  last_name: undefined,
  updates: false,
  terms: false,
  roles: 'customer'
})

const serverErrors = ref('')
const serverStatus = ref(false)
const passwordMatchError = ref(false)
const phoneNumberFormatError = ref(false)

// Function to validate password match
const validatePasswordMatch = () => {
  const { password, password_confirm } = state

  // Only validate when both passwords are entered and the confirmation is complete
  // (we check if they have the same length to determine if user has finished typing)
  if (password && password_confirm) {
    // Type assertion to tell TypeScript these are strings
    const pass = password as string
    const confirmPass = password_confirm as string

    // Only show error if the confirmation password is fully typed
    // (same or greater length as the original password)
    if (confirmPass.length >= pass.length && pass !== confirmPass) {
      if (!passwordMatchError.value) {
        // Only show notification when the error first appears
        showNotification(t("Passwords don't match"), 'error')
      }
      passwordMatchError.value = true
      serverErrors.value = t("Passwords don't match")
      serverStatus.value = true
    } else if (confirmPass.length < pass.length) {
      // User is still typing the confirmation password, don't show error yet
      passwordMatchError.value = false
      // Only clear server status if there are no other errors
      if (!phoneNumberFormatError.value) {
        serverStatus.value = false
        serverErrors.value = ''
      }
    } else {
      // Passwords match
      passwordMatchError.value = false
      // Only clear server status if there are no other errors
      if (!phoneNumberFormatError.value) {
        serverStatus.value = false
        serverErrors.value = ''
      }
    }
  } else {
    // One or both passwords are not entered yet
    passwordMatchError.value = false
    // Only clear server status if there are no other errors
    if (!phoneNumberFormatError.value) {
      serverStatus.value = false
      serverErrors.value = ''
    }
  }
}

// Function to validate phone number format
const validatePhoneNumber = () => {
  const { username } = state
  if (username) {
    // Type assertion to tell TypeScript this is a string
    const phone = username as string

    if (!/^\d+$/.test(phone)) {
      if (!phoneNumberFormatError.value) {
        // Only show notification when the error first appears
        showNotification(t("Phone number must contain only digits"), 'error')
      }
      phoneNumberFormatError.value = true
      serverErrors.value = t("Phone number must contain only digits")
      serverStatus.value = true
    } else {
      phoneNumberFormatError.value = false
      // Only clear server status if there are no other errors
      if (!passwordMatchError.value) {
        serverStatus.value = false
        serverErrors.value = ''
      }
    }
  } else {
    phoneNumberFormatError.value = false
    // Only clear server status if there are no other errors
    if (!passwordMatchError.value) {
      serverStatus.value = false
      serverErrors.value = ''
    }
  }
}


// Watch for changes in password and password_confirm fields
watch([() => state.password, () => state.password_confirm], () => {
  validatePasswordMatch()
})

// Watch for changes in username (phone number) field
watch(() => state.username, () => {
  validatePhoneNumber()
})

// Safely initialize any CSS-related operations
onMounted(() => {
  safelyAccessCSSRules(() => {
    // Any code that might access CSS rules should go here
    // This ensures the CSS is fully loaded before we try to access it

    // For example, if you need to access CSS rules for animations or transitions:
    const formElements = document.querySelectorAll('.login-register input');
    formElements.forEach(element => {
      // Use computed style instead of directly accessing cssRules
      const el = element as HTMLElement;
      if (el && el.style) {
        // Apply any necessary style changes safely
        el.style.transition = 'all 0.3s ease';
      }
    });
  });
});

async function onSubmit (_event: FormSubmitEvent<Schema>) {
  isLoading.value = true

  // Run validations before submitting
  validatePasswordMatch()
  validatePhoneNumber()

  // Check if there are any validation errors
  if (passwordMatchError.value || phoneNumberFormatError.value) {
    isLoading.value = false
    return
  }

  // Check terms agreement
  if (!state.terms) {
    serverStatus.value = true
    isLoading.value = false
    return
  }
try {
    const response: any = await $fetch('/api/register', {
      method: 'post',
      body: state
    })

    if(response.statusCode === 500) {
      serverStatus.value = true
      serverErrors.value = response.error.message
      showNotification(response.error.message, 'error')
      window.scrollTo({ top: 0, behavior: 'smooth' })
      isLoading.value = false
      return
    }

    phoneNumber.value = state.username
    formStatus.value = state
    showVerificationModal.value = true
    // Handle success
    // Reset form
  } catch (err) {
    // Handle error
    serverStatus.value = true
    window.scrollTo({ top: 0, behavior: 'smooth' })
    isLoading.value = false
  }

}
</script>
<style>
.custom-checkbox input {
  border: 1px solid #1D3C34;
  background-color: #E9EAC8;
}

.custom-checkbox .relative.flex.items-start.mt-4.bg-thirdColor {
  align-items: center;
}
</style>