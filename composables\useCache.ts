import { ref } from 'vue'

interface CacheConfig {
  enabled: boolean
  ttl: number // Time to live in milliseconds
  backgroundRefresh: boolean
}

interface CacheItem {
  data: any
  timestamp: number
  endpoint: string
}

const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enabled: true,
  ttl: 5 * 60 * 1000, // 5 minutes
  backgroundRefresh: true
}

// Endpoints that should not be cached
const NO_CACHE_ENDPOINTS = [
  'orders',
  'cart',
  'login',
  'auth',
  'shipping',
  'customer',
  'address',
  'newBranch/orders',
  'payment'
]

// Check if localStorage is available
const isLocalStorageAvailable = (): boolean => {
  try {
    return typeof window !== 'undefined' && window.localStorage !== undefined
  } catch (e) {
    return false
  }
}

export const useCache = () => {
  const isRefreshing = ref<boolean>(false)

  const getCacheKey = (endpoint: string, params?: Record<string, any>): string => {
    const key = params ? `${endpoint}:${JSON.stringify(params)}` : endpoint
    return `cache:${key}`
  }

  const isCacheable = (endpoint: string): boolean => {
    return !NO_CACHE_ENDPOINTS.some(noCache => endpoint.includes(noCache))
  }

  const getCachedData = (key: string): CacheItem | null => {
    if (!isLocalStorageAvailable()) return null

    try {
      const cached = localStorage.getItem(key)
      if (!cached) return null

      const cacheItem: CacheItem = JSON.parse(cached)
      const now = Date.now()

      // Check if cache is expired
      if (now - cacheItem.timestamp > DEFAULT_CACHE_CONFIG.ttl) {
        localStorage.removeItem(key)
        return null
      }

      return cacheItem
    } catch (error) {
      console.error('Error reading from cache:', error)
      return null
    }
  }

  const setCachedData = (key: string, data: any, endpoint: string): void => {
    if (!isLocalStorageAvailable()) return

    try {
      const cacheItem: CacheItem = {
        data,
        timestamp: Date.now(),
        endpoint
      }
      localStorage.setItem(key, JSON.stringify(cacheItem))
    } catch (error) {
      console.error('Error writing to cache:', error)
    }
  }

  const refreshCache = async (
    endpoint: string,
    params: Record<string, any> | undefined,
    fetchFn: () => Promise<any>
  ): Promise<void> => {
    if (isRefreshing.value) return

    isRefreshing.value = true
    try {
      const freshData = await fetchFn()
      const key = getCacheKey(endpoint, params)
      setCachedData(key, freshData, endpoint)
    } catch (error) {
      console.error('Error refreshing cache:', error)
    } finally {
      isRefreshing.value = false
    }
  }

  const getData = async (
    endpoint: string,
    params: Record<string, any> | undefined,
    fetchFn: () => Promise<any>,
    config: Partial<CacheConfig> = {}
  ): Promise<any> => {
    const finalConfig = { ...DEFAULT_CACHE_CONFIG, ...config }
    const key = getCacheKey(endpoint, params)

    // If caching is disabled or endpoint should not be cached, fetch fresh data
    if (!finalConfig.enabled || !isCacheable(endpoint)) {
      return await fetchFn()
    }

    // Try to get cached data
    const cached = getCachedData(key)
    if (cached) {
      // If background refresh is enabled, refresh cache in background
      if (finalConfig.backgroundRefresh) {
        refreshCache(endpoint, params, fetchFn)
      }
      return cached.data
    }

    // If no cache, fetch fresh data
    const freshData = await fetchFn()
    setCachedData(key, freshData, endpoint)
    return freshData
  }

  return {
    getData,
    refreshCache,
    isCacheable
  }
} 