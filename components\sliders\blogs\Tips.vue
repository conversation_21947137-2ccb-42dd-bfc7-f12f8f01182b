<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto tipsSwipper">
      <div class="relative tipsSwiper hidden md:block">
        <Swiper
          @swiper="onSwiper"
          :modules="[ SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
          :loop="true"
          :space-between="20"
          :autoplay="{
            delay: 4000,
            disableOnInteraction: false,
          }"
          :breakpoints="{
            360: {
                slidesPerView: 1,
            },
            640: {
                slidesPerView: 1,
            },
            768: {
                slidesPerView: 2,
            },
            1024: {
                slidesPerView: 3,
            },
            1400: {
                slidesPerView: 3,
            },
            1600: {
                slidesPerView: 3,
            },
            1920: {
                slidesPerView: 3,
            },
            2560: {
                slidesPerView: 3,
            },
            3200: {
                slidesPerView: 3,
            },
          }"
        >
        <div class="absolute top-0 left-0 w-full z-20">
          <div class="flex justify-between items-center w-full">
            <div class="font-secondaryFont uppercase text-primary text-lg sm:text-[25px] font-bold rtl:font-notoSans">
              {{ title }}
            </div>
            <div class="flex">
              <div class="top-slider-arrows hidden sm:flex gap-2">
                <div class="swiper-button-prev-outside cursor-pointer w-[40px] h-[40px] bg-primary rounded-full flex justify-center items-center" @click="swiperInstance?.slideNext()">
                  <Icon v-if="locale === 'en'" class="text-thirdColor cursor-pointer" name="carbon:chevron-left" size="30" />
                  <Icon v-else class="text-thirdColor cursor-pointer" name="carbon:chevron-right" size="30" />
                </div>
                <div class="swiper-button-next-outside cursor-pointer w-[40px] h-[40px] bg-primary rounded-full flex justify-center items-center" @click="swiperInstance?.slidePrev()">
                  <Icon v-if="locale === 'en'" class="text-thirdColor cursor-pointer" name="carbon:chevron-right" size="30" />
                  <Icon v-else class="text-thirdColor cursor-pointer" name="carbon:chevron-left" size="30" />
                </div>
              </div>
            </div>
          </div>
        </div>
          <SwiperSlide v-for="(item, index) in posts ?? []" :key="index">
            <div class="mx-auto">
              <div class="mt-[80px]">
                <ULink :to="localePath(`/${item.link}`)">
                  <div class="relative truncate rounded-[30px] border border-primary">
                    <img class="w-full h-[200px]" :src="item?.featured_image ?? item?.featured_image_url?.thumbnail" />
                    <div class="bg-primary">
                      <div class="py-8 px-8" v-if="locale === 'en'">
                        <div class="text-thirdColor italic text-xl font-semibold font-primaryFont truncate">{{ typeof item?.title === 'string' ? item?.title : item?.title?.rendered }}</div>
                        <div class="text-wrap text-thirdColor mt-4 font-thirdFont text-sm line-clamp-7" v-html="typeof item?.content === 'string' ? item?.content : item?.content?.rendered"></div>
                      </div>
                      <div class="py-8 px-8" v-if="locale === 'ar'">
                        <div class="text-thirdColor text-xl font-semibold rtl:font-notoSans font-primaryFont truncate">{{ typeof item?.title_ar === 'string' ? item?.title_ar : item?.title?.rendered_ar }}</div>
                        <div class="text-wrap text-thirdColor mt-4 font-thirdFont rtl:font-notoSans text-sm line-clamp-7" v-html="typeof item?.content_ar === 'string' ? item?.content_ar : item?.content?.rendered_ar"></div>
                      </div>
                    </div>
                  </div>
                </ULink>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
    <div class="relative md:hidden">
      <div class="sticky z-[55] top-0 rtl:font-notoSans left-0 w-full font-secondaryFont uppercase text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
        {{ title }}
      </div>
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div v-for="(item, index) in posts ?? []" :key="index">
          <div class="mx-auto">
            <div class="mt-[30px]">
              <ULink :to="localePath(`/${item.link}`)">
                <div class="relative truncate rounded-[30px] border border-primary">
                  <img class="w-full" :src="item?.featured_image ?? item?.featured_image_url?.thumbnail" />
                  <div class="bg-primary">
                    <div class="py-8 px-8" v-if="locale === 'en'">
                      <div class="text-thirdColor italic text-xl font-semibold font-primaryFont truncate"> {{ typeof item?.title === 'string' ? item?.title : item?.title?.rendered }} </div>
                      <div class="text-wrap text-thirdColor mt-4 font-thirdFont text-sm line-clamp-7" v-html="typeof item?.content === 'string' ? item?.content : item?.content?.rendered"></div>
                    </div>
                    <div class="py-8 px-8" v-if="locale === 'ar'">
                      <div class="text-thirdColor text-xl rtl:font-notoSans font-semibold font-primaryFont truncate"> {{ typeof item?.title_ar === 'string' ? item?.title_ar : item?.title?.rendered_ar }} </div>
                      <div class="text-wrap text-thirdColor mt-4 rtl:font-notoSans font-thirdFont text-sm line-clamp-7" v-html="typeof item?.content_ar === 'string' ? item?.content_ar : item?.content?.rendered_ar"></div>
                    </div>
                  </div>
                </div>
              </ULink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const { locale } = useI18n()
const localePath = useLocalePath()

// Define the interface for blog post items
interface BlogPost {
  title?: string | {
    rendered?: string;
    rendered_ar?: string;
  };
  title_ar?: string | {
    rendered?: string;
    rendered_ar?: string;
  };
  content?: string | {
    rendered?: string;
    rendered_ar?: string;
  };
  content_ar?: string | {
    rendered?: string;
    rendered_ar?: string;
  };
  featured_image?: string;
  link?: string;
  featured_image_url?: {
    thumbnail: string;
  };
}

// Define the props interface
interface Props {
  title?: string;
  posts?: BlogPost[];
}

defineProps<Props>();

// Swiper settings
const swiperInstance = ref<any>();
const onSwiper = (swiper: any) => (swiperInstance.value = swiper);

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.tipsSwipper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', () => onMouseOver());
    slide.addEventListener('mouseleave', () => onMouseLeave());
    slide.addEventListener('touchstart', () => onMouseOver());
    slide.addEventListener('touchend', () => onMouseLeave());
  });
});

function onMouseOver() {
  swiperInstance.value?.autoplay.stop();
}

function onMouseLeave() {
  swiperInstance.value?.autoplay.start();
}
</script>