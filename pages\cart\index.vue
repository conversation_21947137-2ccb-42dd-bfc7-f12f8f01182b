<template>
  <div class="relative">
    <div class="mt-[60px] hidden md:block">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="bg-cartColor rounded-2xl p-5 sm:p-10 text-black">
          <div class="flex md:gap-20 gap-4 justify-between flex-col md:flex-row items-center">
            <div class="font-secondaryFont text-sm sm:text-lg text-primary rtl:font-notoSans">
              {{ locale === 'en' ? pagesContent?.cart?.page_banner_desc : pagesContent?.cart?.page_banner_desc_ar }}
            </div>
            <UButton
              :to="localePath(`/${pagesContent?.cart?.banner_link_slug}`)"
              class="
                rounded-full
                flex
                items-center
                justify-center
                font-bold
                sm:text-xl
                py-3
                lg:px-24
                px-14
                font-thirdFont
                text-thirdColor
                uppercase
                rtl:font-notoSans
                bg-orangeColor
                border-2
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                transition-all
                duration-500
                ease-in-out"
            >
            <span v-html="locale === 'en' ? pagesContent?.cart?.banner_link_text : pagesContent?.cart?.banner_link_text_ar"></span>
            </UButton>
          </div>

        </div>
      </div>
    </div>
    <div class="mt-3 md:mt-[60px]">
      <ClientOnly>
        <CartOrders />
      </ClientOnly>
    </div>
    <div class="mt-[60px] mb-[60px] hidden md:block">
      <SlidersProduct :products="products" :title="$t('Related products')" />
    </div>
  </div>
</template>
<script setup lang="ts">
const pagesContent: any = useState("pagesContent", () => {})

const { locale } = useI18n()
const localePath = useLocalePath()

// Get Products
const products = ref<any>()
const page: any = useState("page", () => [])
page.value = 1
const perPage: any = useState("perPage", () => [])
perPage.value = 10
const loading: any = useState("loading", () => [])
loading.value = false

const fetchProducts = async () => {
  loading.value = true
  const { items } = await fetchHock(`refactor/products/54?page=${page.value}&posts_per_page=${perPage.value}`)
  products.value = items?.value?.products
  loading.value = false
}

onMounted(async () => {
  await fetchProducts()
})
</script>
<style>
.vendors-rating .el-rate {
  height: 20px;
  --el-rate-disabled-void-color: #0f030338;
  --el-rate-icon-size: 30px;
  --el-rate-icon-margin: -6px;
}
</style>
