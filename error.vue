<template>
  <!-- <NuxtLayout> -->
    <div>
      <Header @getGuestData="getGuestData" />
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <!-- 404 Design Box -->
        <div class="bg-thirdColor mt-14 text-center p-5 md:px-36 md:py-14 rounded-xl">
          <div class="font-bold text-base md:text-xl text-primary font-secondaryFont" v-html="errors?.banner">
          </div>
        </div>
      </div>
      <SlidersProduct
        class="mt-14"
        :products="errors?.section_1?.products"
        :title="errors?.section_1?.title"
      />
      <div class="px-[20px] max-w-[1280px] mx-auto mt-5 mb-10">
        <div class="border-b border-primary"></div>
      </div>
      <div class="px-[20px] max-w-[1280px] mx-auto mb-10">
        <div class="font-secondaryFont uppercase text-primary text-lg sm:text-[25px] font-bold mb-10">{{ errors?.section_2?.title }}</div>
        <div class="flex justify-center md:justify-between flex-wrap lg:flex-nowrap gap-4 items-center">
          <div
            v-for="link in errors?.section_2?.links ?? []"
            :key="link.id"
            >
            <NuxtLink :to="`/category/${link?.id}`" class="bg-thirdColor uppercase block font-bold text-xl text-primary py-4 min-w-[300px] lg:min-w-[auto] xl:min-w-[300px] text-center px-14 rounded-full">
              {{ link?.name }}
            </NuxtLink>
          </div>
        </div>
      </div>
      <div class="px-[20px] max-w-[1280px] mx-auto mt-5 mb-10">
        <div class="border-b border-primary"></div>
      </div>
      <div class="px-[20px] max-w-[1280px] mx-auto mb-40">
        <div class="font-secondaryFont uppercase text-primary text-lg sm:text-[25px] font-bold mb-10">{{ errors?.section_3?.title }}</div>
        <div class="flex justify-center md:justify-between flex-wrap lg:flex-nowrap gap-4 items-center">
          <div
            v-for="link in errors?.section_3?.links ?? []"
            :key="link.id"
            >
            <NuxtLink
              :to="`/${link?.slug === 'home' ? '' : link?.slug}`"
              v-html="link?.title"
              class="bg-thirdColor uppercase block font-bold text-xl text-primary py-4 min-w-[300px] lg:min-w-[auto] xl:min-w-[300px] text-center px-14 rounded-full"
            >
            </NuxtLink>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  <!-- </NuxtLayout> -->
</template>

<script setup lang="ts">
import type { PagesContent } from '~/types/pagesContent'
import type { Footer } from '~/types/footer'
import type { NavigationLinks } from '~/types/links'
import type { MenuStructure } from '~/types/navLinks'
import type { UserData } from '~/types/user'
import type { CartData } from '~/types/defaultCart'

const error = useError();

const handleError = () => {
  clearError({
    redirect: '/',
  });
};

// Set HTML dir attribute based on locale with safe i18n usage
const { locale } = useSafeI18n()
const direction = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const phoneNumber: any = useState("phoneNumber", () => '')
const { items: errors } = await fetchHock(`newBranch/error`)

// Client-side only code for document manipulation
onMounted(() => {
  // Set initial direction
  if (direction && direction.value) {
    document.documentElement.setAttribute('dir', direction.value)
    
    // Watch for locale changes and update HTML dir attribute
    watch(direction, (newDir) => {
      document.documentElement.setAttribute('dir', newDir)
    })
  }
})

// Cookies Data with persistent storage (30 days expiration)
import { persistentCookieOptions } from '~/utils/cookieOptions'

const visitorTokenCookie = useCookie('visitor_token', persistentCookieOptions)
const visitorIdCookie = useCookie('visitor_id', persistentCookieOptions)
const tokenCookie = useCookie('token', persistentCookieOptions)
const userIdCookie = useCookie('user_id', persistentCookieOptions)

// Main Variables
// const token = useState<string>("token", () => '')

const userData = useState<UserData>("userData", () => ({}))
const socialData = useState<number | null>("socialData", () => null)
const cartItems = useState<CartData>("cartItems", () => ({}))

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// VISITOR LOGIC
// Create a unique ID for the visitor
const uniqueid = (): string => {
  // always start with a letter (for DOM friendlyness)
  let idstr = String.fromCharCode(Math.floor((Math.random() * 25) + 65));
  do {
    // between numbers and characters (48 is 0 and 90 is Z (42-48 = 90)
    const ascicode = Math.floor((Math.random() * 42) + 48);
    if (ascicode < 58 || ascicode > 64) {
      // exclude all chars between : (58) and @ (64)
      idstr += String.fromCharCode(ascicode)
    }
  } while (idstr.length < 32)

  return idstr
}

if (!visitorTokenCookie.value)  {
  visitorTokenCookie.value = uniqueid()
  const response = await $fetch<{ id: string }>(`/api/newBranch/guest/${visitorTokenCookie.value}`, {
    method: 'post'
  })
  visitorIdCookie.value = response.id
}

// Get the Data for guest user and orders
if (visitorIdCookie.value && visitorTokenCookie.value && !tokenCookie.value && !userIdCookie.value) {
  const { items } = await fetchHock(`newBranch/orders/${Number(visitorIdCookie.value)}`)
  const { items: usersData } = await fetchHock(`newBranch/customer/${Number(visitorIdCookie.value)}`)
  userData.value = usersData.value
  cartItems.value = items.value
}

// Get the Data for Guest user and orders
const getGuestData = async () => {
  if (visitorIdCookie.value) {
    visitorTokenCookie.value = uniqueid()
    const { items } = await fetchHock(`newBranch/orders/${Number(visitorIdCookie.value)}`)
    const { items: usersData } = await fetchHock(`newBranch/customer/${Number(visitorIdCookie.value)}`)
    userData.value = usersData.value
    cartItems.value = items.value
  }
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// LOGGED IN LOGIC
// Get the Data for logged in user and orders
if (tokenCookie.value && userIdCookie.value) {
  const { items } = await fetchHock(`newBranch/orders/${Number(userIdCookie.value)}`)
  const { items: usersData } = await fetchHock(`newBranch/customer/${Number(userIdCookie.value)}`)
  userData.value = usersData.value
  cartItems.value = items.value
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Static content and links
const navLinks = useState<MenuStructure>("navLinks", () => ({} as MenuStructure))
const { items: sideLinks } = await fetchHock("newBranch/menus")
navLinks.value = sideLinks.value

const links = useState<NavigationLinks>("links", () => [])
const { items: linksData } = await fetchHock("newBranch/links")
links.value = linksData.value


const pagesContent = useState<PagesContent>("pagesContent", () => ({} as PagesContent))
const { items: pageContent } = await fetchHock(`newBranch/content`)
pagesContent.value = pageContent.value

const footer = useState<Footer>("footer", () => ({} as Footer))
const { items: footerData } = await fetchHock("newBranch/footer")
footer.value = footerData.value
</script> 