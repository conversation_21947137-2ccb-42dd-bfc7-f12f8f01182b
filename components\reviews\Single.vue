<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <div class="relative sm:px-[50px] mt-2 sm:mt-7 recentlySwiper"  v-if="reviews.reviews.length">
      <div class="top-slider-arrows hidden sm:block" v-if="reviews.reviews.length < 3">
        <div
          class="
            swiper-button-prev-outside
            cursor-pointer
            absolute
            top-[38%]
            left-0
          "
          @click="swiperInstance.slideNext()"
        >
          <svg width="16" height="27" viewBox="0 0 16 27" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.5 1L2 13.5L14.5 26" stroke="#1D3C34" stroke-width="2"/>
          </svg>
        </div>
        <div
          class="
            swiper-button-next-outside
            cursor-pointer
            absolute
            top-[38%]
            right-0
          " 
          @click="swiperInstance.slidePrev()"
        >
          <svg width="16" height="27" viewBox="0 0 16 27" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.5 26L14 13.5L1.5 0.999998" stroke="#1D3C34" stroke-width="2"/>
          </svg>
        </div>
      </div>
      <Swiper
        @swiper="onSwiper"
        :modules="[ SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
        :loop="true"
        :space-between="20"
        :autoplay="{
          delay: 4000,
          disableOnInteraction: false,
        }"
        :breakpoints="{
          360: {
              slidesPerView: 2,
          },
          640: {
              slidesPerView: 2,
          },
          768: {
              slidesPerView: 2,
          },
          830: {
              slidesPerView: 3,
          },
          1024: {
              slidesPerView: 3,
          },
          1400: {
              slidesPerView: 3,
          },
          1600: {
              slidesPerView: 3,
          },
          1920: {
              slidesPerView: 3,
          },
          2560: {
              slidesPerView: 3,
          },
          3200: {
              slidesPerView: 3,
          },
        }"
      >
        <SwiperSlide v-for="(item, index) in reviews.reviews ?? []" :key="index">
          <div class="mx-auto w-full">
            <div
              class="
                rounded-md
                py-5
                px-5
                bg-filter
                overflow-hidden
              "
            >
              <div class="flex flex-col sm:flex-row sm:items-center font-medium text-textLight text-base uppercase font-secondaryFont">
                <div class="sm:pe-[13px] sm:h-[20px] mb-[6px] sm:border-r border-primary rtl:font-notoSans">{{ locale === 'en' ? item?.first_name : item?.first_name_ar }} {{ locale === 'en' ? item?.last_name : item?.last_name_ar }}</div>
                <div class="sm:ps-[14px] sm:h-[20px] mb-[6px] rtl:font-notoSans">{{ locale === 'en' ? item?.date : item?.date_ar }}</div>
              </div>
              <div class="card-rating mb-3 sm:mb-0">
                <el-rate :colors="	['#FF671F', '#FF671F', '#FF671F', '#FF671F', '#FF671F']"  v-model="item.rate" size="large" allow-half disabled />
              </div>
              <div>
                <p class="text-textLight text-lg font-bold rtl:font-notoSans">
                  {{ locale === 'en' ? item?.content : item?.content_ar }}
                </p>
              </div>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
    </div>
    <div class="border-b border-primary pb-20" v-if="reviews.reviews.length"></div>
  </div>
</template>
<script setup lang="ts">
const reviews: any = useState('reviews', () => [])
const { locale } = useI18n()

// Swipper settings
const swiperInstance = ref()
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.recentlySwiper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', onMouseOver)
    slide.addEventListener('mouseleave', onMouseLeave)
    slide.addEventListener('touchstart', onMouseOver)
    slide.addEventListener('touchend', onMouseLeave)
  })
})

function onMouseOver(event: any) {
  swiperInstance.value.autoplay.stop()
}

function onMouseLeave(event: any) {
  swiperInstance.value.autoplay.start()
}
</script>

<style>
</style>