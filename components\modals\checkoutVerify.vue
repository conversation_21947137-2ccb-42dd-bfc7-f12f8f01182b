<template>
  <div>
    <UModal v-model="showVerificationModal" class="custom-color-for-modal">
      <div class="px-4 md:px-14 py-3 md:py-8 bg-thirdColor mb-[50px] md:mb-0">
        <h2 class="md:text-xl font-bold font-secondaryFont text-secondary mb-4 uppercase">{{ $t('verify phone number') }}</h2>
        <!-- Add form for submitting mobile number -->
        <form v-if="!isMobileSubmitted && !FormPhoneNumber" @submit.prevent="onSubmit">
          <div class="text-field-bg-13 relative login-register mt-5">
            <UFormGroup name="username">
              <input
                class="
                  w-full
                  border
                  border-primary
                  rounded-full
                  !bg-thirdColor
                  py-2
                  px-5
                  md:py-3
                  md:px-5
                  md:rounded-lg
                  focus:outline-0
                "
                variant="outline"
                v-model="state.mobile_number"
                required
                placeholder=" "
              >
              <span
              class="
                uppercase
                absolute
                text-primary
                font-semibold
                opacity-1
                md:text-base
                text-[13px]
                z-[4]
                ltr:left-[22px]
                rtl:right-[22px]
                md:top-[13px]
                top-[11px]
                transition-all
                duration-100
                custom-auth-style
                ease-in-out"
                :class="{
                  'placeholder-animation-bg-13' : state.mobile_number,
                }"
              >
                {{ $t('Phone Number') }}
              </span>
            </UFormGroup>
          </div>
          <div v-if="mobileError" class="text-red-500 text-sm mb-4">{{ mobileError }}</div>
          <div class="flex justify-center">
            <UButton 
              type="submit"
              class="bg-orangeColor text-thirdColor uppercase font-bold md:text-base text-[14px] mt-5 md:py-3 py-2 px-8 md:px-14 rounded-full"
              :loading="loading"
            >
              {{ $t('Submit') }}
            </UButton>
          </div>
        </form>
        <!-- Existing verification code inputs -->
        <div class="space-y-4" v-else>
          <div class="flex items-center justify-between gap-2">
            <template v-for="(digit, index) in 6" :key="index">
              <div class="relative">
                <input
                  :ref="el => inputRefs[index] = el"
                  v-model="verificationCode[index]"
                  type="text"
                  maxlength="1"
                  :disabled="!codeSent"
                  :class="[
                    'md:w-12 md:h-12 w-[35px] h-[35px] border border-primary rounded text-center text-xl font-bold focus:outline-none relative z-10',
                    codeSent ? 'bg-thirdColor' : 'bg-gray-300 text-gray-500' // Change color if disabled
                  ]"
                  @input="handleInput(index)"
                  @keydown="handleKeydown($event, index)"
                />
                <span 
                  class="
                    absolute 
                    top-1/2 
                    left-1/2 
                    transform 
                    -translate-x-1/2 
                    -translate-y-1/2 
                    text-primary 
                    font-bold 
                    text-2xl
                    pointer-events-none
                    z-0
                    transition-opacity
                    duration-200
                  "
                  :class="{
                    'opacity-0': verificationCode[index],
                    'opacity-100': !verificationCode[index]
                  }"
                >
                  -
                </span>
              </div>
            </template>
          </div>
          <div v-if="error" class="font-secondaryFont" style="color: #FF0101;">{{ $t('The code is incorrect. Please try again.') }}</div>
          <div v-if="success" class="font-secondaryFont" style="color: #4B9560;">{{ $t('The code is correct, and your number has been successfully verified.') }}</div>
           



                  <!-- Add verification method selection -->
        <div class="mb-6 border-b border-primary">
          <div class="flex flex-col gap-3 mb-4">
            <label class="flex cursor-pointer">
              <input
                type="radio"
                v-model="verificationMethod"
                value="sms"
                class="hidden"
              />
              <span class="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center mr-2">
                <span 
                  class="w-3 h-3 rounded-full bg-primary"
                  :class="{ 'opacity-0': verificationMethod !== 'sms' }"
                ></span>
              </span>
              <span class="text-primary font-secondaryFont md:text-base text-[13px]"><span class="font-bold">{{ $t('SMS') }}</span> {{ $t('“Receive a code via text message.”') }}</span>
            </label>
            <label class="flex cursor-pointer">
              <input
                type="radio"
                v-model="verificationMethod"
                value="whatsapp"
                class="hidden"
              />
              <span class="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center mr-2">
                <span 
                  class="w-3 h-3 rounded-full bg-primary"
                  :class="{ 'opacity-0': verificationMethod !== 'whatsapp' }"
                ></span>
              </span>
              <span class="text-primary font-secondaryFont md:text-base text-[13px]"><span class="font-bold">{{ $t('WhatsApp') }}</span> {{ $t('“Receive a code via WhatsApp message.”') }}</span>
            </label>
          </div>
        </div>

        <div class="font-secondaryFont md:text-base text-[13px]" style="color: #1E1E1E;">
          {{ $t('You’ll receive a 6 digit code via your preferred method (SMS or WhatsApp). Enter the code above to complete the verification. The code is valid for 10 minutes.') }}
        </div>












          <div class="flex justify-center">
            <UButton 
              class="bg-orangeColor text-thirdColor uppercase font-bold md:text-base text-[14px] md:py-3 py-2 px-8 md:px-14 rounded-full"
              @click="handleSendCode"
              :disabled="countdown > 0"
              :loading="verifyLoading"
            >
              {{ $t('SEND CODE') }}
            </UButton>
          </div>
          <div class="text-center font-secondaryFont text-primary text-[13px]">
            {{ countdown > 0 ? `${$t('You can resend the code in')} ${countdown} $t('seconds.')` : $t('You can now resend the code.') }}
          </div>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
const showVerificationModal: any = useState("showVerificationModal", () => true)
const verificationCode = ref(Array(6).fill(''))
const inputRefs = ref<any>([])
const isCompleted = computed(() => verificationCode.value.every(digit => digit !== ''))
const codeStatus: any = useState("codeStatus", () => false)
const error = ref(false)
const success = ref(false)
const userData: any = useState("userData", () => [])
const verifyLoading = ref(false)
const FormPhoneNumber = useState<any>("FormPhoneNumber", () => "")
const codeSent = ref(false) // Track if the code has been sent
const loading = ref(false)

const { t } = useI18n()

const verificationMethod = ref('')

const countdown = ref(0)
const countdownInterval = ref<NodeJS.Timeout | null>(null)

const handleInput = async (index: number) => {
  // Ensure single character
  if (verificationCode.value[index]?.length > 1) {
    verificationCode.value[index] = verificationCode.value[index].slice(0, 1)
  }
  
  // Auto focus next input
  if (verificationCode.value[index] && index < 5) {
    inputRefs.value[index + 1]?.focus()
  }

  // Check if all digits are filled
  if (isCompleted.value) {
    verifyLoading.value = true
    const verified: any = await $fetch('/api/register/verifyCode', {
      method: 'post',
      body: {
        phoneNumber: `+2${FormPhoneNumber.value}`,
        code: verificationCode.value.join('')
      }
    })
    
    if (!verified.valid) {
      error.value = true
      success.value = false
      codeStatus.value = false
      verificationCode.value = Array(6).fill('')
      inputRefs.value[0]?.focus()
    } else {
      codeStatus.value = true
      success.value = true
      error.value = false


      const responses: any = await $fetch('/api/register/customVerify', {
        method: 'post',
        body: {
          user_id: userData?.value?.id
        }
      })
      // Close modal
        closeModal()
    }
    verifyLoading.value = false
  }
}

const handleKeydown = (event: KeyboardEvent, index: number) => {
  // Handle backspace
  if (event.key === 'Backspace' && !verificationCode.value[index] && index > 0) {
    verificationCode.value[index - 1] = ''
    inputRefs.value[index - 1]?.focus()
  }
}

const closeModal = () => {
  showVerificationModal.value = false
  verificationCode.value = Array(6).fill('')
}

const sendVerificationCode = async () => {
  try {
    if (verificationMethod.value === 'whatsapp') {
      await $fetch('/api/register/whatsAppVerify', {
        method: 'post',
        body: {
          phoneNumber: `+2${FormPhoneNumber.value}`
        }
      })
    } else {
      await $fetch('/api/register/smsVerify', {
        method: 'post',
        body: {
          phoneNumber: `+2${FormPhoneNumber.value}`
        }
      })
    }
  } catch (error) {
    console.error('Failed to send verification code:', error)
  }
}

const startCountdown = () => {
  countdown.value = 120 // 2 minutes in seconds
  if (countdownInterval.value) clearInterval(countdownInterval.value)
  
  countdownInterval.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      if (countdownInterval.value) clearInterval(countdownInterval.value)
    }
  }, 1000)
}

const handleSendCode = async () => {
  try {
    await sendVerificationCode()
    codeSent.value = true // Set to true after sending the code
    startCountdown()
  } catch (error) {
    console.error('Failed to send code:', error)
  }
}

// Clean up interval when component is unmounted
onBeforeUnmount(() => {
  if (countdownInterval.value) clearInterval(countdownInterval.value)
})

onMounted(() => {
  // Focus first input when modal opens
  inputRefs.value[0]?.focus()
})

const state = reactive({
  mobile_number: undefined,
})

const isMobileSubmitted = ref(false)
const mobileError = ref('')

const { items: usersData } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)


if (usersData?.value?.username) {
  if (/^\d+$/.test(usersData.value.username)) {
    // Username is exactly 11 digits
    state.mobile_number = usersData?.value?.username;
    FormPhoneNumber.value = usersData?.value?.username;
  } else {
    // Username is not a valid phone number format
    state.mobile_number = undefined;
    FormPhoneNumber.value = undefined;
  }
}

const schema = z.object({
  mobile_number: z.string()
  .min(11, t('Phone number must be at least 11 characters')),
})

type Schema = z.output<typeof schema>

async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true
  mobileError.value = ''
  try {
    const response = await $fetch(`/api/profile/update?username=${userData.value.username}`, {
      method: 'post',
      body: state
    }) as any

    if (!response.response.valid) {
      mobileError.value = response.response.message
      loading.value = false
      return
    }

    const { items: usersData } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
    localStorage.setItem("userData", JSON.stringify(usersData.value))
    const userDataString = localStorage.getItem('userData')
    userData.value = userDataString ? JSON.parse(userDataString) : null
    FormPhoneNumber.value = state.mobile_number
    isMobileSubmitted.value = true
  } catch (error) {
    console.error('Failed to submit mobile number:', error)
    mobileError.value = 'An error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

input {
  background: transparent;
}

.custom-color-for-modal .fixed.transition-opacity {
  background-color: #1D3C34D9 !important;
}
</style>