import OAuth from 'oauth-1.0a'
import crypto from 'crypto'

// Function to generate OAuth 1.0a headers
const getOAuthHeader = (url: string, method: string, consumerKey: string, consumerSecret: string) => {
  const oauth = new OAuth({
    consumer: { key: consumerKey, secret: consumerSecret },
    signature_method: 'HMAC-SHA1',
    hash_function(baseString: string, key: string) {
      return crypto.createHmac('sha1', key).update(baseString).digest('base64')
    },
  })

  const requestData = {
    url: url,
    method: method,
  }

  return oauth.toHeader(oauth.authorize(requestData))
}


export default defineEventHandler(async (event) => {
   try {
    const bodyy = await readBody(event)

    const queryId = event.context.params?.id

    const url = `${process.env.WC_API_URL}customers/${queryId}`
    const method = 'PUT'
    const consumerKey = 'ck_a0119e28ab6f69c8a08caf7ca0baf937d286b203'
    const consumerSecret = 'cs_2707cd204def923d883b869af77f330ab6c70da3'
    
    const headers = {
      ...getOAuthHeader(url, method, consumerKey, consumerSecret),
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

      const response = await $fetch(url, {
        method: method,
        body: bodyy,
        headers: headers,
      })
  
      const data = await response
      
      return data

  } catch (error) {
     console.log(error)
     return {
       error: (error as Error).message,
     }
  }
 })
