<template>
  <div v-if="!loadingInformation">
    <div class="bg-thirdColor border-b rtl:font-notoSans border-primary text-center py-3 font-bold font-secondaryFont text-[14px] text-primary uppercase md:hidden">
      {{ $t('Personal information') }}
    </div>
    <div class="md:px-[30px] md:max-w-[1200px] md:mx-auto md:mt-10">
      <div>
        <div class="grid md:grid-cols-2 gap-6 pb-10 md:border-b md:border-primary">
          <div class=" px-[30px] md:px-0">
            <div class="text-secondary font-bold font-secondaryFont text-[22px] rtl:font-notoSans uppercase hidden md:block">{{ $t('Personal information') }}</div>
            <div class="mt-7">
              <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
                <div class="w-full mx-auto flex flex-col gap-4">
                  <div class="relative text-field">
                    <UFormGroup name="first_name">
                      <input
                        class="w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                        variant="outline"
                        v-model="state.first_name"
                        required
                        placeholder=" "
                      >
                      <span
                        class="
                          uppercase
                          absolute
                          text-primary
                          font-primaryFont
                          font-semibold
                          opacity-1
                          font-base
                          z-[4]
                          rtl:font-notoSans
                          ltr:left-[22px]
                          rtl:right-[22px]
                          top-[13px]
                          transition-all
                          duration-100
                          !bg-white
                          ease-in-out"
                          :class="{
                            'placeholder-animation-with-bg !bg-white' : state.first_name,
                          }"
                        >
                          {{ $t('First Name') }}
                      </span>
                    </UFormGroup>
                  </div>
                  <div class="relative text-field">
                    <UFormGroup name="last_name">
                      <input
                        class="w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                        variant="outline"
                        v-model="state.last_name"
                        required
                        placeholder=" "
                      >
                      <span
                        class="
                          uppercase
                          absolute
                          text-primary
                          font-primaryFont
                          font-semibold
                          opacity-1
                          font-base
                          z-[4]
                          rtl:font-notoSans
                          ltr:left-[22px]
                          rtl:right-[22px]
                          top-[13px]
                          transition-all
                          duration-100
                          !bg-white
                          ease-in-out"
                          :class="{
                            'placeholder-animation-with-bg !bg-white' : state.last_name,
                          }"
                        >
                          {{ $t('Last Name') }}
                      </span>
                    </UFormGroup>
                  </div>
                  <div class="relative text-field">
                    <UFormGroup name="mobile_number">
                      <input
                        class="w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                        variant="outline"
                        v-model="state.mobile_number"
                        required
                        placeholder=" "
                      >
                      <span
                        class="
                          uppercase
                          absolute
                          text-primary
                          font-primaryFont
                          font-semibold
                          opacity-1
                          font-base
                          z-[4]
                          rtl:font-notoSans
                          ltr:left-[22px]
                          rtl:right-[22px]
                          top-[13px]
                          transition-all
                          duration-100
                          !bg-white
                          ease-in-out"
                          :class="{
                            'placeholder-animation-with-bg !bg-white' : state.mobile_number,
                          }"
                        >
                          {{ $t('number') }}
                      </span>
                    </UFormGroup>
                  </div>
                  <div class="relative text-field">
                    <UFormGroup name="email">
                      <input
                        class="w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                        variant="outline"
                        v-model="state.email"
                        required
                        placeholder=" "
                      >
                      <span
                        class="
                          uppercase
                          absolute
                          text-primary
                          font-primaryFont
                          font-semibold
                          opacity-1
                          font-base
                          z-[4]
                          rtl:font-notoSans
                          ltr:left-[22px]
                          rtl:right-[22px]
                          top-[13px]
                          transition-all
                          duration-100
                          !bg-white
                          ease-in-out"
                          :class="{
                            'placeholder-animation-with-bg !bg-white' : state.email,
                          }"
                        >
                          {{ $t('email') }}
                      </span>
                    </UFormGroup>
                  </div>
                  <div class="relative text-field variations-filter">
                    <UFormGroup name="city">
                      <div class="information">
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            font-primaryFont
                            font-semibold
                            opacity-1
                            md:text-base
                            text-[14px]
                            z-[4]
                            rtl:font-notoSans
                            ltr:left-[22px]
                            rtl:right-[22px]
                            md:top-[13px]
                            top-[11px]
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-without-bg"
                            :class="{
                              '!top-[-9px] bg-white md:!top-[-12px] !text-[12px] py-[1px] px-[5px]' : state.city,
                            }"
                          >
                          {{ $t('City') }}
                        </div>
                        <el-select
                          v-model="state.city"
                          placeholder=" "
                          style="width: 100%"
                        >
                          <el-option
                            v-for="item in cities?.response ?? []"
                            :key="item.name"
                            :label="item.name"
                            :value="item.name"
                          />
                        </el-select>
                      </div>
                    </UFormGroup>
                  </div>
                </div>
                <div class="flex justify-center">
                  <UButton
                    type="submit"
                    class="
                      font-thirdFont
                      font-bold
                      text-lg
                      text-thirdColor
                      py-2
                      px-20
                      md:mt-5
                      uppercase
                      mt-2
                      rounded-full
                      border
                      rtl:font-notoSans
                      md:border-orangeColor
                      md:bg-orangeColor
                      bg-primary
                      border-primary
                      hover:border-orangeColor
                      hover:bg-orangeColor
                      ease-in-out
                      duration-500
                    "
                    variant="solid"
                    :loading="loading"
                  >
                    <span v-if="!loading">{{ $t('UPDATE') }}</span>
                  </UButton>
                </div>
              </UForm>
            </div>
          </div>
          <ProfileShipping />
        </div>
      </div>
      <div class="grid md:grid-cols-2 gap-6 pb-10 md:border-b md:border-primary">
        <div v-if="!hideChangePassword">
          <ProfileChangePassword />
        </div>
        <div v-else>
          <div class="bg-thirdColor border-y border-primary text-center py-3 rtl:font-notoSans font-bold font-secondaryFont text-[14px] mt-10 text-primary uppercase md:hidden">
            {{ $t('ACCOUNT information') }}
          </div>
          <div class="text-secondary font-bold font-secondaryFont text-[22px] rtl:font-notoSans uppercase hidden md:block mt-10">{{ $t('ACCOUNT information') }}</div>
          <div class="px-[30px] md:px-0">
            <div class="border border-primary rounded-md p-7 mt-7">
              <div v-if="userData?.meta_data?.some((meta: any) => meta.key === 'usr_gauth')" class="text-primary font-primaryFont font-bold text-xl rtl:font-notoSans">{{ $t('GOOGLE ACCOUNT') }}</div>
              <div v-if="userData?.meta_data?.some((meta: any) => meta.key === 'usr_fbauth')" class="text-primary font-primaryFont font-bold text-xl rtl:font-notoSans">{{ $t('FACEBOOK ACCOUNT') }}</div>
              <div class="text-primary font-primaryFont font-semibold mt-1 rtl:font-notoSans">{{ locale === 'en' ? userData?.email : userData?.email_ar }}</div>
            </div>
          </div>
        </div>
        <div>
          <ProfileNotificationOptions />
        </div>
      </div>
    </div>
  </div>
  <div v-else class="text-center py-20 font-bold text-3xl text-orangeColor rtl:font-notoSans">
    {{ $t('Loading....') }}
  </div>
</template>
<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { ElNotification } from 'element-plus'

const userName = ref()
const userData: any = useState("userData", () => [])
const loading = ref(false)
const { locale, t } = useI18n()

const successPopUp = (message: string) => {
  ElNotification({
    // title: message,
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, message),
    // type: 'success',
    position: 'top-right',
  })
}

const errorPopUp = (message: string) => {
  ElNotification({
    message: h('i', { style: 'color: #FF0000; font-weight: bold;' }, message),
    position: 'top-right',
  })
}

const schema = z.object({
  first_name: z.string()
  .min(3, t('First Name must be at least 3 characters')),
  last_name: z.string()
  .min(3, t('Last Name must be at least 3 characters')),
  email: z.string()
  .min(4, t('Email must be at least 3 characters')),
  mobile_number: z.string()
  .min(11, t('Phone Number must be at least 11 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  first_name: undefined,
  last_name: undefined,
  mobile_number: undefined,
  email: undefined,
  city: undefined,
})

async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true
  try {
    const response = await $fetch(`/api/newBranch/profile/update?username=${userName.value}`, {
      method: 'post',
      body: state
    }) as any

    if (!response.response.valid) {
      throw new Error(response.response.message)
    }

    const { items: usersData } = await fetchHock(`newBranch/customer/${Number(userData?.value?.id)}`)
    localStorage.setItem("userData", JSON.stringify(usersData.value))
    const userDataString = localStorage.getItem('userData')
    userData.value = userDataString ? JSON.parse(userDataString) : null
    successPopUp(t('Profile updated successfully'))
  } catch (error: any) {
    errorPopUp(error.message)
  } finally {
    loading.value = false
  }
}

const profileData: any = useState("profileData", () => {})
const loadingInformation = ref(false)

const cities: any = useState("cities", () => {})

setTimeout(async () => {
  loadingInformation.value = true
  const { items } = await fetchHock(`newBranch/customer/${Number(userData?.value?.id)}`)
  const { items: city } = await fetchHock(`newBranch/cities`)
  cities.value = city.value
  profileData.value = items.value
  loadingInformation.value = false
}, 1)


watch(profileData, (current) => {
  if (current.id) {
    userName.value = current.username
    Object.assign(state, {
      first_name: current?.first_name,
      last_name: current?.last_name,
      email: current?.email,
      mobile_number: /^\d+$/.test(current?.username) ? current?.username : '',
      city: current?.shipping?.city,
    })
  }
})

const hideChangePassword = computed(() => {
  return userData?.value?.meta_data?.some((meta: any) => meta.key === 'usr_gauth' || meta.key === 'usr_fbauth');
});
</script>