// Get params to send request to filter and highlight selected bassed on query parameters
export function transformAndApplyQueryParamsMob(paramsString: any, additionalParam: any, posts_per_page: any) {
  // Get the router instance
  const router = useRouter()

  // Initialize the query object
  const queryObject: any = {}

  // Parse the main query string
  if (paramsString) {
    // Split the query string into individual key-value pairs
    const keyValuePairs = paramsString.split('&')

    // Transform the key-value pairs into an object
    keyValuePairs.forEach((pair: any) => {
      let [key, value] = pair.split('=')

      // Remove 'filter[' prefix and ']' suffix from the key
      if (key.startsWith('filter[') && key.endsWith(']')) {
        key = key.slice(7, -1); // Remove 'filter[' and ']'
      }

      // Decode the value to handle any URL-encoded characters
      value = decodeURIComponent(value)

      // Add the transformed key-value pair to the query object
      queryObject[key] = value
    })
  }

  // Include the additional parameter if it exists
  if (additionalParam) {
    const [key, value] = additionalParam.split('=')
    queryObject[key] = value
  }
  if (posts_per_page) {
    const [key, value] = posts_per_page.split('=')
    queryObject[key] = value
  }

  // Update the router with the transformed query parameters
  router.replace({ query: queryObject })
}