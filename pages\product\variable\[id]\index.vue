<template>
  <div>
    <div class="hidden md:block">
      <FiltersSingle />
    </div>
    <div class="md:mt-[60px]">
      <ClientOnly>
        <ProductsTire />
      </ClientOnly>
    </div>


    <div class="single-collapse md:hidden">
      <el-collapse v-model="activeNames">
        <!-- Brands -->
        <el-collapse-item name="SPECIFICATIONS">
          <template #title>
            <div class="flex justify-between items-center w-full collapse-title">
              <div class="font-secondaryFont font-bold text-primary text-[14px] rtl:font-notoSans">{{ $t('SPECIFICATIONS') }}</div>
              <div class="non-acitve">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-plus" class="text-[14px] text-primary" />
                </ClientOnly>
              </div>
              <div class="active--">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-xmark" class="text-[14px] text-primary" />
                </ClientOnly>
              </div>
            </div>
          </template>
          <div
              class="flex justify-between mt-6 flex-wrap pb-6 px-[30px]"
            >
              <div
                v-for="(meta, index) in product.metas ?? []"
                :key="index"
                class="
                  w-[48%]
                  h-full
                "
                :class="{'hidden': !meta?.Key}"
              >
                <UBadge
                  class="
                    flex
                    flex-col
                    items-start
                    justify-between
                    p-3
                    mb-2
                    text-primary
                    bg-thirdColor
                  "
                  v-if="meta?.value"
                >
                  <div class="font-bold text-[13px] uppercase rtl:font-notoSans" v-if="meta?.Key" v-html="locale === 'en' ? meta?.Key : meta?.Key_ar"></div>
                  <div class="font-bold text-base uppercase" v-if="meta?.Key" v-html="meta?.value"></div>
                </UBadge>
              </div>
            </div>
          

        </el-collapse-item>
        <el-collapse-item :name="`REVIEWS ${reviews.reviews.length}`">
          <template #title>
            <div class="flex justify-between items-center w-full collapse-title">
              <div class="font-secondaryFont font-bold text-primary text-[14px] rtl:font-notoSans">{{ $t('REVIEWS') }} {{ reviews.reviews.length }}</div>
              <div class="non-acitve">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-plus" class="text-[14px] text-primary" />
                </ClientOnly>
              </div>
              <div class="active--">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-xmark" class="text-[14px] text-primary" />
                </ClientOnly>
              </div>
            </div>
          </template>
          <div class="recentlySwiper pt-6 px-2">
            <Swiper
              @swiper="onSwiper"
              :modules="[ SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
              :loop="true"
              :space-between="20"
              :autoplay="{
                delay: 4000,
                disableOnInteraction: false,
              }"
              :breakpoints="{
                360: {
                    slidesPerView: 1.7,
                },
                640: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 2,
                },
              }"
            >
              <SwiperSlide v-for="(item, index) in reviews.reviews ?? []" :key="index">
                <div class="mx-auto">
                  <div
                    class="
                      rounded-md
                      py-3
                      px-3
                      bg-filter
                      overflow-hidden
                    "
                  >
                    <div class="flex flex-col font-medium text-primary text-base uppercase font-secondaryFont">
                      <div class="text-[14x] font-secondaryFont rtl:font-notoSans">{{ locale === 'en' ? item?.first_name : item?.first_name_ar }} {{ locale === 'en' ? item?.last_name : item?.last_name_ar }}</div>
                      <div class="text-[14x] font-secondaryFont rtl:font-notoSans">{{ locale === 'en' ? item?.date : item?.date_ar }}</div>
                    </div>
                    <div class="card-rating mt-2">
                      <el-rate :colors="	['#FF671F', '#FF671F', '#FF671F', '#FF671F', '#FF671F']"  v-model="item.rate" size="large" allow-half disabled />
                    </div>
                    <div>
                      <p class="text-primary text-[14px] font-bold rtl:font-notoSans">
                        {{ locale === 'en' ? item?.content : item?.content_ar }}
                      </p>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </el-collapse-item>
        <!-- <el-collapse-item name="MAKE COMPATIBILITY">
          <template #title>
            <div class="flex justify-between items-center w-full collapse-title">
              <div class="font-secondaryFont font-bold text-primary text-[14px]">MAKE COMPATIBILITY</div>
              <div class="non-acitve">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-plus" class="text-[14px] text-primary" />
                </ClientOnly>
              </div>
              <div class="active--">
                <ClientOnly>
                  <font-awesome-icon icon="fa-solid fa-xmark" class="text-[14px] text-primary" />
                </ClientOnly>
              </div>
            </div>
          </template>
          <div class="recentlySwiper pt-6 px-2">
            <Swiper
              @swiper="onSwiper"
              :modules="[ SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
              :loop="true"
              :space-between="20"
              :autoplay="{
                delay: 4000,
                disableOnInteraction: false,
              }"
              :breakpoints="{
                360: {
                    slidesPerView: 1.5,
                },
                640: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 2,
                },
              }"
            >
              <SwiperSlide v-for="(item, index) in itemsCompat" :key="index">
                <div class="mx-auto w-full">
                  <div class="">
                    <UBadge class="py-6 px-4 justify-center font-thirdFont items-center text-[14px] font-semibold text-thirdColor bg-secondary w-full">
                      {{ item.name }}
                    </UBadge>
                  </div>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </el-collapse-item> -->
      </el-collapse>
      <div class="px-[20px] max-w-[1280px] mx-auto mt-5">
        <NuxtLink :to="localePath(`/category/${product?.category?.id}?brand=${product?.brand_id}`)">
          <div class="flex items-center gap-4 w-full bg-thirdColor border border-primary rounded-lg p-4">
            <img class="w-[60px] h-[60px] rounded-full border border-primary" :src="product?.brand_logo" :alt="locale === 'en' ? product?.brand_name : product?.name_ar" />
            <div class="">
              <div class="font-secondaryFont text-[14px] font-bold text-primary rtl:font-notoSans">
                {{ locale === 'en' ? product?.brand_name : product?.brand_name_ar }}
              </div>
              <div class="font-primaryFont text-[12px] text-primary rtl:font-notoSans">
                {{ $t('Explore Brand') }}
              </div>
            </div>
          </div>
        </NuxtLink>
        <NuxtLink :to="localePath(`/category/${product?.category?.id}?${product?.attributes?.filter(attr => ['pa_width', 'pa_height', 'pa_rim', 'pa_tyre-type'].includes(attr.key))?.map(attr => `${attr.key}=${attr.value}`).join('&')}`)">
          <div class="flex items-center gap-4 w-full mt-4 bg-thirdColor border border-primary rounded-lg p-4">
            <img class="w-[60px] h-[60px]" :src="product?.category?.icon" :alt="locale === 'en' ? product?.category?.name : product?.category?.name_ar" />
            <div class="">
              <div class="font-secondaryFont text-[14px] font-bold text-primar rtl:font-notoSansy">
                {{ locale === 'en' ? product?.category?.name : product?.category?.name_ar }}
              </div>
              <div class="font-primaryFont text-[12px] text-primary rtl:font-notoSans">
                {{ $t('Same Size Picks.') }}
              </div>
            </div>
          </div>
        </NuxtLink>
      </div>
    </div>

    <div class="mt-[60px] md:block hidden">
      <ReviewsSingle />
    </div>
    <div class="mt-[60px] md:block hidden">
      <SlidersProduct :products="relatedProducts" :title="$t('RELATED PRODUCTS')" />
    </div>
    <!-- <div class="mt-[60px] md:block hidden">
      <SlidersCompat />
    </div> -->
    <!-- <div class="mt-[40px] md:mt-[60px]">
      <SlidersAdverAds />
    </div> -->
    <div class="mt-[40px] md:mt-[60px] mb-[60px]">
      <SlidersLogosHome
        :logos="brands.brands"
        :title="$t('OTHER BRANDS')"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { locale, t } = useI18n()
const localePath = useLocalePath()

// Remove query parameters on page load
onMounted(() => {
  if (Object.keys(route.query).length > 0) {
    router.replace({ 
      path: route.path,
      query: {} 
    })
  }
})

// Get Product
const product: any = useState("product", () => {})
const categoryId: any = useState("categoryId", () => [])
const categoryName: any = useState("categoryName", () => '')
const { items: productData } = await fetchHock(`newBranch/product/${Number(route.params.id)}`)
product.value = productData.value

categoryId.value = productData?.value?.category?.id
categoryName.value = locale.value === 'en' ? productData?.value?.category?.name : productData?.value?.category?.name_ar

// Get Brands
const brands: any = useState("brands", () => [])
const { items: brandss } = await fetchHock(`newBranch/brands/0`)
brands.value = brandss?.value

// Get Products
const relatedProducts = ref()
const { items } = await fetchHock(`newBranch/products/${categoryId.value}`)
relatedProducts.value = items?.value?.products

// Get Reviews
const reviews: any = useState('reviews', () => [])
const { items: reviewsData } = await fetchHock(`newBranch/productsreviews/${route.params.id}`)
reviews.value = reviewsData?.value

// Collapse Logic
const activeNames = ref(['1'])

// Swipper settings
const swiperInstance = ref()
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.recentlySwiper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', onMouseOver)
    slide.addEventListener('mouseleave', onMouseLeave)
    slide.addEventListener('touchstart', onMouseOver)
    slide.addEventListener('touchend', onMouseLeave)
  })
})

function onMouseOver(event: any) {
  swiperInstance.value.autoplay.stop()
}

function onMouseLeave(event: any) {
  swiperInstance.value.autoplay.start()
}
</script>
<style>
.single-collapse {
  margin-top: 20px;
}
.single-collapse .el-collapse-item__header,
.single-collapse .el-collapse {
  border-color: #1D3C34;
}
.single-collapse .el-collapse-item__header {
  padding: 0 30px;
  height: 40px;
}
.single-collapse .el-icon.el-collapse-item__arrow {
  display: none;
}
.single-collapse .el-collapse-item__header .non-acitve, 
.single-collapse .el-collapse-item__header.is-active .active--{
  display: block;
}
.single-collapse .el-collapse-item__header .active--,
.single-collapse .el-collapse-item__header.is-active .non-acitve {
  display: none;
}
.single-collapse .el-collapse-item__header.is-active .collapse-title * {
  color: #FF671F;
}
.single-collapse .el-collapse-item__wrap {
border-bottom-color: #1D3C34;
}
.single-collapse .el-collapse-item__header.is-active {
  border-bottom-color: transparent;
}
/* border-bottom-color */
</style>