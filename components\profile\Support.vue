<template>
  <div class="relative">
    <div v-if="!loadingSupport">
      <div class="relative">
        <div class="px-[20px] max-w-[1280px] mx-auto" v-if="!loadingSupport">
          <div class="md:w-[70%] mx-auto">
            <div class="font-primaryFont rtl:font-notoSans text-lg text-center text-primary mb-[60px] md:block hidden mt-10">
              {{ $t("If you're having any issues with your order or have a general inquiry, feel free to submit a new") }} <span class="font-semibold">{{ $t('Support Ticket') }}</span>. {{ $t("This helps document your concern, ensuring it's carefully addressed by our support team.") }} 
            </div>
            <div class="hidden md:block">
              <div class="custom-text-input-responsive">
                <UForm ref="form" :schema="schema" :state="state" class="md:space-y-4" @submit="onSubmit">
                  <div>
                    <UFormGroup name="inquiry_id">
                      <label class="custom-field-select one relative variations-filter">
                        <el-select
                          v-model="state.inquiry_id"
                          size="large"
                        >
                          <el-option
                            v-for="item in itemss?.inquiries ?? []"
                            :key="item?.term_id"
                            :label="item?.name"
                            :value="item?.term_id"
                          />
                        </el-select>
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            font-primaryFont
                            font-semibold
                            opacity-1
                            font-base
                            z-[4]
                            placeholder
                            rtl:font-notoSans
                            ltr:left-[15px]
                            rtl:right-[15px]
                            top-[13px]
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-without-bg"
                            :class="{
                              'placeholder-animation-without-bg bg-white' : state.inquiry_id,
                            }"
                          >
                            {{ $t('INQUIRY') }}
                          </div>
                      </label>
                    </UFormGroup>
                  </div>
      
                  <div class="custom-text-input">
                    <UFormGroup name="order_id">
                      <label class="custom-field-select relative variations-filter">
                        <el-select
                          v-model="state.order_id"
                          size="large"
                        >
                          <el-option
                            v-for="item in itemss?.orders ?? []"
                            :key="item"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            font-primaryFont
                            font-semibold
                            opacity-1
                            font-base
                            z-[4]
                            rtl:font-notoSans
                            ltr:left-[15px]
                            rtl:right-[15px]
                            top-[13px]
                            placeholder
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-without-bg"
                            :class="{
                              'placeholder-animation-without-bg bg-white' : state.order_id,
                            }"
                          >
                            {{ $t('Your complaint order') }}
                          </div>
                      </label>              
                    </UFormGroup>
                  </div>
      
                  <div class="custom-text-input">
                    <UFormGroup name="description">
                      <label class="custom-field-select relative variations-filter">
                        <textarea
                          v-model="state.description"
                          class="
                            text-lg
                            w-full
                            text-primary
                            font-bold
                            font-secondaryFont
                            py-2
                            px-4
                            border-2
                            border-primary
                            rounded-lg
                            focus:outline-none
                            min-h-[150px]
                          "
                          id="w3review"
                          name="w3review"
                        ></textarea>
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            font-primaryFont
                            font-semibold
                            placeholder
                            opacity-1
                            font-base
                            z-[4]
                            rtl:font-notoSans
                            ltr:left-[22px]
                            rtl:right-[22px]
                            top-[13px]
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-mob"
                            :class="{
                              'placeholder-animation-mob' : state.description,
                            }"
                          >
                            {{ $t('brief about the problem') }}
                          </div>
                      </label>
                    </UFormGroup>
                  </div>
                  <div class="flex justify-center">
                    <UButton
                      type="submit"
                      class="
                        font-thirdFont
                        font-bold
                        text-lg
                        text-thirdColor
                        py-2
                        px-32
                        mt-5
                        rtl:font-notoSans
                        uppercase
                        rounded-full
                        border
                        border-orangeColor
                        bg-orangeColor
                        hover:border-orangeColor
                        hover:bg-orangeColor
                        ease-in-out
                        duration-500
                      "
                      variant="solid"
                    >
                      <span>{{ $t('SUBMIT') }}</span>
                    </UButton>
                  </div>
                </UForm>
              </div>
            </div>
            <div
              class="md:hidden mb-[150px]"
              :class="{'hidden': !switchForm}"
            >
            <div class="font-secondaryFont text-[15px] rtl:font-notoSans text-center text-primary mb-[20px] md:hidden mt-6">
              {{ $t("If you're having any issues with your order or have a general inquiry, feel free to submit  a new Support Ticket.") }} 
            </div>
              <div class="custom-text-input-responsive">
                <UForm ref="form" :schema="schema" :state="state" class="md:space-y-4" @submit="onSubmit">
                  <div >
                    <UFormGroup name="inquiry_id">
                      <label class="custom-field-select one relative variations-filter">
                        <el-select
                          v-model="state.inquiry_id"
                          size="large"
                        >
                          <el-option
                            v-for="item in itemss?.inquiries ?? []"
                            :key="item?.term_id"
                            :label="item?.name"
                            :value="item?.term_id"
                          />
                        </el-select>
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            font-primaryFont
                            font-semibold
                            opacity-1
                            font-base
                            z-[4]
                            rtl:font-notoSans
                            ltr:left-[22px]
                            rtl:right-[22px]
                            placeholder
                            top-[10px]
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-without-bg"
                            :class="{
                              'placeholder-animation-with-bg-white-mob' : state.inquiry_id,
                            }"
                          >
                            {{ $t('INQUIRY') }}
                          </div>
                      </label>
                    </UFormGroup>
                  </div>
      
                  <div>
                    <UFormGroup name="order_id">
                      <label class="custom-field-select variations-filter relative mt-2">
                        <el-select
                          v-model="state.order_id"
                          size="large"
                        >
                          <el-option
                            v-for="item in itemss?.orders ?? []"
                            :key="item"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            font-primaryFont
                            font-semibold
                            opacity-1
                            font-base
                            z-[4]
                            rtl:font-notoSans
                            ltr:left-[22px]
                            rtl:right-[22px]
                            placeholder
                            top-[10px]
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-without-bg"
                            :class="{
                              'placeholder-animation-with-bg-white-mob' : state.order_id,
                            }"
                          >
                            {{ $t('Your complaint order') }}
                          </div>
                      </label>              
                    </UFormGroup>
                  </div>
      
                  <div>
                    <UFormGroup name="description">
                      <label class="custom-field-select relative variations-filter mt-2">
                        <textarea
                          v-model="state.description"
                          class="
                            text-lg
                            w-full
                            text-primary
                            font-bold
                            font-secondaryFont
                            py-8
                            px-4
                            border-2
                            border-primary
                            rounded-lg
                            focus:outline-none
                            min-h-[150px]
                          "
                          id="w3review"
                          name="w3review"
                        ></textarea>
                        <div
                          class="
                            uppercase
                            absolute
                            text-primary
                            placeholder
                            font-primaryFont
                            font-semibold
                            opacity-1
                            font-base
                            z-[4]
                            rtl:font-notoSans
                            ltr:left-[22px]
                            rtl:right-[22px]
                            top-[13px]
                            transition-all
                            duration-100
                            ease-in-out
                            focus:placeholder-animation-with-bg-white-mob"
                            :class="{
                              'placeholder-animation-with-bg-white-mob' : state.description,
                            }"
                          >
                            {{ $t('brief about the problem') }}
                          </div>
                      </label>
                    </UFormGroup>
                  </div>
                  <div class="flex justify-center">
                    <UButton
                      type="submit"
                      class="
                        font-thirdFont
                        font-bold
                        text-lg
                        text-thirdColor
                        py-2
                        px-20
                        rtl:font-notoSans
                        rounded-full
                        uppercase
                        border
                        hover:border-orangeColor
                        hover:bg-orangeColor
                        ease-in-out
                        duration-500
                      "
                      variant="solid"
                    >
                      <span>{{ $t('SUBMIT') }}</span>
                    </UButton>
                  </div>
                </UForm>
              </div>
            </div>
          </div>
  
  
  
  
          <div class="border-t border-primary mt-10 pt-10 hidden md:block">
            <div class="flex justify-between items-center mb-10">
              <div class="text-secondary rtl:font-notoSans font-bold font-secondaryFont text-[22px] uppercase">
                {{ $t('my previous support tickets') }}
              </div>
              <div class="support-select-status variations-filter relative">
                <div
                  class="
                  uppercase
                  absolute
                  text-primary
                  font-primaryFont
                  font-semibold
                  opacity-1
                  font-base
                  z-[4]
                  rtl:font-notoSans
                  ltr:left-[15px]
                  rtl:right-[15px]
                  top-[11px]
                  transition-all
                  duration-100
                  ease-in-out
                  focus:placeholder-animation-with-bg"
                  :class="{
                    'placeholder-animation-with-bg' : statusSelected,
                  }"
                >
                  {{ $t('STATUS') }}
                </div>
                <el-select
                  v-model="statusSelected"
                  placeholder=" "
                  size="large"
                  @change="statusChanged"
                >
                  <el-option
                    label="All"
                    value=""
                  />
                  <el-option
                    v-for="item in statuses ?? []"
                    :key="item.term_id"
                    :label="item.term_name"
                    :value="item.term_id"
                  />
                </el-select>
              </div>
            </div>
            <div class="faq-content-container mb-20 pb-20">
              <div v-if="!tickets" class="text-primary rtl:font-notoSans font-bold text-3xl text-center font-primaryFont mt-14">
                {{ $t('No Tickets Found') }}
              </div>
              <el-collapse v-model="activeItems" accordion>
                <el-collapse-item 
                  v-for="(item, index) in tickets"
                  :key="index"
                  :title="item.title"
                  :name="item.id"
                >
                  <template #title>
                    <span class="flex justify-between w-full">
                      <span>{{ item.title }}</span>
                      <span
                        class="text-primary text-base font-bold flex items-center gap-2 uppercase"
                        :class="{
                          '!text-orangeColor' : item.status === 'Investigating'
                        }"
                      >
                        {{ item.status }}
                        <ClientOnly>
                          <font-awesome-icon :icon="`fa-solid ${item.status_icon}`" />
                        </ClientOnly>
                      </span>
                    </span>
                  </template>
                  <div class="text-[15px] flex items-center justify-between">
                    <div class="y-[20px] pe-10 text-primary font-secondaryFont text-lg">{{ item.content }}</div>
                    <div class="py-[20px] ps-10 w-[40%] border-l border-primary font-secondaryFont text-primary">
                      <span class="mb-2 block">
                        <div class="text-[15px] rtl:font-notoSans">{{ $t('INQUIRY') }}</div>
                        <div class="text-xl font-bold font-primaryFont">{{ item.inquiry }}</div>
                      </span>  
                      <span class="mb-2 block">
                        <div class="text-[15px] rtl:font-notoSans">{{ $t('order') }}</div>
                        <div class="text-xl font-bold font-primaryFont">#{{ item.order_id }}</div>
                      </span>  
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
  
  
  
        <div
          class="md:hidden"
          :class="{'hidden': switchForm}"
        >
          <div class="flex justify-between items-center mb-10 bg-thirdColor border-b border-primary px-[30px] py-3">
            <div class="text-primary rtl:font-notoSans font-bold font-secondaryFont text-[15px] uppercase">
              {{ $t('My Tickets') }} ({{ tickets?.length }})
            </div>
            <div class="support-select-status select-status-responsive variations-filter relative">
              <div
                class="
                  uppercase
                  absolute
                  text-thirdColor
                  font-primaryFont
                  font-semibold
                  opacity-1
                  font-base
                  z-[4]
                  rtl:font-notoSans
                  ltr:left-[22px]
                  rtl:right-[22px]
                  top-[10px]
                  transition-all
                  duration-100
                  ease-in-out
                  focus:placeholder-animation-with-bg"
                  :class="{
                    'placeholder-animation-with-bg !bg-orangeColor !border-thirdColor' : statusSelected,
                  }"
                >
                  {{ $t('STATUS') }}
                </div>
              <el-select
                v-model="statusSelected"
                placeholder=" "
                size="large"
                @change="statusChanged"
              >
                <el-option
                  label="All"
                  value=""
                />
                <el-option
                  v-for="item in statuses ?? []"
                  :key="item.term_id"
                  :label="item.term_name"
                  :value="item.term_id"
                />
              </el-select>
            </div>
          </div>
          <div class="faq-content-container mb-20 pb-20 px-[30px]">
            <div v-if="!tickets" class="text-primary rtl:font-notoSans font-bold text-3xl text-center font-primaryFont mt-10">
              {{ $t('No Tickets Found') }}
            </div>
            <el-collapse v-model="activeItems" accordion>
              <el-collapse-item 
                v-for="(item, index) in tickets"
                :key="index"
                :title="item.title"
                :name="item.id"
              >
                <template #title>
                  <div class="flex flex-col w-full">
                    <span class="flex justify-between w-full p-2 px-[20px]">
                      <span class="font-semibold font-primaryFont text-[14px] text-primary">{{ item.title }}</span>
                    </span>
                    <div
                      class="
                        flex justify-between items-center
                        bg-secondary
                        px-[20px]
                        border-b
                        border-primary
                        text-thirdColor
                        py-1
                        font-secondaryFont"
                      >
                      <div class="text-[12px] rtl:font-notoSans">{{ $t('Status') }}</div>
                      <span class="font-bold text-[13px]">
                        {{ item.status }}
                        <ClientOnly>
                          <font-awesome-icon :icon="`fa-solid ${item.status_icon}`" />
                        </ClientOnly>
                      </span>
                    </div>
                    <div class="text-[15px] flex items-center justify-between px-[20px]">
                      <span class="mb-2 block">
                        <div class="text-[12px] rtl:font-notoSans">{{ $t('INQUIRY') }}</div>
                        <div class="text-[14px] font-bold font-primaryFont">{{ item.inquiry }}</div>
                      </span>
                    <div class="py-[20px] w-[40%] border-l border-primary font-secondaryFont text-primary">
                      <span class="mb-2 block text-center">
                        <div class="text-[12px] rtl:font-notoSans">{{ $t('order') }}</div>
                        <div class="text-[14px] font-bold font-primaryFont">#{{ item.order_id }}</div>
                      </span>  
                    </div>
                  </div>
                  </div>
                </template>
                <div>
                  <div class="flex items-center justify-between">
                    <div class="py-[20px] text-primary font-secondaryFont rtl:font-notoSans">{{ item.content }}</div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
  
  
  
  
  
        

      </div>
    </div>
    <div v-else class="text-center rtl:font-notoSans py-20 font-bold text-3xl text-orangeColor">
      {{ $t('Loading....') }}
    </div>
    <div class="bg-thirdColor fixed bottom-[56px] left-0 w-full py-4 text-center md:hidden">
      <div class="font-secondaryFont text-[14px] text-primary rtl:font-notoSans">{{ $t('History of tickets') }}</div>
      <div class="flex justify-center px-[30px]">
        <UButton
          class="
            font-thirdFont
            font-bold
            text-[15px]
            text-thirdColor
            py-2
            mt-2
            uppercase
            w-full
            rounded-full
            border
            rtl:font-notoSans
            border-orangeColor
            bg-orangeColor
            hover:border-orangeColor
            hover:bg-orangeColor
            ease-in-out
            duration-500
            flex items-center justify-center
          "
          variant="solid"
          @click="switchForm = !switchForm"
        >
          <span v-if="!switchForm">{{ $t('CREATE SUPPORT TICKET') }}</span>
          <span v-else>{{ $t('MY SUPPORT TICKETS') }}</span>
        </UButton>
      </div>
    </div>
  </div>

</template>
<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { ElNotification } from 'element-plus'

const statusSelected = ref()
const profileData: any = useState("profileData", () => {})
const userData: any = useState("userData", () => [])

const { locale, t } = useI18n()

const loadingSupport = ref(false)

const activeItems = ref<number | null>(1)

const itemss = ref()
const tickets = ref()
const statuses = ref()

setTimeout(async () => {
  loadingSupport.value = true

  const { items: itemsData } = await fetchHock(`newBranch/support/getSupportData/${Number(userData?.value?.id)}`)
  itemss.value = itemsData.value

  const { items: status } = await fetchHock(`newBranch/support/getStatus`)
  statuses.value = status.value

  const { items: ticketss } = await fetchHock(`newBranch/support/getTickets?user_id=${Number(userData?.value?.id)}`)

  tickets.value = ticketss?.value?.posts

  loadingSupport.value = false
}, 1)

const statusChanged = async (val: number)  => {
  const { items: ticketss } = await fetchHock(`newBranch/support/getTickets?user_id=${Number(userData?.value?.id)}&status_id=${Number(val)}`)
  tickets.value = ticketss?.value?.posts
}

const schema = z.object({
  inquiry_id: z.number(),
  order_id: z.number(),
  description: z.string()
  .min(6, t('Description must be at least 6 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  inquiry_id: undefined,
  order_id: undefined,
  user_id: profileData?.value?.id,
  description: undefined,
})

const popUpMessage = ref()
const successPopUp = () => {
  ElNotification({
    // title: popUpMessage.value,
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, popUpMessage.value),
    // type: 'success',
    position: 'top-right',
  })
}

const loading = ref(false)

async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true
  // // Do something with event.data
  const response = await $fetch(`/api/newBranch/support/sendSupport`, {
    method: 'post',
    body: state
  }) as any
  popUpMessage.value = response
  const { items: ticketss } = await fetchHock(`newBranch/support/getTickets?user_id=${Number(userData?.value?.id)}`)
  tickets.value = ticketss?.value?.posts
  successPopUp()
  state.inquiry_id = undefined
  state.order_id = undefined
  state.description = undefined
  loading.value = false
}

// Responsive logic
const switchForm = ref(false)
</script>

<style>
.support-select-status {
  width: 200px;
}
.support-select-status .el-select__wrapper {
  border: 1px solid #263238 !important;
  background-color: #E9EAC8 !important;
}
.support-select-status .el-select__wrapper .el-select__selected-item.el-select__placeholder {
  font-style: italic;
  font-weight: 600;
  font-size: 16px;
  color: #1D3C34;
  font-family: "Montserrat", sans-serif !important;
}
@media (max-width: 768px) {
  .select-status-responsive.support-select-status {
    width: 130px !important;
  }
  .select-status-responsive.support-select-status .el-select__wrapper {
    background-color: #FF671F !important;
    color: #E9EAC8 !important;
    border: none !important;
    border-radius: 12312312px;
  }
  .select-status-responsive.support-select-status .el-select__wrapper .el-select__selected-item.el-select__placeholder {
    color: #E9EAC8 !important;
    font-weight: bold !important;
    font-style: normal !important;
    font-size: 12px !important;
  }
  .select-status-responsive.support-select-status .el-select__caret {
    color: #E9EAC8 !important;
  }
  .faq-content-container .el-collapse-item__header {
    padding: 0 !important;
    overflow: hidden;
  }
}
</style>