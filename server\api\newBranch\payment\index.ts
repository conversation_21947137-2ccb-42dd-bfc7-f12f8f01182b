export default defineEventHandler(async (event) => {
  try {
    const queryObject = getQuery(event)

    const queryString = Object.entries(queryObject).map(([key, value]) => {
      // Check if the value is not undefined, not null, and not an array
      if (value!== undefined && value!== null &&!Array.isArray(value)) {
        // If the value is defined, encoded, and not an array, encode both the key and the value
        // Convert value to a string if it's an object
        const stringValue = typeof value === 'object'? JSON.stringify(value) : value;
        return `${encodeURIComponent(key)}=${encodeURIComponent(stringValue)}`;
      }
      // If the value is undefined, null, or an array, return an empty string
      return '';
    }).filter(Boolean).join('&'); // Filter out empty strings and join the array of strings into a single string with '&' as the separator


    const url = `${process.env.WC_API_URL}geidea_checkout_session?${queryString}`
    const method = 'GET'
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json',
    }

    const response = await $fetch(url, {
      method,
      headers,
    })

    return response
  } catch (error: any) {
    console.error('Error in Geidea checkout session:', error?.data)
    return {
      error: error?.data,
      statusCode: error?.statusCode,
    }
  }
}) 