/** @type {import('tailwindcss').Config} */

module.exports = {
  content: [],
  theme: {
    extend: {
      colors: {
        'te-papa-green': {
          DEFAULT: '#1D3C34',
          50: '#5FB29C',
          100: '#52AA93',
          200: '#458F7C',
          300: '#387364',
          400: '#2A584C',
          500: '#1D3C34',
          600: '#0B1613',
          700: '#000000',
          800: '#000000',
          900: '#000000',
          950: '#000000'
        },        
        secondary: "#4B9560",
        thirdColor: "#E9EAC8",
        orangeColor: "#FF671F",
        filter: "#F0F0F0",
        textLight: "#1D3C34",
        cartColor: "#F5F5F5",

        // headerBg: "#37474F",
        // cyan: "#CFD8DC",
        // subRed: "#FF0202",
        // subGrey: "#67737A",
        // subYellow: "#D69F5A",
        // facebook: "#4267B2",
        // singleBadge: "#DFE8EC",
        // lightColor: "#EFEFEF",
        // vendorColor: "#429DD5",
      },
      fontFamily: {
        primaryFont: ["Montserrat", "sans-serif"],
        secondaryFont: ["Oxygen", "sans-serif"],
        thirdFont: ["Roboto", "sans-serif"],
        notoSans: ["Noto Sans", "sans-serif"],
      },
    },
  },
  plugins: [],
};
