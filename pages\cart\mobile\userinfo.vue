<template>
  <div>
    <div class="px-[30px]">
      <h2 class="font-bold font-secondaryFont text-primary text-[15px] text-center mt-5 rtl:font-notoSans">{{ $t('Existing customer or new registration?') }}</h2>
      <div class="text-primary text-[11px] font-secondaryFont text-center mt-1 rtl:font-notoSans">
        {{ $t('If you have an account, please use on of the below methods in order to log-in.') }}
      </div>
      <div class="bg-primary flex items-center justify-between mt-5 rounded-full">
        <div class="w-[50%]">
          <NuxtLink :to="localePath('/cart/mobile/login')" class="text-thirdColor rtl:font-notoSans font-bold text-[15px] font-thirdFont block text-center py-2">
            {{ $t('SIGN IN') }}
          </NuxtLink>
        </div>
        <div class="text-thirdColor">|</div>
        <div class="w-[50%]">
          <NuxtLink :to="localePath('/cart/mobile/register')" class="text-thirdColor rtl:font-notoSans font-bold text-[15px] font-thirdFont block text-center py-2">
            {{ $t('REGISTER') }}
          </NuxtLink>
        </div>
      </div>
    </div>
    <div class="border-b border-primary pb-4">
      <div class="flex justify-between items-center px-[30px] mt-4">
        <div class="font-bold text-[12px] font-secondaryFont rtl:font-notoSans">{{ $t('Quick Sign-in / Registration') }}</div>
        <AuthSocialLoginCart v-if="!socialLoading" />
        <div v-else class="flex justify-center items-center">
          <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-primary"/>
        </div>
      </div>
    </div>
    <div class="px-[30px] pt-4">
      <h3 class="font-bold text-[15px] font-secondaryFont text-center text-primary rtl:font-notoSans">{{ $t('Guest Check-out') }}</h3>
      <h3 class="text-red-700 font-primaryFont rtl:font-notoSans" v-if="serverErrors">{{ $t('Something wrong') }}</h3>
      <div class="mt-5">
        <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
          <div class="w-full mx-auto flex flex-col gap-4 custom-form-responsive-info">
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="first_name">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.first_name"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    text-[14px]
                    rtl:font-notoSans
                    ltr:left-[25px]
                    rtl:right-[25px]
                    top-[10px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.first_name,
                    }"
                  >
                    {{ $t('First Name') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="last_name">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.last_name"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    text-[14px]
                    rtl:font-notoSans
                    ltr:left-[25px]
                    rtl:right-[25px]
                    top-[10px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.last_name,
                    }"
                  >
                    {{ $t('Last Name') }}
                </span>
              </UFormGroup>
            </div>
            <div class="relative text-field-nobg-mob">
              <UFormGroup name="username">
                <input
                  class="w-full border border-primary py-2 px-5 rounded-full focus:outline-0"
                  variant="outline"
                  v-model="state.mobile_number"
                  required
                  placeholder=" "
                >
                <span
                  class="
                    uppercase
                    absolute
                    text-primary
                    font-primaryFont
                    font-semibold
                    opacity-1
                    font-base
                    z-[4]
                    text-[14px]
                    rtl:font-notoSans
                    ltr:left-[25px]
                    rtl:right-[25px]
                    top-[10px]
                    transition-all
                    duration-100
                    ease-in-out"
                    :class="{
                      'placeholder-animation-mob' : state.mobile_number,
                    }"
                  >
                    {{ $t('Phone') }}
                </span>
              </UFormGroup>
            </div>
          </div>
          <div class="text-center mt-6 !mb-[80px]">
            <UButton
              type="submit"
              class="
                text-thirdColor
                font-bold
                font-thirdFont
                rounded-full
                text-[15px]
                border
                border-primary
                uppercase
                bg-primary
                px-20
                py-2
                mt-2
                rtl:font-notoSans
                shadow-unset
                hover:scale-[1.1]
                ease-in-out
                duration-500
              "
              variant="solid"
              :loading="loading"
            >
              <div v-if="!loading">{{ $t('SUBMIT') }}</div>
            </UButton>
          </div>
        </UForm>
      </div>
    </div>
    <div class="px-[30px] bg-primary pb-[20px] pt-3 fixed w-full bottom-0 z-[123123123] md:hidden">
      <div class="font-bold text-thirdColor text-[12px] font-secondaryFont text-center mb-2 rtl:font-notoSans flex justify-center gap-1 items-center">
        <span class="rtl:mt-0.5">{{ $t('YOUR CART') }} ({{ totalItemss || '0' }})</span> |
        <span class="rtl:flex rtl:gap-2 rtl:flex-row-reverse">
          <span>{{ $t('EGP') }}</span> <span class="rtl:font-secondaryFont">{{ formatPrice(Number(cartItems?.single?.[0]?.total)) }}</span>
        </span>
      </div>
      <div class="flex justify-center">
        <UButton
          v-if="dataDone"
          class="
            rounded-full
            flex
            items-center
            justify-center
            font-bold
            text-[15px]
            w-full
            py-2
            font-thirdFont
            text-thirdColor
            uppercase
            bg-orangeColor
            border-2
            border-orangeColor
            rtl:font-notoSans
            transition-all
            duration-500
            ease-in-out"
            :disabled="!dataDone"
            :to="localePath('/cart/mobile/address')"
            >
            <!-- @click="mobileBuyOrder" -->
          {{ $t('CHECKOUT') }}
        </UButton>
        <UButton
          v-else
          class="
            rounded-full
            flex
            items-center
            rtl:font-notoSans
            justify-center
            font-bold
            text-[15px]
            w-full
            py-2
            font-thirdFont
            text-thirdColor
            uppercase
            bg-orangeColor
            border-2
            border-orangeColor
            disabled:bg-orangeColor
            transition-all
            duration-500
            ease-in-out"
            :disabled="!dataDone"
            >
            <!-- @click="mobileBuyOrder" -->
          {{ $t('CHECKOUT') }}
        </UButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
const router = useRouter()

const loading = ref(false)
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => {})
const socialLoading: any = useState("socialLoading", () => false)
const FormPhoneNumber = useState<any>("FormPhoneNumber", () => "")
const { t } = useI18n()
const localePath = useLocalePath()

const schema = z.object({
  first_name: z.string()
  .min(3, t('First Name must be at least 3 characters')),
  last_name: z.string()
  .min(3, t('Last Name must be at least 3 characters')),
  mobile_number: z.string()
  .min(11, t('Phone Number must be at least 11 characters')),
})

type Schema = z.output<typeof schema>

const state = reactive({
  first_name: undefined,
  last_name: undefined,
  mobile_number: undefined,
})

const serverErrors = ref('')
const dataDone = ref(false)

async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true
  // Do something with event.data
  const response = await $fetch(`/api/profile/update?username=${userData?.value?.username}`, {
    method: 'post',
    body: state
  }) as any

  // if (response.response.valid) {
    const { items: usersData } = await fetchHock(`shipping/${Number(userData?.value?.id)}`)
    localStorage.setItem("visitor_data", JSON.stringify(usersData.value))
    const userDataString = localStorage.getItem('visitor_data')
    userData.value = userDataString ? JSON.parse(userDataString) : null
    dataDone.value = true
  // } else {
  //   serverErrors.value = response.response.message
  // }

  loading.value = false
  router.push(localePath('/cart/mobile/address'))
}

const totalItemss = ref(0)

totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
  return acc + Number(item.quantity || 0)
}, 0)

watch(cartItems, async (current) => {
  if (cartItems?.value?.single?.[0]?.items?.length) {
    totalItemss.value = cartItems?.value?.single?.[0]?.items?.reduce((acc: number, item: any) => {
      return acc + Number(item.quantity || 0)
    }, 0)
  }
})
</script>