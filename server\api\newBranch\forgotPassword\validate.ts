export default defineEventHandler(async (event) => {
   try {
    const bodyy = await readBody(event)
    const url = `${process.env.WP_API_URL}reset_password_code`
    const method = 'post'
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

    const response = await $fetch(url, {
      method: method,
      body: bodyy,
      headers: headers,
    })
    
    return response
  } catch (error: any) {
    // Handle 404 and other errors
    if (error.statusCode === 404 || error.status === 404) {
      return {
        error: "User not found."
      }
    }

    // Handle other types of errors
    return {
      error: error.message || "An error occurred while validating the phone number."
    }
  }
})
