<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="hidden md:block">
        <div class="relative sellerSwiper">
          <div
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start"
            v-if="!loadingCategory"
          >
            <div
              class="mx-auto flex items-end w-full"
              v-for="(item, index) in products ?? []" :key="item.id"
            >
              <div class="w-full">
                <div class="w-full">
                  <div class="relative mx-auto w-[50%]">
                    <img class="mx-auto md:w-[136px] md:h-[136px]" :src="item?.image" :alt="locale === 'en' ? item?.name : item?.name_ar"/>
                    <img class="absolute bottom-0 ltr:left-[-35%] rtl:right-[-35%] w-[80px] h-[80px] rounded-full border border-primary" :src="item?.brand_logo" :alt="locale === 'en' ? item?.brand_name : item?.brand_name_ar" />
                    <div
                      v-if="item?.sale"
                      class="absolute z-2 ltr:right-[-61px] rtl:left-[-61px] bottom-0 uppercase w-full"
                    >
                      <div class="py-1 px-3 bg-thirdColor rounded-t-[15px] text-[11px] font-semibold text-primary">{{ locale === 'en' ? item?.sale?.title : item?.sale?.title_ar }}</div>
                      <div class="bg-orangeColor rounded-b-[15px] px-3 py-1 text-thirdColor flex flex-col">
                        <span class="font-semibold text-[15px]">{{ locale === 'en' ? item?.sale?.percent : item?.sale?.percent_ar }}</span>
                        <span class="text-[12px] font-medium line-through">{{ locale === 'en' ? item?.sale?.line_through : item?.sale?.line_through_ar }}</span>
                      </div>
                    </div>
                  </div>
                  <h2 class="text-primary italic font-semibold text-xl mt-4 mb-0 pb-0 truncate">{{ locale === 'en' ? item?.name : item?.name_ar }}</h2>
                  <div class="text-orangeColor italic font-semibold text-lg mt-[-5px] truncate">{{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}</div>
                  <div class="flex flex-col mt-3">
                    <div class="flex gap-2 italic text-sm	text-primary" v-if="item?.load_index?.value">
                      <div class="font-semibold">{{ locale === 'en' ? item?.load_index?.name : item?.load_index?.name_ar }}:</div>
                      <span class="font-bold">{{ item?.load_index?.value }}</span>
                    </div>
                    <div class="flex gap-2 italic text-sm	text-primary" v-if="item?.speed_index?.value">
                      <div class="font-semibold">{{ locale === 'en' ? item?.speed_index?.name : item?.speed_index?.name_ar }}:</div>
                      <span class="font-bold">{{ item?.speed_index?.value }}</span>
                    </div>
                    <div class="flex gap-2 italic text-sm	text-primary" v-if="item?.origin?.value">
                      <div class="font-semibold">{{ locale === 'en' ? item?.origin?.name : item?.origin?.name_ar }}:</div>
                      <span class="font-bold">{{ item?.origin?.value }}</span>
                    </div>
                  </div>
                  <div class="ltr:italic text-sm text-primary font-thirdFont mt-4 lines-2" v-html="locale === 'en' ? item?.description : item?.description_ar">
                  </div>
                  <div class="pb-5 mt-4 flex flex-col gap-3">
                    <UButton
                      v-if="item?.stock"
                      class="
                        text-primary
                        flex
                        flex-col
                        w-full
                        items-center
                        justify-between
                        font-bold
                        text-[15px]
                        py-2
                        bg-thirdColor
                        border-2
                        border-thirdColor
                        uppercase
                        transition-all
                        button-shadow
                        duration-500
                        ease-in-out
                        hover:text-thirdColor
                        hover:bg-primary
                        hover:border-2
                        hover:border-primary
                        hover:border-secondary
                        hover:bg-secondary
                        min-h-[43px]
                      "
                      :loading="cartLoading[item?.id]"
                      @click="addToCarts(item)"
                    >
                      <div v-if="!cartLoading[item?.id]" class="flex items-center gap-3">
                        <ClientOnly>
                          <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                        </ClientOnly>
                        <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(item?.price)) }}</span>
                        </div>
                      </div>
                    </UButton>
                    <UButton
                      v-else
                      class="
                        text-primary
                        flex
                        flex-col
                        w-full
                        items-center
                        justify-between
                        font-bold
                        uppercase
                        py-2
                        transition-all
                        button-shadow
                        hover:text-primary
                        hover:border-thirdColor
                        hover:bg-thirdColor
                        disabled:border-thirdColor
                        disabled:bg-thirdColor
                        min-h-[43px]
                      "
                      disabled
                    >
                      <div class="flex items-center gap-3">
                        <div class="text-[12px] mt-[3px]">{{ $t('Out of stock') }}</div>
                          <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center">
                            <span class="rtl:text-lg">{{ $t('EGP') }}</span>
                            <span class="rtl:text-base">{{ formatPrice(Number(item?.price)) }}</span>
                          </div>
                      </div>
                    </UButton>
                    <UButton
                    class="
                      flex
                      flex-col
                      w-full
                      uppercase
                      items-center
                      justify-between
                      font-bold
                      text-[15px]
                      py-2
                      text-thirdColor
                      border-2
                      border-primary
                      button-shadow
                      transition-all
                      duration-500
                      min-h-[43px]
                      ease-in-out
                      hover:text-primary
                      hover:bg-secondary
                      hover:border-secondary
                      hover:text-thirdColor"
                      :to="localePath(`/product/${item.type}/${item?.id}`)"
                      >
                        {{ $t('VIEW PRODUCT') }}
                    </UButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <el-skeleton animated>
              <template #template>
                <div
                  class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start p-4"
                >
                  <div
                    class="mx-auto flex items-end w-full"
                    v-for="(item, index) in products.length ? products : [1, 2, 3]" :key="index"
                  >
                    <div class="w-full">
                      <div class="w-full">
                          <div class="!relative mx-auto w-[60%]">
                            <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[150px]"/>
                            <el-skeleton-item variant="image" class="!absolute !bottom-0 !left-[-35%] !w-[80px] !h-[80px] !rounded-full !border !border-primary" />
                          </div>
                          <div class="mt-4">
                            <el-skeleton-item variant="text" style="width: 80%" />
                          </div>
                          <div>
                            <el-skeleton-item variant="text" style="width: 60%" />
                          </div>
                          <div class="!mt-4">
                            <el-skeleton-item variant="text" style="width: 40%" />
                          </div>
                          <div>
                            <el-skeleton-item variant="text" style="width: 50%" />
                          </div>
                          <div>
                            <el-skeleton-item variant="text" style="width: 40%" />
                          </div>
                          <div class="mt-6">
                            <el-skeleton-item variant="text" style="width: 100%; height: 100px;" />
                          </div>
                      </div>
                    </div>
                  </div>

                </div>
              </template>
            </el-skeleton>
          </div>
          <div v-if="!products?.length && !loadingCategory">
            <div
              class="flex items-center justify-center font-secondaryFont uppercase text-secondary text-[22px] font-bold h-[400px]"
            >
              {{ $t('No products in this category') }}
            </div>
          </div>
        </div>
        <div class="font-thirdFont text-primary text-xl text-center mt-5" v-if="products?.length">
          {{ $t('There are more than') }} {{ Number(perPage) * Number(paginationPages) }} {{ $t('products to see.') }}
        </div>
        <div class="flex justify-center mt-6">
          <div class="flex gap-3">
            <button
              @click="previousPage"
              :disabled="currentNumber === 1"
              class="
                text-primary
                font-secondaryFont
                font-bold
                w-[40px]
                h-[40px]
                border-2
                border-primary
                rounded-full
                flex
                items-center
                justify-center"
            >
              <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
              </svg>
              <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
              </svg>
            </button>
            <button
              v-for="pageNumber in totalPagesArray"
              :key="pageNumber"
              @click="selectPage(pageNumber)"
              class="
                text-primary
                font-secondaryFont
                font-bold
                w-[40px]
                h-[40px]
                uppercase
                border-2
                border-primary
                rounded-full"
              :class="{ 'bg-orangeColor !border-orangeColor text-thirdColor': currentNumber === pageNumber }"
            >
              {{ pageNumber }}
            </button>
            <button
              @click="nextPage"
              :disabled="currentNumber === paginationPages"
              class="
                text-primary
                font-secondaryFont
                font-bold
                w-[40px]
                h-[40px]
                border-2
                uppercase
                border-primary
                rounded-full
                flex
                items-center
                justify-center"
            >
              <svg class="rtl:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.20117 5.13086L0.201172 2.94336V0.228516L7.39844 3.80273V5.75586L5.20117 5.13086ZM0.201172 6.9375L5.20117 4.71094L7.39844 4.14453V6.09766L0.201172 9.65234V6.9375Z" fill="black"/>
              </svg>
              <svg class="ltr:hidden" width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.75391 4.74023L7.70508 6.92773V9.66211L0.595703 6.09766V4.14453L2.75391 4.74023ZM7.70508 2.95312L2.75391 5.17969L0.595703 5.73633V3.79297L7.70508 0.228516V2.95312Z" fill="#1D3C34"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>



    <div class="md:hidden">
      <div class="relative md:hidden" ref="scrollContainer">
        <div  v-if="!loadingCategory" class="mx-auto flex items-end border-none-custom border-b border-primary pb-6 mt-6" v-for="(item, index) in products ?? []" :key="item.id">
          <div class="px-[20px] max-w-[1280px] mx-auto w-full">
            <div class="w-full">
              <div class="w-full">
                <ULink :to="localePath(`/product/${item.type}/${item.id}`)">
                  <div class="flex gap-2 items-center">
                    <div class="relative min-w-[35%]">
                      <img class="mx-auto w-[105px] h-[105px] md:w-full" :src="item.image" :alt="locale === 'en' ? item.name : item.name_ar"/>
                      <img class="absolute bottom-0 ltr:left-[25%] rtl:right-[25%] w-[60px] h-[60px] rounded-full border border-primary" :src="item.brand_logo" :alt="item.brand_name" />
                    </div>
                    <div>
                      <h2 class="text-primary italic font-semibold text-[15px] lines-1">{{ locale === 'en' ? item?.name : item?.name_ar }}</h2>
                      <div class="text-orangeColor italic font-semibold text-sm mt-[-5px] lines-1">{{ locale === 'en' ? item?.excerpt : item?.excerpt_ar }}</div>
                      <div class="flex flex-col mt-2">
                        <div class="flex mt-1 gap-2 italic text-xs text-primary" v-if="item?.load_index?.value">
                          <div class="font-semibold">{{ locale === 'en' ? item?.load_index?.name : item?.load_index?.name_ar }}:</div>
                          <span class="font-bold">{{ item?.load_index?.value }}</span>
                        </div>
                        <div class="flex mt-1 gap-2 italic text-xs text-primary" v-if="item?.speed_index?.value">
                          <div class="font-semibold">{{ locale === 'en' ? item?.speed_index?.name : item?.speed_index?.name_ar }}:</div>
                          <span class="font-bold">{{ item?.speed_index?.value }}</span>
                        </div>
                        <div class="flex mt-1 gap-2 italic text-xs text-primary" v-if="item?.origin?.value">
                          <div class="font-semibold">{{ locale === 'en' ? item?.origin?.name : item?.origin?.name_ar }}:</div>
                          <span class="font-bold">{{ item?.origin?.value }}</span>
                        </div>
                      </div>
                      <div class="italic text-[12px] text-primary font-thirdFont mt-1 lines-2" v-html="locale === 'en' ? item?.description : item?.description_ar">
                      </div>
                    </div>
                  </div>
                </ULink>
                <div class="mt-4 flex flex-col gap-3">
                  <UButton
                    v-if="item?.stock"
                    class="
                      text-thirdColor
                      flex
                      flex-col
                      w-full
                      items-center
                      justify-between
                      font-bold
                      text-[15px]
                      py-2
                      bg-orangeColor
                      uppercase
                      border-2
                      border-orangeColor
                      transition-all
                      button-shadow
                      duration-500
                      ease-in-out
                      hover:text-thirdColor
                      hover:bg-primary
                      hover:border-2
                      hover:border-primary
                      hover:border-primary
                      hover:bg-primary
                      min-h-[43px]
                    "
                    :loading="cartLoading[item.id]"
                    @click="addToCarts(item)"
                  >
                    <div v-if="!cartLoading[item.id]" class="flex items-center justify-center gap-3 w-full">
                      <ClientOnly>
                        <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                      </ClientOnly>
                        <div v-if="item?.sale">
                          <span class="font-normal line-through">{{ locale === 'en' ? item?.sale?.line_through : item?.sale?.line_through_ar }}</span>
                        </div>
                        <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center">
                          <span>{{ $t('EGP') }}</span>
                          <span>{{ formatPrice(Number(item.price)) }}</span>
                        </div>
                    </div>
                  </UButton>
                  <UButton
                    v-else
                    class="
                      text-primary
                      flex
                      flex-col
                      w-full
                      items-center
                      justify-between
                      font-bold
                      uppercase
                      py-2
                      transition-all
                      button-shadow
                      hover:text-primary
                      hover:border-thirdColor
                      hover:bg-thirdColor
                      disabled:border-thirdColor
                      disabled:bg-thirdColor
                      min-h-[43px]
                    "
                    disabled
                  >
                    <div class="flex items-center gap-3">
                      <div class="text-[12px]">{{ $t('Out of stock') }}</div>
                      <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center text-[18px]">
                        <span>{{ $t('EGP') }}</span>
                        <span>{{ formatPrice(Number(item?.price)) }}</span>
                      </div>
                    </div>
                  </UButton>
                  <UButton
                    class="
                      flex
                      flex-col
                      w-full
                      items-center
                      justify-between
                      font-bold
                      text-[15px]
                      py-2
                      uppercase
                      text-thirdColor
                      border-2
                      border-primary
                      button-shadow
                      transition-all
                      duration-500
                      ease-in-out
                      min-h-[43px]
                      hover:text-primary
                      hover:bg-secondary
                      hover:border-secondary
                      hover:text-thirdColor"
                      :to="localePath(`/product/${item.type}/${item.id}`)"
                    >
                      {{ $t('VIEW PRODUCT') }}
                  </UButton>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="px-[30px]">
          <el-skeleton animated>
            <template #template>
              <div
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start p-4"
              >
                <div
                  class="mx-auto flex items-end w-full"
                  v-for="(item, index) in products.length ? products : [1, 2, 3]" :key="index"
                >
                  <div class="w-full">
                    <div class="w-full">
                        <div class="!relative mx-auto w-[60%]">
                          <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[150px]"/>
                          <el-skeleton-item variant="image" class="!absolute !bottom-0 !left-[-35%] !w-[80px] !h-[80px] !rounded-full !border !border-primary" />
                        </div>
                        <div class="mt-4">
                          <el-skeleton-item variant="text" style="width: 80%" />
                        </div>
                        <div>
                          <el-skeleton-item variant="text" style="width: 60%" />
                        </div>
                        <div class="!mt-4">
                          <el-skeleton-item variant="text" style="width: 40%" />
                        </div>
                        <div>
                          <el-skeleton-item variant="text" style="width: 50%" />
                        </div>
                        <div>
                          <el-skeleton-item variant="text" style="width: 40%" />
                        </div>
                        <div class="mt-6">
                          <el-skeleton-item variant="text" style="width: 100%; height: 100px;" />
                        </div>
                    </div>
                  </div>
                </div>

              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <div v-if="loadingLoadMoreButton" class="px-[30px]">
        <el-skeleton animated>
          <template #template>
            <div
              class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 flex-wrap gap-4 justify-start p-4"
            >
              <div
                class="mx-auto flex items-end w-full"
                v-for="(item, index) in [1]" :key="index"
              >
                <div class="w-full">
                  <div class="w-full">
                      <div class="!relative mx-auto w-[60%]">
                        <el-skeleton-item variant="image" class="!mx-auto !w-full !h-[150px]"/>
                        <el-skeleton-item variant="image" class="!absolute !bottom-0 !left-[-35%] !w-[80px] !h-[80px] !rounded-full !border !border-primary" />
                      </div>
                      <div class="mt-4">
                        <el-skeleton-item variant="text" style="width: 80%" />
                      </div>
                      <div>
                        <el-skeleton-item variant="text" style="width: 60%" />
                      </div>
                      <div class="!mt-4">
                        <el-skeleton-item variant="text" style="width: 40%" />
                      </div>
                      <div>
                        <el-skeleton-item variant="text" style="width: 50%" />
                      </div>
                      <div>
                        <el-skeleton-item variant="text" style="width: 40%" />
                      </div>
                      <div class="mt-6">
                        <el-skeleton-item variant="text" style="width: 100%; height: 100px;" />
                      </div>
                  </div>
                </div>
              </div>

            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'

const { t, locale } = useI18n()
const localePath = useLocalePath()

// Define types for better type safety
interface Product {
  id: number;
  name: string;
  name_ar: string;
  excerpt?: string;
  excerpt_ar?: string;
  description?: string;
  description_ar?: string;
  image?: string;
  brand_logo?: string;
  brand_name?: string;
  brand_name_ar?: string;
  price: number;
  type?: string;
  sale?: {
    title?: string;
    title_ar?: string;
    percent?: string;
    percent_ar?: string;
    line_through?: string;
    line_through_ar?: string;
  };
  load_index?: {
    name: string;
    name_ar: string;
    value: string;
  };
  speed_index?: {
    name: string;
    name_ar: string;
    value: string;
  };
  origin?: {
    name: string;
    name_ar: string;
    value: string;
  };
  [key: string]: any;
}

interface CartResponse {
  value: any;
}

// Define emits with proper types
const emit = defineEmits<{
  updatePage: [pageNumber: number];
  loadMore: [];
}>()

// States with proper typing
const loadingLoadMoreButton = useState<boolean>("loadingLoadMoreButton", () => false)
const isLoadMore = useState<boolean>('isLoadMore', () => false)

// User and product data
const userData = useState<Record<string, any>>("userData", () => ({}))
const products = useState<Product[]>("products", () => [])

// Cart data
const cartItems = useState<any[]>("cartItems", () => [])

// Pagination state
const paginationPages = useState<number>("paginationPages", () => 1)
const perPage = useState<number>("perPage", () => 8)
const currentNumber = useState<number>("currentNumber", () => 1)

// Loading states
const loadingCategory = useState<boolean>("loadingCategory", () => false)
const cartLoading = ref<Record<number, boolean>>({}) // Track loading state per product

const selectPage = (pageNumber: number) => {
  if (currentNumber.value !== pageNumber) {
    currentNumber.value = pageNumber
    emit('updatePage', pageNumber)
  }
}

// Function to go to the previous page
const previousPage = () => {
  if (currentNumber.value > 1) {
    currentNumber.value--
    emit('updatePage', currentNumber.value)
  }
};

// Function to go to the next page
const nextPage = () => {
  if (currentNumber.value < paginationPages.value) {
    currentNumber.value++
    emit('updatePage', currentNumber.value)
  }
}

// Limit to display 10 pages at a time
const pagesPerSet = 5;

// Function to get the current set of pages to display (Sliding Window)
const getCurrentPageSet = () => {
  const start = Math.max(1, currentNumber.value - Math.floor(pagesPerSet / 2));
  const end = Math.min(start + pagesPerSet - 1, paginationPages.value);

  // Adjust the start if the end is less than 10 pages away from paginationPages
  const adjustedStart = Math.max(1, end - pagesPerSet + 1);

  return Array.from({ length: end - adjustedStart + 1 }, (_, i) => adjustedStart + i);
}

// Get the current set of pages
const totalPagesArray = computed(() => {
  return getCurrentPageSet();
})

// Add to cart function with improved error handling
const addToCarts = async (AddToCartItem: Product) => {
  try {
    // Set loading state only for the clicked product
    cartLoading.value[AddToCartItem.id] = true

    // Default quantity is 1
    const counter = ref(1)

    // Call the addToCart function with proper typing
    const { cart } = await addToCart(
      AddToCartItem,
      Number(userData?.value?.id),
      counter,
      true
    )

    // Update cart state
    cartItems.value = cart.value
  } catch (error) {
    // Show error notification
    ElNotification({
      title: 'Error',
      message: t('Failed to add item to cart'),
      type: 'error',
      duration: 3000
    })
    console.error('Error adding to cart:', error)
  } finally {
    // Reset loading state only for the clicked product
    cartLoading.value[AddToCartItem.id] = false
  }
}

// Responsive Logic
const isDesktop = computed(() => window.innerWidth >= 768);

const loadMorePosts = () => {
  emit('loadMore')
}


// Debounce function to limit how often a function can be called
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number): ((...args: Parameters<T>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return function(this: any, ...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      fn.apply(this, args);
      timeoutId = null;
    }, delay);
  };
};

// Function to detect if user has scrolled to the bottom
const handleScroll = debounce(() => {
  const container = document.documentElement || document.body;
  const bottomReached = container.scrollTop + window.innerHeight >= container.scrollHeight - 100;

  if (bottomReached && !loadingLoadMoreButton.value && !isLoadMore.value) {
    loadMorePosts();
  }
}, 200); // 200ms debounce

// Add scroll listener on mount
onMounted(() => {
  if (typeof window !== 'undefined' && !isDesktop.value) {
    window.addEventListener('scroll', handleScroll);
  }
});

// Remove scroll listener before unmounting
onBeforeUnmount(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('scroll', handleScroll);
  }
});
</script>

<style>
</style>


