<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto hidden md:block">
      <div
        class="mt-10 mb-10 rounded-[16px] rtl:font-notoSans text-center p-7 font-secondaryFont text-xl text-primary"
        style="background-color: #B7CDC2;"
      >
        🎉 {{ $t('Great news! We found') }} {{ searchCount }} {{ $t('results for your search on') }}
        <span class="font-bold">"{{ searchParam }}"!</span>
      </div>
    </div>
    <div class="text-primary font-secondaryFont text-[16px] md:hidden" style="background-color:#B7CDC2;">
      <div class="px-[20px] max-w-[1280px] mx-auto py-2 text-center rtl:font-notoSans">
        🎉 {{ $t('Great news! We found') }} {{ searchCount }} {{ $t('results for your search on') }} <span class="font-bold">"{{ searchParam }}"!</span>
      </div>
    </div>
    <div>
      <ProductProductsSearch @updatePage="updatePage" @loadMore="loadMore" class="mb-40 md:mb-0"/>
    </div>
    <RequestsSearch class="hidden md:block" />
  </div>
</template>
<script setup lang="ts">
import type { Product } from '~/types/pagesContent'

const route = useRoute()
const router = useRouter()

/**
 * Search parameters and state
 */
const searchParam = ref<string | null>(route.query.search as string || null)
const searchCount = ref<number>(0)
const loadingLoadMoreButton = useState<boolean>("loadingLoadMoreButton", () => false)
const isLoadMore = useState<boolean>('isLoadMore', () => false)
/**
 * Pagination state
 */
const page = useState<number>("page", () => parseInt(route.query.page as string))
const loadingCategory = useState<boolean>('loadingCategory', () => false)
const perPage = useState<number>("perPage", () => {
  // Initialize perPage from URL or default to 8
  return parseInt(route.query.posts_per_page as string) || 8
})
const paginationPages = useState<number>("paginationPages", () => 1)
const currentNumber = useState<number>("currentNumber", () => parseInt(route.query.page as string) || 1)

/**
 * Check if the device is mobile (width < 768px)
 */
const isMobile = computed<boolean>(() => {
  if (typeof window !== 'undefined') {
    return window.innerWidth < 768
  }
  return false
})

/**
 * Base number of products per page
 */
const BASE_PER_PAGE = 9

/**
 * Products state
 */
const products = useState<Product[]>("products", () => [])

/**
 * Fetch search results from the API
 * @param appendResults Whether this is a load more request (append results) or a new search (replace results)
 */
const fetchData = async (appendResults: boolean = false): Promise<void> => {
  if (appendResults) {
    loadingLoadMoreButton.value = true
  } else {
    loadingCategory.value = true
  }

  try {
    // Construct the API URL with appropriate parameters
    const apiUrl = `newBranch/search?page=${page.value}&posts_per_page=${perPage.value}&keyword=${searchParam.value || ''}`

    const { items: responseItems } = await fetchHock(apiUrl)

    if (responseItems?.value) {
      paginationPages.value = responseItems.value.total_pages || 1
      searchCount.value = responseItems.value.total_results || 0

      if (appendResults && Array.isArray(products.value)) {
        // Append new results to existing products for infinite scroll
        products.value = [...products.value, ...(responseItems.value.results || [])]
      } else {
        // Replace products with new results for new search
        products.value = responseItems.value.results || []
      }
    }
  } catch (error) {
    console.error('Error fetching search results:', error)
    if (!appendResults) {
      products.value = []
    }
  } finally {
    loadingCategory.value = false
    loadingLoadMoreButton.value = false

    // Reset isLoadMore state after a short delay to prevent multiple triggers
    if (appendResults) {
      setTimeout(() => {
        isLoadMore.value = false
      }, 300)
    }
  }
}

// Initial data fetch
await fetchData()

/**
 * Handle page change from pagination controls
 * @param newPage The new page number
 */
const updatePage = async (newPage: number): Promise<void> => {
  page.value = newPage
  currentNumber.value = newPage

  // Reset posts_per_page to BASE_PER_PAGE when changing pages
  perPage.value = BASE_PER_PAGE

  // Update URL with new page number and reset posts_per_page
  await router.push({
    query: {
      ...route.query,
      page: newPage,
      posts_per_page: BASE_PER_PAGE
    }
  })

  // Fetch new data
  await fetchData()

  // Scroll to top when changing pages via pagination
  if (typeof window !== 'undefined') {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

/**
 * Handle infinite scroll load more
 */
const loadMore = async (): Promise<void> => {
  // Only proceed if we're not already loading and there are more pages
  if (!loadingLoadMoreButton.value && !isLoadMore.value && page.value < paginationPages.value) {
    isLoadMore.value = true

    if (isMobile.value) {
      // On mobile: keep page=1 but increase posts_per_page by BASE_PER_PAGE
      perPage.value += BASE_PER_PAGE

      // Update URL silently (without triggering navigation)
      router.replace({
        query: {
          ...route.query,
          posts_per_page: perPage.value
        }
      })
    } else {
      // On desktop: increment page number
      page.value++

      // Update URL silently (without triggering navigation)
      router.replace({
        query: {
          ...route.query,
          page: page.value
        }
      })
    }

    // Fetch and append more data
    await fetchData(true) // Pass true to indicate we want to append results
  }
}

/**
 * Watch for URL page changes
 */
watch(() => route.query.page, async (newPage) => {
  if (newPage && parseInt(newPage as string) !== page.value) {
    const pageNum = parseInt(newPage as string)
    page.value = pageNum
    currentNumber.value = pageNum
    await fetchData()
  }
})

/**
 * Watch for search query changes
 */
watch(() => route.query.search, async (current) => {
  searchParam.value = current as string || null

  // Reset pagination when search changes
  page.value = 1
  currentNumber.value = 1
  perPage.value = BASE_PER_PAGE

  // Update URL
  await router.push({
    query: {
      ...route.query,
      page: 1,
      posts_per_page: BASE_PER_PAGE
    }
  })

  // Fetch new results
  await fetchData()
})

/**
 * Watch for posts_per_page URL parameter changes
 */
watch(() => route.query.posts_per_page, (newValue) => {
  if (newValue && parseInt(newValue as string) !== perPage.value) {
    perPage.value = parseInt(newValue as string) || BASE_PER_PAGE
  }
})
</script>