<template>
  <div>
    <UModal v-model="showEmailModal" class="custom-color-for-modal">
      <div class="px-4 md:px-14 py-3 md:py-8 bg-thirdColor mb-[50px] md:mb-0">
        <h2 class="md:text-xl font-bold font-secondaryFont text-secondary mb-4 uppercase rtl:font-notoSans">{{ $t('verify email') }}</h2>

        <div class="space-y-4">
          <div class="flex items-center justify-between gap-2">
            <template v-for="(digit, index) in 6" :key="index">
              <div class="relative">
                <input
                  :ref="el => inputRefs[index] = el"
                  v-model="verificationCode[index]"
                  type="text"
                  maxlength="1"
                  :disabled="!codeSent"
                  :class="[
                    'md:w-12 md:h-12 w-[35px] h-[35px] border border-primary rounded text-center text-xl font-bold focus:outline-none relative z-10',
                    codeSent ? 'bg-thirdColor' : 'bg-gray-300 text-gray-500' // Change color if disabled
                  ]"
                  @input="handleInput(index)"
                  @keydown="handleKeydown($event, index)"
                />
                <span 
                  class="
                    absolute 
                    top-1/2 
                    left-1/2 
                    transform 
                    -translate-x-1/2 
                    -translate-y-1/2 
                    text-primary 
                    font-bold 
                    text-2xl
                    pointer-events-none
                    z-0
                    transition-opacity
                    duration-200
                  "
                  :class="{
                    'opacity-0': verificationCode[index],
                    'opacity-100': !verificationCode[index]
                  }"
                >
                  -
                </span>
              </div>
            </template>
          </div>
          <div v-if="error" class="font-secondaryFont rtl:font-notoSans" style="color: #FF0101;">{{ $t('The code is incorrect. Please try again') }}.</div>
          <div v-if="success" class="font-secondaryFont rtl:font-notoSans" style="color: #4B9560;">{{ $t('The code is correct, and your number has been successfully verified') }}.</div>

          <div class="font-secondaryFont md:text-base text-[13px] rtl:font-notoSans" style="color: #1E1E1E;">
            {{ $t('You’ll receive a 6 digit code via your email') }}. <span class="font-bold">{{ userData?.email }}</span> {{ $t('Enter the code above to complete the verification. The code is valid for 10 minutes.') }}
          </div>

          <div class="flex justify-center">
            <UButton 
              class="bg-orangeColor text-thirdColor rtl:font-notoSans uppercase font-bold md:text-base text-[14px] md:py-3 py-2 px-8 md:px-14 rounded-full"
              @click="handleSendCode"
              :disabled="countdown > 0"
              :loading="verifyLoading"
            >
              {{ $t('SEND CODE') }}
            </UButton>
          </div>
          <div class="text-center font-secondaryFont text-primary text-[13px]">
            {{ countdown > 0 ? `You can resend the code in ${countdown} seconds.` : 'You can now resend the code.' }}
          </div>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'

const router = useRouter()
const showEmailModal: any = useState("showEmailModal", () => true)
const userData: any = useState("userData", () => [])
const profileData: any = useState("profileData", () => {})

const verificationCode = ref(Array(6).fill(''))
const inputRefs = ref<any>([])
const isCompleted = computed(() => verificationCode.value.every(digit => digit !== ''))
const verifyLoading = ref(false)
const error = ref(false)
const success = ref(false)
const localePath = useLocalePath()

const countdown = ref(0)
const countdownInterval = ref<NodeJS.Timeout | null>(null)
const codeSent = ref(false) // Track if the code has been sent

const handleInput = async (index: number) => {
  // Ensure single character
  if (verificationCode.value[index]?.length > 1) {
    verificationCode.value[index] = verificationCode.value[index].slice(0, 1)
  }
  
  // Auto focus next input
  if (verificationCode.value[index] && index < 5) {
    inputRefs.value[index + 1]?.focus()
  }

  // Check if all digits are filled
  if (isCompleted.value) {
    verifyLoading.value = true
    const verified: any = await $fetch('/api/newBranch/verify/emailCheckCode', {
      method: 'post',
      body: {
        email: userData?.value?.email,
        verification_code: Number(verificationCode.value.join(''))
      }
    })
    
    if (verified.statusCode == 400) {
      error.value = true
      success.value = false
      verificationCode.value = Array(6).fill('')
      inputRefs.value[0]?.focus()
    } else {
      success.value = true
      error.value = false

      const { items: prof } = await fetchHock(`newBranch/address/${Number(userData?.value?.id)}`)

      profileData.value = prof.value
  
      // Close modal
        closeModal()
    }
    verifyLoading.value = false
  }
}

const handleKeydown = (event: KeyboardEvent, index: number) => {
  // Handle backspace
  if (event.key === 'Backspace' && !verificationCode.value[index] && index > 0) {
    verificationCode.value[index - 1] = ''
    inputRefs.value[index - 1]?.focus()
  }
}

const closeModal = () => {
  const currentRoute = router.currentRoute.value.path
  if (currentRoute !== '/profile') {
    router.push(localePath('/'))
  }
  showEmailModal.value = false
  verificationCode.value = Array(6).fill('')
}

const sendVerificationCode = async () => {
  try {
    await $fetch('/api/newBranch/verify/emailSendCode', {
      method: 'post',
      body: {
        email : userData?.value?.email
      }
    })
  } catch (error) {
    console.error('Failed to send verification code:', error)
  }
}

const startCountdown = () => {
  countdown.value = 120 // 2 minutes in seconds
  if (countdownInterval.value) clearInterval(countdownInterval.value)
  
  countdownInterval.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      if (countdownInterval.value) clearInterval(countdownInterval.value)
    }
  }, 1000)
}

const handleSendCode = async () => {
  try {
    await sendVerificationCode()
    codeSent.value = true // Set to true after sending the code
    startCountdown()
  } catch (error) {
    console.error('Failed to send code:', error)
  }
}

// Clean up interval when component is unmounted
onBeforeUnmount(() => {
  if (countdownInterval.value) clearInterval(countdownInterval.value)
})

onMounted(() => {
  // Focus first input when modal opens
  inputRefs.value[0]?.focus()
})
</script>

<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

input {
  background: transparent;
}

.custom-color-for-modal .fixed.transition-opacity {
  background-color: #1D3C34D9 !important;
}
</style>