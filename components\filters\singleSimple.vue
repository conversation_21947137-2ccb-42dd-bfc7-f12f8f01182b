<template>
  <div class="bg-secondary border-b border-primary">
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="py-5">
        <h2 class="text-primary font-secondaryFont text-lg mb-3 font-bold">{{ $t('Select the Best') }} {{ categoryName }} {{ $t('for Your Needs from the Options Below') }}</h2>
        <div class="flex w-full justify-between flex-col md:flex-row gap-[20px] lg:gap-[80px]">
          <div class="flex flex-col md:flex-row w-full lg:w-4/5 gap-5 select-filter">
            <div
              class="w-full variations-filter relative flex justify-between"
              v-for="(item, index) in filterItems ?? []"
              :key="item?.slug"
            >
              <div
                class="
                  uppercase
                  absolute
                  text-primary
                  font-semibold
                  opacity-1
                  rtl:font-bold
                  text-base
                  italic
                  z-[4]
                  ltr:left-[15px]
                  rtl:right-[15px]
                  top-[13px]
                  transition-all
                  text-events
                  duration-100
                  ease-in-out"
                  :class="{
                    'placeholder-animation-with-bg' : selectedFilter[item?.slug]
                  }"
                >
                  {{ locale === 'en' ? item?.name : item?.name_ar }}
                </div>




              <el-select
                v-model="selectedFilter[item?.slug]"
                placeholder=" "
                clearable
                size="large"
                style="width: 100%"
                @change="onSelect()"
                :loading="loadingCategory"
                :disabled="!isSelectEnabled(index)"
              >
                <template v-if="item.type === 'nested'">
                  <el-option-group
                    v-for="(group, index) in item.options"
                    :key="index"
                    :label="locale === 'en' ? group?.label : group?.label"
                  >
                    <el-option
                      v-for="option in group.options"
                      :key="option.term_id"
                      :label="locale === 'en' ? option.name : option.name_ar"
                      :disabled="!option.status"
                      :value="`${item?.slug}=${option.term_id}`"
                    />
                  </el-option-group>
                </template>
                <template v-else>
                  <el-option
                    v-for="option in item.options"
                    :key="option.term_id"
                    :label="locale === 'en' ? option.name : option.name_ar"
                    :disabled="!option.status"
                    :value="`${item?.slug}=${option.term_id}`"
                  />
                </template>
              </el-select>
            </div>

          </div>
          <div class="md:w-[22%]">
            <UButton
              class="
                flex
                flex-col
                w-full
                items-center
                justify-between
                font-bold
                text-lg
                py-2
                font-thirdFont
                rtl:text-xl
                h-[51px]
                text-thirdColor
                uppercase
                bg-primary
                border-2
                border-primary
                hover:bg-orangeColor
                hover:border-orangeColor
                transition-all
                duration-500
                ease-in-out"
                @click="goToCategory"
                >
              {{ $t('SEARCH') }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const loadingCategory = useState<boolean>('loadingCategory', () => false)

const categoryId: any = useState("categoryId", () => [])
const { items: filterItem } = await fetchHock(`newBranch/simpleAttributes/${Number(categoryId.value)}`)
const categoryName: any = useState("categoryName", () => '')

const { locale } = useI18n()
const localePath = useLocalePath()

const filterItems = ref<any>([])
const loading = ref(false)
const selectedFilter = ref<any>({})
const router = useRouter()


filterItems.value = filterItem.value.menus


// Ensure that filterItems is initialized with status
const onSelect = async () => {
  loading.value = true
  const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')
  const { items: filter } = await fetchHock(`newBranch/simpleAttributes/${Number(categoryId.value)}?${queryString}`)
  filterItems.value = filter.value.menus
  // transformAndApplyQueryParams(queryString, brand)
  loading.value = false
}

const goToCategory = () => {
  if (Object.keys(selectedFilter.value).length >= 1) {
    const queryString = Object.values(selectedFilter.value).filter(Boolean).join('&')
    
    // Navigate to the target route with the query parameters
    router.push({ path: localePath(`/category/${categoryId.value}`), query: queryString })
  }
}


const isSelectEnabled = (index: number) => {
  if (index === 0) return true;
  
  const previousSelects = filterItems.value.slice(0, index);
  return previousSelects.every((item: any) => selectedFilter.value[item.slug]);
};
</script>
<style>
.el-select-group__title {
  padding: 5px 0 !important;
  border-bottom: 1px solid #1D3C34 !important;
  border-top: 1px solid #1D3C34 !important;
  font-family: "Montserrat", sans-serif !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #1D3C34;
  opacity: 0.5;
  font-style: italic;
}

.el-select-group__wrap:not(:last-of-type):after {
  background: transparent;
}

.el-select-group__wrap:not(:last-of-type) {
  padding-bottom: 0 !important;
}

.el-select-group {
  padding: 0 10px !important;
}

.el-select-group li {
  border-bottom: 1px solid #1D3C34 !important;
}

.el-select-group .el-select-dropdown__item:last-child {
  border-bottom: none !important;
}

.el-select-dropdown__item.is-selected {
  color: #1D3C34;
}

.variations-filter .el-select__wrapper {
  height: 50px;
}
</style>
