<template>
  <div class="px-[20px] max-w-[1280px] mx-auto">
    <!-- Loading spinner -->
    <div v-if="loading" class="loading-spinner" aria-label="Loading"></div>

    <!-- Success message -->
    <div class="text-2xl rtl:font-notoSans font-bold text-primary mt-5 uppercase text-center py-[60px] mb-[170px]">
      {{ $t('Authentication successful') }}
    </div>

    <!-- Mobile verification modal -->
    <ModalsAuthSocialVerify v-if="showModal" />
  </div>
</template>
<script setup lang="ts">
import type { UserData } from '~/types/user'
import type { CartItem } from '~/types/defaultCart'

/**
 * Interface for email verification API response
 */
interface EmailVerifyResponse {
  success?: boolean;
  message?: string;
}

/**
 * Interface for meta data in user profile
 */
interface MetaData {
  key: string;
  value: string | number | boolean | object;
}

const localePath = useLocalePath()

// State variables with proper types
const socialData = useState<number | null>("socialData", () => null)
const router = useRouter()
const loading = ref<boolean>(false)
const showModal = useState<boolean>("showModal", () => false)

// Use cookies instead of localStorage for better security and persistence
import { persistentCookieOptions } from '~/utils/cookieOptions'
const tokenCookie = useCookie<string>('token', persistentCookieOptions)
const userIdCookie = useCookie<string>('user_id', persistentCookieOptions)

// State variables for user data and cart
const userData = useState<UserData>("userData", () => ({} as UserData))
const cartItems = useState<CartItem[]>("cartItems", () => [])

/**
 * Handles the login process after social authentication
 * - Generates a token and stores it in a cookie
 * - Fetches user data and cart items
 * - Checks if mobile verification is needed
 */
const isLoggedIn = async (): Promise<void> => {
  if (!socialData.value) return;

  try {
    loading.value = true;

    // Generate a secure token and store in cookie
    const generatedToken = Math.random().toString(36).substring(2) + Date.now().toString(36);
    tokenCookie.value = generatedToken;
    userIdCookie.value = String(socialData.value);

    const [ordersResponse, userDataResponse] = await Promise.all([
      fetchHock(`newBranch/orders/${socialData.value}`),
      fetchHock(`newBranch/customer/${socialData.value}`)
    ])

    if (userDataResponse?.items?.value) {
      userData.value = userDataResponse.items.value

      // Verify email
      const verifyData = {
        user_id: socialData.value
      };

      await $fetch<EmailVerifyResponse>('/api/newBranch/register/emailVerify', {
        method: 'post',
        body: verifyData
      });

      // Update cart state
      cartItems.value = ordersResponse.items.value


      // Check if mobile verification is needed
      const mobileVerify = userDataResponse?.items?.value?.meta_data?.some(
        (meta: MetaData) => meta.key === 'mobile_verify'
      );

      if (!mobileVerify) {
        showModal.value = true;
      } else {
        router.push(localePath('/'));
      }
    }
  } catch (error) {
    console.error('Error during login process:', error);
  } finally {
    loading.value = false;
  }
};

// Initialize login process when component is mounted
onMounted(async () => {
  await isLoggedIn();
});

// Watch for modal close to redirect user
watch(() => showModal.value, (val) => {
  if (!val) {
    router.push(localePath('/'));
  }
});
</script>
<style scoped>
/**
 * Loading spinner animation
 * Uses primary and secondary brand colors
 */
.loading-spinner {
  /* Border styling */
  border: 16px solid var(--color-primary, #1D3C34);
  border-top: 16px solid var(--color-secondary, #4B9560);
  border-radius: 50%;

  /* Dimensions */
  width: 120px;
  height: 120px;

  /* Positioning */
  margin: 50px auto;

  /* Animation */
  animation: spin 2s linear infinite;
}

/**
 * Rotation animation for the spinner
 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>