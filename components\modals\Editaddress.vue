<template>
  <div>
    <UModal v-model="editAddressModal" class="orderModal">
      <div class="bg-thirdColor">
        <div class="flex justify-between items-center p-4 md:p-8">
          <div class="font-secondaryFont font-bold text-secondary text-[12px] md:text-xl">{{ $t('EDIT SHIPPING ADDRESS') }}</div>
          <div class="text-end">
            <ClientOnly>
              <font-awesome-icon class="text-primary text-xl md:text-[25px] cursor-pointer" icon="fa-solid fa-xmark" @click="editAddressModal = false" />
            </ClientOnly>
          </div>
        </div>
        <div class="p-4 md:p-8 custom-form-responsive">
          <UForm ref="form" :schema="schema" :state="state" class="md:space-y-4" @submit="onSubmit">
            <div class="w-full mx-auto flex flex-col gap-2 md:gap-4">
              <div class="relative text-field">
                <UFormGroup name="ship_address_1">
                  <input
                    class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                    variant="outline"
                    v-model="state.ship_address_1"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      top-[13px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-with-bg' : state?.ship_address_1,
                      }"
                    >
                      {{ $t('TITLE') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="relative text-field">
                <UFormGroup name="ship_fname">
                  <input
                    class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                    variant="outline"
                    v-model="state.ship_fname"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      top-[13px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-with-bg' : state?.ship_fname,
                      }"
                    >
                      {{ $t('First Name') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="relative text-field">
                <UFormGroup name="ship_lname">
                  <input
                    class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                    variant="outline"
                    v-model="state.ship_lname"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      top-[13px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-with-bg' : state?.ship_lname,
                      }"
                    >
                      {{ $t('last Name') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="relative text-field">
                <UFormGroup name="ship_address_2">
                  <input
                    class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                    variant="outline"
                    v-model="state.ship_address_2"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      top-[13px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-with-bg' : state?.ship_address_2,
                      }"
                    >
                      {{ $t('Address') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="relative text-field">
                <UFormGroup name="ship_address_phone">
                  <input
                    class="bg-thirdColor w-full border border-primary py-3 px-5 rounded-lg focus:outline-0"
                    variant="outline"
                    v-model="state.ship_address_phone"
                    required
                    placeholder=" "
                  >
                  <span
                    class="
                      uppercase
                      absolute
                      text-primary
                      font-semibold
                      opacity-1
                      font-base
                      z-[4]
                      ltr:left-[22px]
                      rtl:right-[22px]
                      top-[13px]
                      transition-all
                      duration-100
                      ease-in-out"
                      :class="{
                        'placeholder-animation-with-bg' : state?.ship_address_phone,
                      }"
                    >
                      {{ $t('phone') }}
                  </span>
                </UFormGroup>
              </div>
              <div class="relative text-field variations-filter custom-field-input-third">
                <UFormGroup name="ship_address_city">
                  <div class="information">
                    <el-select
                      v-model="state.ship_address_city"
                      placeholder=" "
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in cities.response"
                        :key="item.name"
                        :label="item.name"
                        :value="item.name"
                      />
                    </el-select>
                    <span
                      class="
                        uppercase
                        absolute
                        text-primary
                        font-semibold
                        opacity-1
                        font-base
                        z-[4]
                        ltr:left-[22px]
                        rtl:right-[22px]
                        top-[13px]
                        transition-all
                        duration-100
                        ease-in-out"
                        :class="{
                          'placeholder-animation-with-bg !opacity-100' : state?.ship_address_city,
                        }"
                      >
                        {{ $t('City') }}
                    </span>
                  </div>
                </UFormGroup>
              </div>
            </div>
            <div class="flex justify-center mb-[100px]">
              <UButton
                type="submit"
                class="
                  font-thirdFont
                  font-bold
                  text-[15px]
                  md:text-lg
                  py-2
                  px-20
                  mt-5
                  uppercase
                  rounded-full
                  border
                  md:border-orangeColor
                  md:bg-orangeColor
                  bg-primary
                  border-primary
                  hover:bg-orangeColor
                  hover:border-orangeColor
                  ease-in-out
                  duration-500
                "
                variant="solid"
                :loading="loading"
              >
                <span v-if="!loading">{{ $t('UPDATE') }}</span>
              </UButton>
            </div>
          </UForm>
        </div>
      </div>
    </UModal>
  </div>
</template>
<script setup lang="ts">
</script>

<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import { z } from 'zod'
import { ElNotification } from 'element-plus'

const editAddressModal = useState("editAddressModal", () => false)
const addAddressIndex = useState("addAddressIndex", () => false)
const addAddressItem = useState("addAddressItem", () => false)
const additionalAddress: any = useState("additionalAddress", () => [])
const { t, locale } = useI18n()

const loading = ref(false)
// const shppingData = ref<any>({})
const userData: any = useState("userData", () => [])
const profileData: any = useState("profileData", () => {})


const schema = z.object({
  ship_address_1: z.string()
  .min(3, t('Title must be at least 3 characters')),
  ship_fname: z.string()
  .min(3, t('First Name must be at least 3 characters')),
  ship_lname: z.string()
  .min(3, t('Last Name must be at least 3 characters')),
  ship_address_2: z.string()
  .min(3, t('Adress must be at least 3 characters')),
  ship_address_phone: z.string().min(11, t('Phone Number must be at least 11 Number')),
  ship_address_city: z.string()
  .min(1, t('City is required')),
})


type Schema = z.output<typeof schema>

const state = reactive({
  userid: Number(userData?.value?.id),
  ship_address_1: undefined,
  ship_fname: undefined,
  ship_lname: undefined,
  ship_address_2: undefined,
  ship_address_phone: undefined,
  ship_address_city: undefined,
})

watch(addAddressIndex, (current) => {
  state.ship_address_1 = addAddressItem?.value?.ship_address_1
  state.ship_fname = addAddressItem?.value?.ship_fname
  state.ship_lname = addAddressItem?.value?.ship_lname
  state.ship_address_2 = addAddressItem?.value?.ship_address_2
  state.ship_address_phone = addAddressItem?.value?.ship_address_phone
  state.ship_address_city = addAddressItem?.value?.ship_address_city
})

const successPopUp = (message: string) => {
  ElNotification({
    // title: message,
    message: h('i', { style: 'color: #1D3C34; font-weight: bold;' }, message),
    // type: 'success',
    position: 'top-right',
  })
}


async function onSubmit (event: FormSubmitEvent<Schema>) {
  loading.value = true
  const data = ref({
    "userid": state.userid,
    "row_index": addAddressIndex.value,
    "fields": {
        "ship_address_1": state.ship_address_1,
        "ship_fname": state.ship_fname,
        "ship_lname": state.ship_lname,
        "ship_address_2":state.ship_address_2,
        "ship_address_city": state.ship_address_city,
        "ship_address_phone": state.ship_address_phone
    }
  })
  
  const response = await $fetch(`/api/newBranch/address/addAddress`, {
    method: 'post',
    body: data.value
  }) as any

  const { items: usersData } = await fetchHock(`newBranch/address/${Number(userData?.value?.id)}`)
  profileData.value = usersData.value
  userData.value = usersData.value
  localStorage.setItem("userData", JSON.stringify(usersData.value))
  

  usersData.value.meta_data.map((el: any) => {
    if (el.key === 'additional_shipping_addresses') {
      additionalAddress.value = el.value
    }
  })
  successPopUp(t('Address Updated Successfully'))
  editAddressModal.value = false
  loading.value = false
}


const cities = useState("cities", () => {})
</script>

<style scoped>
</style>
