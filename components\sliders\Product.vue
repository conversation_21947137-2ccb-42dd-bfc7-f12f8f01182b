<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto">
      <div class="relative sellerSwiper md:block hidden">
        <!-- <ClientOnly> -->
          <Swiper
            @swiper="onSwiper"
            :modules="[SwiperEffectCreative, SwiperPagination, SwiperMousewheel]"
            :loop="true"
            :space-between="20"
            :autoplay="{
              delay: 4000,
              disableOnInteraction: false,
            }"
            :breakpoints="{
              360: {
                slidesPerView: 1,
              },
              640: {
                slidesPerView: 1,
              },
              768: {
                slidesPerView: 3,
              },
              1090: {
                slidesPerView: 4,
              },
              1400: {
                slidesPerView: 4,
              },
              1600: {
                slidesPerView: 4,
              },
              1920: {
                slidesPerView: 4,
              },
              2560: {
                slidesPerView: 4,
              },
              3200: {
                slidesPerView: 4,
              },
            }"
          >
            <div class="absolute top-0 left-0 w-full z-20">
              <div class="flex justify-between items-center w-full">
                <div class="font-secondaryFont rtl:font-notoSans uppercase text-primary text-lg sm:text-[25px] font-bold">
                  {{ title }}
                </div>
                <div v-if="(products ?? []).length > 4" class="top-slider-arrows hidden sm:flex gap-2">
                  <div
                    class="swiper-button-prev-outside cursor-pointer w-[40px] h-[40px] bg-primary rounded-full flex justify-center items-center"
                    @click="swiperInstance.slideNext()"
                  >
                    <Icon v-if="locale === 'en'" class="text-thirdColor cursor-pointer" name="carbon:chevron-left" size="30" />
                    <Icon v-else class="text-thirdColor cursor-pointer" name="carbon:chevron-right" size="30" />
                  </div>
                  <div
                    class="swiper-button-next-outside cursor-pointer w-[40px] h-[40px] bg-primary rounded-full flex justify-center items-center"
                    @click="swiperInstance.slidePrev()"
                  >
                    <Icon v-if="locale === 'en'" class="text-thirdColor cursor-pointer" name="carbon:chevron-right" size="30" />
                    <Icon v-else class="text-thirdColor cursor-pointer" name="carbon:chevron-left" size="30" />
                  </div>
                </div>
              </div>
            </div>
            <SwiperSlide v-for="(item) in products ?? []" :key="item.id">
              <div class="mx-auto flex items-end">
                <div class="mt-[80px] w-full">
                  <div class="w-full">
                    <div class="min-h-[150px]">
                      <div class="relative mx-auto w-[50%]">
                        <img class="mx-auto" :src="item.image" :alt="item.name"/>
                        <img class="absolute bottom-0 rtl:right-[-35%] ltr:left-[-35%] w-[80px] h-[80px] rounded-full border border-primary" :src="item.brand_logo" :alt="item.brand_name" />
                        <div
                          v-if="item?.sale"
                          class="absolute z-2 rtl:left-[-61px] ltr:right-[-61px] bottom-0 uppercase w-full"
                        >
                          <div class="py-1 px-3 bg-thirdColor rounded-t-[15px] text-[11px] font-primaryFont rtl:font-notoSans rtl:font-bold font-semibold text-primary">{{ typeof item.sale === 'object' ? item.sale.title : '' }}</div>
                          <div class="bg-orangeColor rounded-b-[15px] px-3 py-1 text-thirdColor flex flex-col">
                            <span class="font-semibold rtl:font-notoSans rtl:font-bold text-[15px]">{{ typeof item.sale === 'object' ? item?.sale?.percent : '' }}</span>
                            <span class="text-[12px] font-primaryFont rtl:font-notoSans font-medium line-through">{{ typeof item.sale === 'object' ? item?.sale?.line_through : '' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <h2 class="text-primary italic font-semibold text-xl font-primaryFont mt-4 mb-0 pb-0 truncate rtl:font-notoSans rtl:font-bold">{{ locale === 'en' ? item?.name : item?.name_ar }}</h2>
                    <div class="text-orangeColor italic font-semibold text-lg font-primaryFont mt-[-5px] truncate rtl:font-notoSans">{{ item.excerpt }}</div>
                    <div class="flex flex-col mt-3">
                      <div class="flex gap-2 font-primaryFont italic text-sm text-primary" v-if="item?.load_index?.value">
                        <div class="font-semibold">{{ item?.load_index?.name }}:</div>
                        <span class="font-bold rtl:font-notoSans">{{ item?.load_index?.value }}</span>
                      </div>
                      <div class="flex gap-2 font-primaryFont italic text-sm	text-primary" v-if="item?.speed_index?.value">
                        <div class="font-semibold">{{ item?.speed_index?.name }}:</div>
                        <span class="font-bold rtl:font-notoSans">{{ item?.speed_index?.value }}</span>
                      </div>
                      <div class="flex gap-2 font-primaryFont italic text-sm	text-primary" v-if="item?.origin?.value">
                        <div class="font-semibold">{{ item?.origin?.name }}:</div>
                        <span class="font-bold rtl:font-notoSans">{{ item?.origin?.value }}</span>
                      </div>
                    </div>
                    <div
                      class="ltr:italic text-sm text-primary rtl:font-notoSans font-thirdFont mt-4 lines-2"
                      v-html="locale === 'en' ? item?.description : item?.description_ar"
                    >
                    </div>
                    <div class="pb-5 mt-4 flex flex-col gap-3">
                      <UButton
                        v-if="item?.stock"
                        class="
                          text-primary
                          flex
                          flex-col
                          w-full
                          items-center
                          justify-between
                          font-primaryFont
                          font-bold
                          text-[15px]
                          uppercase
                          py-2
                          bg-thirdColor
                          border-2
                          border-thirdColor
                          transition-all
                          button-shadow
                          duration-500
                          ease-in-out
                          hover:text-thirdColor
                          hover:bg-primary
                          hover:border-2
                          hover:border-primary
                          hover:border-secondary
                          hover:bg-secondary
                          min-h-[43px]
                        "
                        :loading="cartLoading[item?.id as number]"
                        @click="addToCarts(item)"
                      >
                        <div v-if="!cartLoading[item?.id as number]" class="flex items-center gap-3">
                          <ClientOnly>
                            <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                          </ClientOnly>
                          <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center">
                            <span class="rtl:text-lg">{{ $t('EGP') }}</span>
                            <span class="rtl:font-notoSans rtl:text-base">{{ formatPrice(Number(item.price)) }}</span>
                          </div>
                        </div>
                      </UButton>
                      <UButton
                        v-else
                        class="
                          text-primary
                          flex
                          flex-col
                          w-full
                          items-center
                          justify-between
                          font-primaryFont
                          font-bold
                          uppercase
                          py-2
                          transition-all
                          button-shadow
                          hover:text-primary
                          hover:border-thirdColor
                          hover:bg-thirdColor
                          disabled:border-thirdColor
                          disabled:bg-thirdColor
                          min-h-[43px]
                          rtl:font-notoSans
                        "
                        disabled
                      >
                        <div class="flex items-center gap-3">
                          <div class="text-[12px] mt-[3px]">{{ $t('Out of stock') }}</div>
                          <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center">
                            <span class="rtl:text-lg">{{ $t('EGP') }}</span>
                            <span class="rtl:font-notoSans rtl:text-base">{{ formatPrice(Number(item.price)) }}</span>
                          </div>
                        </div>
                      </UButton>
                      <UButton
                        class="
                          flex
                          flex-col
                          w-full
                          items-center
                          justify-between
                          font-bold
                          text-[15px]
                          py-2
                          uppercase
                          text-thirdColor
                          font-primaryFont
                          border-2
                          border-primary
                          button-shadow
                          transition-all
                          duration-500
                          ease-in-out
                          min-h-[43px]
                          rtl:font-notoSans
                          hover:text-primary
                          hover:bg-secondary
                          hover:border-secondary
                          hover:text-thirdColor"
                          :to="localePath(`/product/${item.type}/${item.id}`)"
                        >
                          {{ $t('VIEW PRODUCT') }}
                      </UButton>
                    </div>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          </Swiper>
        <!-- </ClientOnly> -->
      </div>
      <div class="border-b border-primary mt-8 md:block hidden"></div>
    </div>
    <div class="relative md:hidden">
      <div class="sticky z-[55] top-0 left-0 w-full rtl:font-notoSans font-secondaryFont uppercase text-primary text-sm font-extrabold text-center py-2 bg-thirdColor border-b border-t border-primary">
        {{ title }}
      </div>
      <div class="mx-auto flex items-end border-none-custom border-b border-primary pb-6 mt-6" v-for="(item) in products ?? []" :key="item.id">
        <div class="px-[20px] max-w-[1280px] mx-auto w-full">
          <div class="w-full">
            <div class="w-full">
              <ULink :to="localePath(`/product/${item.type}/${item.id}`)">
                <div class="flex gap-2 items-center">
                  <div class="relative min-w-[35%]">
                    <img class="mx-auto w-[105px] h-[105px]" :src="item.image" :alt="item.name"/>
                    <img class="absolute bottom-0 rtl:right-[25%] ltr:left-[25%] w-[60px] h-[60px] rounded-full border border-primary" :src="item.brand_logo" :alt="item.brand_name" />
                  </div>
                  <div>
                    <h2 class="text-primary italic font-semibold text-[15px] font-primaryFont lines-1 rtl:font-notoSans rtl:font-bold">{{ locale === 'en' ? item?.name : item?.name_ar }}</h2>
                    <div class="text-orangeColor italic font-semibold text-sm font-primaryFont mt-[-5px] lines-1 rtl:font-notoSans">{{ item.excerpt }}</div>
                    <div class="flex flex-col mt-2">
                      <div class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary" v-if="item?.load_index?.value">
                        <div class="font-semibold">{{ item?.load_index?.name }}:</div>
                        <span class="font-bold rtl:font-notoSans">{{ item?.load_index?.value }}</span>
                      </div>
                      <div class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary" v-if="item?.speed_index?.value">
                        <div class="font-semibold">{{ item?.speed_index?.name }}:</div>
                        <span class="font-bold rtl:font-notoSans">{{ item?.speed_index?.value }}</span>
                      </div>
                      <div class="flex mt-1 gap-2 font-primaryFont italic text-xs text-primary" v-if="item?.origin?.value">
                        <div class="font-semibold">{{ item?.origin?.name }}:</div>
                        <span class="font-bold rtl:font-notoSans">{{ item?.origin?.value }}</span>
                      </div>
                    </div>
                    <div
                      class="ltr:italic text-sm text-primary font-thirdFont mt-4 lines-2 rtl:font-notoSans"
                      v-html="locale === 'en' ? item?.description : item?.description_ar"
                    >
                    </div>
                  </div>
                </div>
              </ULink>
              <div class="mt-4 flex flex-col gap-3">
                <UButton
                  v-if="item?.stock"
                  class="
                    text-thirdColor
                    flex
                    flex-col
                    w-full
                    items-center
                    justify-between
                    font-primaryFont
                    font-bold
                    text-[15px]
                    py-2
                    bg-orangeColor
                    uppercase
                    border-2
                    border-orangeColor
                    transition-all
                    button-shadow
                    duration-500
                    ease-in-out
                    hover:text-thirdColor
                    hover:bg-primary
                    hover:border-2
                    hover:border-primary
                    hover:border-primary
                    hover:bg-primary
                    min-h-[43px]
                    rtl:font-notoSans
                  "
                  :loading="cartLoading[item?.id as number]"
                  @click="addToCarts(item)"
                >
                  <div v-if="!cartLoading[item?.id as number]" class="flex items-center justify-center gap-3 w-full">
                    <ClientOnly>
                      <font-awesome-icon icon="fa-solid fa-cart-shopping" />
                    </ClientOnly>
                    <div v-if="item?.sale">
                      <span class="font-primaryFont font-normal line-through">{{ typeof item.sale === 'object' ? item.sale.line_through : '' }}</span>
                    </div>
                    <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center">
                      <span>{{ $t('EGP') }}</span>
                      <span class="rtl:font-notoSans">{{ formatPrice(Number(item?.price)) }}</span>
                    </div>
                  </div>
                </UButton>
                <UButton
                  v-else
                  class="
                    text-primary
                    flex
                    flex-col
                    w-full
                    items-center
                    justify-between
                    font-primaryFont
                    font-bold
                    uppercase
                    py-2
                    transition-all
                    button-shadow
                    hover:text-primary
                    hover:border-thirdColor
                    hover:bg-thirdColor
                    disabled:border-thirdColor
                    disabled:bg-thirdColor
                    min-h-[43px]
                  "
                  disabled
                >
                  <div class="flex items-center gap-3">
                    <div class="text-[12px]">{{ $t('Out of stock') }}</div>
                    <div class="flex gap-1 rtl:flex-row-reverse rtl:font-bold rtl:items-center text-[18px]">
                      <span>{{ $t('EGP') }}</span>
                      <span class="rtl:font-notoSans">{{ formatPrice(Number(item?.price)) }}</span>
                    </div>
                  </div>
                </UButton>
                <UButton
                  class="
                    flex
                    flex-col
                    w-full
                    items-center
                    justify-between
                    font-bold
                    text-[15px]
                    py-2
                    uppercase
                    text-thirdColor
                    font-primaryFont
                    border-2
                    border-primary
                    button-shadow
                    transition-all
                    duration-500
                    ease-in-out
                    min-h-[43px]
                    rtl:font-notoSans
                    hover:text-primary
                    hover:bg-secondary
                    hover:border-secondary
                    hover:text-thirdColor"
                    :to="localePath(`/product/${item.type}/${item.id}`)"
                  >
                    {{ $t('VIEW PRODUCT') }}
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'
import type { UserData } from '~/types/user'
import type { CartData, CartOrder } from '~/types/defaultCart'
import type { Product } from '~/types/pagesContent'

const { locale, t } = useI18n()
const localePath = useLocalePath()

interface ProductSliderProps {
  products?: Product[];
  title?: string;
}
defineProps<ProductSliderProps>()

const userData = useState<UserData | null>("userData", () => null)
const cartLoading = ref<{ [key: number]: boolean }>({}) // Track loading state per product

// Items on the cart
const cartItems = useState<CartData>("cartItems", () => ({}))

// Swiper settings
const swiperInstance = ref<any>(null)
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

// Add to cart function
const addToCarts = async (AddToCartItem: Product) => {
  try {
    // Set loading state only for the clicked product
    cartLoading.value[AddToCartItem.id] = true

    // Default quantity is 1
    const counter = ref(1)

    // Call the addToCart function with proper typing
    const { cart } = await addToCart(
      AddToCartItem,
      Number(userData?.value?.id),
      counter,
      true
    )

    // Update cart state
    cartItems.value = cart.value
  } catch (error) {
    // Show error notification
    ElNotification({
      title: 'Error',
      message: t('Failed to add item to cart'),
      type: 'error',
      duration: 3000
    })
    console.error('Error adding to cart:', error)
  } finally {
    // Reset loading state only for the clicked product
    cartLoading.value[AddToCartItem.id] = false
  }
}
</script>
