export default defineNuxtPlugin(() => {
  // This plugin ensures i18n is available on the client side
  // It runs after the i18n module is initialized
  
  // Add some debugging in development
  if (process.env.NODE_ENV === 'development') {
  }
  
  // Ensure i18n is available
  try {
    const { locale } = useI18n()
    if (process.env.NODE_ENV === 'development') {
    }
  } catch (error) {
    console.warn('i18n not available in client plugin:', error)
  }
}) 