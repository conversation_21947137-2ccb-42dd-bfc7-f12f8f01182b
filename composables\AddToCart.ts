import { ElNotification } from 'element-plus'
import { h } from 'vue'

export const addToCart = async (AddToCartItem: any, user_id: number, counter: any, slider: boolean = false) => {
  const router = useRouter()

  const cart = ref<any>({})

  // Function to show notification when product is added to cart
  const showAddToCartNotification = (productName: string, price: number | string, quantity: number) => {
    ElNotification({
      message: h('i', { style: 'color: #1D3C34; font-weight: bold;' },
        `Product Added To Cart Successfuly`
      ),
      position: 'top-right',
      duration: 3000
    })
  }

  const doesItemExist = (items: any[], productId: number, variationId: number) => {
    return items.some(item => item.product_id === productId && Number(item.variation_id) === variationId)
  }

  // Function to check if both matchingVariationId and AddToCartItem.id exist in the array and return the item
  const findItemId = (items: any[], productId: number, variationId: number) => {
      if (!variationId) {
          // For simple products, only match product_id
          const item = items.find(item => item.product_id === productId)
          return item ? item : null
      }
      // For variable products, match both product_id and variation_id
      const item = items.find(item => item.product_id === productId && item.variation_id === variationId)
      return item ? item : null
  }

  const AddToCartRefetch = async () => {
    if (user_id) {
      const data = ref({
        customer_id: Number(user_id),
        status: 'pending-cart',
        line_items: [
          {
            product_id: AddToCartItem.parent,
            quantity: counter.value,
            variation_id: Number(AddToCartItem.id),
          }
        ]
      })
      const pendingCart = ref<any>([])
      const { items } = await fetchHock(`orders/${Number(user_id)}`)
      pendingCart.value = items.value

      if(pendingCart.value?.single?.length) {
        const itemExists = doesItemExist(pendingCart.value?.single[0]?.items, Number(AddToCartItem.parent), Number(AddToCartItem.id))
        const itemId = findItemId(pendingCart.value?.single[0]?.items, Number(AddToCartItem.parent), Number(AddToCartItem.id))

        const itemExistsSimple = doesItemExist(pendingCart.value?.single[0]?.items, Number(AddToCartItem.id), 0)
        const itemIdSimple = findItemId(pendingCart.value?.single[0]?.items, Number(AddToCartItem.id), 0)

        if (itemExists) {
          const quantityPrice = itemId.quantity += counter.value

          const response = await $fetch(`/api/orders/update/${items.value?.single[0].id}`, {
            method: 'put',
            body: {
              customer_id: Number(user_id),
              line_items: [
                {
                  id: itemId.line_item_id,
                  quantity: quantityPrice,
                  product_id: itemId.product_id,
                  variation_id: itemId.variation_id ? itemId.variation_id : null,
                  subtotal: String(itemId.price * quantityPrice),
                  total: String(itemId.price * quantityPrice)
                }
              ]
            }
          }) as any

          const { items: resetCart } = await fetchHock(`orders/${Number(user_id)}`)
          cart.value = resetCart?.value

          // Show notification for updated item
          showAddToCartNotification(
            itemId.name || 'Product',
            itemId.price || 0,
            counter.value
          )

        } else if (itemExistsSimple) {
          const quantityPrice = itemIdSimple.quantity += counter.value

          const response = await $fetch(`/api/orders/update/${items.value?.single[0].id}`, {
            method: 'put',
            body: {
              customer_id: Number(user_id),
              line_items: [
                {
                  id: itemIdSimple.line_item_id,
                  quantity: quantityPrice,
                  product_id: itemIdSimple.product_id,
                  variation_id: itemIdSimple.variation_id ? itemId.variation_id : null,
                  subtotal: String(itemIdSimple.price * quantityPrice),
                  total: String(itemIdSimple.price * quantityPrice)
                }
              ]
            }
          }) as any

          const { items: resetCart } = await fetchHock(`orders/${Number(user_id)}`)
          cart.value = resetCart?.value

          // Show notification for updated item
          showAddToCartNotification(
            itemIdSimple.name || 'Product',
            itemIdSimple.price || 0,
            counter.value
          )

        } else {
          const response = await $fetch(`/api/orders/update/${items.value?.single[0].id}`, {
            method: 'put',
            body: data.value
          }) as any
          const { items: resetCart } = await fetchHock(`orders/${Number(user_id)}`)
          cart.value = resetCart?.value

          // Show notification for new item
          showAddToCartNotification(
            AddToCartItem.name || 'Product',
            AddToCartItem.price || 0,
            counter.value
          )
        }
      } else {
        const response = await $fetch('/api/orders/create', {
          method: 'post',
          body: data.value
        }) as any
        const { items: resetCart } = await fetchHock(`orders/${Number(user_id)}`)
        cart.value = resetCart?.value

        // Show notification for new item in new order
        showAddToCartNotification(
          AddToCartItem.name || 'Product',
          AddToCartItem.price || 0,
          counter.value
        )
      }
    } else {
      const loginUrl = '/auth/login'
      router.push(loginUrl)
    }
  }

  await AddToCartRefetch()

  return { cart, AddToCartRefetch }
}