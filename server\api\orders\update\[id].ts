export default defineEventHandler(async (event) => {
  try {

    const queryId = event?.context?.params?.id

   const bodyy = await readBody(event)

   const url = `${process.env.WC_API_URL}update-order`
   const method = 'PUT'
   
    const sendData = {
      customer_id: bodyy.customer_id,
      line_items: bodyy.line_items,
      order_id: queryId
    }
   
   const headers = {
     'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
     'Content-Type': 'application/json', // or 'text/plain', depending on your API
   }

     const response = await $fetch(url, {
       method: method,
       body: sendData,
       headers: headers,
     })
 
     const data = await response
     return data
 } catch (error) {
    console.error('Error in register handler:', error?.data)
    return {
      error: error?.data,
      statusCode: error?.statusCode,
    }
 }
})