<template>
  <div>
    <CategoryVariable v-if="currentCategory?.product_type === 'variable'" />
    <CategorySimple v-if="currentCategory?.product_type === 'simple'" />
    <div class="md:block hidden">
      <CarsCar />
    </div>
    <div class="mt-[60px] mb-[150px] md:mb-[60px] md:block hidden">
      <SlidersBlogsTips
        :posts="blogsData?.data"
        :title="$t('TIPS & TRICKS')"
        class="mb-[60px] mt-[60px]"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const links: any = useState("links", () => [])
const categoryDescription: any = useState("categoryDescription", () => '')

const { locale } = useI18n()

// Get current category data
const currentCategory = computed(() => {
  return links.value.find((link: any) => link.id === Number(route.params.id))
})

categoryDescription.value = locale.value === 'en' ? currentCategory?.value?.description : currentCategory?.value?.description_ar

// Blogs
const blogs: any = useState("blogs", () => [])
const { items: blogsData } = await fetchHock(`blogs?page=1&per_page=10`)
blogs.value = blogsData.value.data
</script>