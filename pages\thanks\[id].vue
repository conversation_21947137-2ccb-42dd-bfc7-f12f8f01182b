<template>
  <div>
    <!-- Desktop confirmation banner -->
    <div class="px-[20px] max-w-[1280px] mx-auto hidden md:block">
      <div
        class="mt-10 mb-10 rounded-[16px] text-center p-7 font-secondaryFont text-xl text-primary rtl:font-notoSans"
        style="background-color: #B7CDC2;"
      >
        <div class="font-bold text-3xl font-secondaryFont uppercase rtl:font-notoSans">{{ $t('CONGRATULATIONS') }} 🙌</div>
        <div class="font-secondaryFont font-bold text-xl mt-6 uppercase rtl:font-notoSans">
          {{ $t('Your order') }} #{{ route.params.id }} {{ $t('has been successfully placed.') }}
        </div>
        <div class="font-secondaryFont text-lg mt-2 rtl:font-notoSans">
          {{ $t('Please note that it may take up to 24 hours to confirm the availability of the items you ordered.') }}
        </div>
      </div>
    </div>

    <!-- Mobile confirmation banner -->
    <div class="py-5 px-[30px] text-center border-b border-primary md:hidden" style="background-color: #B7CDC2;">
      <div class="font-bold text-[15px] text-primary font-secondaryFont rtl:font-notoSans">{{ $t('CONGRATULATIONS') }} 🙌</div>
      <div class="font-secondaryFont text-[11px] text-primary mt-2 rtl:font-notoSans">
        {{ $t('Your order') }} #{{ route.params.id }} {{ $t('has been successfully placed.') }}
      </div>
    </div>

    <!-- Order tracking component -->
    <Trackorder v-if="!endsWithG" :order-data="trackOrderData" />
    <TrackorderGroup v-if="endsWithG" :order-data="trackOrderData" />
  </div>
</template>
<script setup lang="ts">
import type { UserData } from '~/types/user'

const route = useRoute()
const userData = useState<UserData>("userData", () => ({} as UserData))
const trackOrderData = useState<any>("trackOrderData", () => ({} as any))

/**
 * Check if order ID ends with -G
 */
const orderId = route.params.id as string
const endsWithG = orderId.endsWith('-G')

/**
 * Fetch order data based on order type
 */
const { data } = await useAsyncData('orderData', async () => {
  const endpoint = endsWithG 
    ? `newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&order_type=group&order_id=${route.params.id}`
    : `newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&order_type=sinlge&order_id=${Number(route.params.id)}`
  
  const { items } = await fetchHock(endpoint)
  return endsWithG ? items?.value?.grouped?.[0] || {} : items?.value?.single?.[0] || {}
})

// Update the trackOrderData state with the fetched data
trackOrderData.value = data.value

if (endsWithG) {
  if (trackOrderData?.value?.orders[0]?.geidea_transaction_id) {
    const data = await $fetch(`/api/orders/updateStatus`, {
      method: 'put',
      body: {
        customer_id: Number(userData?.value?.id),
        order_id: trackOrderData?.value?.group,
        status: 'confirmation'
      }
    })
    
    // Refetch order data after status update
    const endpoint = `newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&order_type=group&order_id=${route.params.id}`
    const { items } = await fetchHock(endpoint)
    trackOrderData.value = items?.value?.grouped?.[0] || {}
  }
} else {
  if (trackOrderData?.value?.geidea_transaction_id) {
    const data = await $fetch(`/api/orders/updateStatus`, {
      method: 'put',
      body: {
        customer_id: Number(userData?.value?.id),
        order_id: trackOrderData?.value?.id,
        status: 'confirmation'
      }
    })
    
    // Refetch order data after status update
    const endpoint = `newBranch/orders/userOrders/?user_id=${Number(userData?.value?.id)}&order_type=sinlge&order_id=${Number(route.params.id)}`
    const { items } = await fetchHock(endpoint)
    trackOrderData.value = items?.value?.single?.[0] || {}
  }
}

</script>