<template>
  <div>
    <UModal v-model="showVerificationModal" class="custom-color-for-modal">
      <div class="px-4 md:px-14 py-3 md:py-8 bg-thirdColor mb-[50px] md:mb-0">
        <h2 class="md:text-xl font-bold font-secondaryFont text-secondary mb-4 uppercase">verify phone number</h2>
        <!-- Existing verification code inputs -->
        <div class="space-y-4">
          <div class="flex items-center justify-between gap-2">
            <template v-for="(digit, index) in 6" :key="index">
              <div class="relative">
                <input
                  :ref="el => inputRefs[index] = el"
                  v-model="verificationCode[index]"
                  type="text"
                  maxlength="1"
                  :disabled="!codeSent"
                  :class="[
                    'md:w-12 md:h-12 w-[35px] h-[35px] border border-primary rounded text-center text-xl font-bold focus:outline-none relative z-10',
                    codeSent ? 'bg-thirdColor' : 'bg-gray-300 text-gray-500' // Change color if disabled
                  ]"
                  @input="handleInput(index)"
                  @keydown="handleKeydown($event, index)"
                />
                <span 
                  class="
                    absolute 
                    top-1/2 
                    left-1/2 
                    transform 
                    -translate-x-1/2 
                    -translate-y-1/2 
                    text-primary 
                    font-bold 
                    text-2xl
                    pointer-events-none
                    z-0
                    transition-opacity
                    duration-200
                  "
                  :class="{
                    'opacity-0': verificationCode[index],
                    'opacity-100': !verificationCode[index]
                  }"
                >
                  -
                </span>
              </div>
            </template>
          </div>
          <div v-if="error" class="font-secondaryFont" style="color: #FF0101;">The code is incorrect. Please try again.</div>
          <div v-if="success" class="font-secondaryFont" style="color: #4B9560;">The code is correct, and your number has been successfully verified.</div>
           



                  <!-- Add verification method selection -->
        <div class="mb-6 border-b border-primary">
          <div class="flex flex-col gap-3 mb-4">
            <label class="flex cursor-pointer">
              <input
                type="radio"
                v-model="verificationMethod"
                value="sms"
                class="hidden"
              />
              <span class="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center mr-2">
                <span 
                  class="w-3 h-3 rounded-full bg-primary"
                  :class="{ 'opacity-0': verificationMethod !== 'sms' }"
                ></span>
              </span>
              <span class="text-primary font-secondaryFont md:text-base text-[13px]"><span class="font-bold">SMS</span> “Receive a code via text message.”</span>
            </label>
            <label class="flex cursor-pointer">
              <input
                type="radio"
                v-model="verificationMethod"
                value="whatsapp"
                class="hidden"
              />
              <span class="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center mr-2">
                <span 
                  class="w-3 h-3 rounded-full bg-primary"
                  :class="{ 'opacity-0': verificationMethod !== 'whatsapp' }"
                ></span>
              </span>
              <span class="text-primary font-secondaryFont md:text-base text-[13px]"><span class="font-bold">WhatsApp</span> “Receive a code via WhatsApp message.”</span>
            </label>
          </div>
        </div>

        <div class="font-secondaryFont md:text-base text-[13px]" style="color: #1E1E1E;">
          You’ll receive a 6 digit code via your preferred method (SMS or WhatsApp). Enter the code above to complete the verification. The code is valid for 10 minutes.
        </div>












          <div class="flex justify-center">
            <UButton 
              class="bg-orangeColor text-thirdColor uppercase font-bold md:text-base text-[14px] md:py-3 py-2 px-8 md:px-14 rounded-full"
              @click="handleSendCode"
              :disabled="countdown > 0"
              :loading="verifyLoading"
            >
              SEND CODE
            </UButton>
          </div>
          <div class="text-center font-secondaryFont text-primary text-[13px]">
            {{ countdown > 0 ? `You can resend the code in ${countdown} seconds.` : 'You can now resend the code.' }}
          </div>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()
const showVerificationModal: any = useState("showVerificationModal", () => true)
const verifying = ref(false)
const verificationCode = ref(Array(6).fill(''))
const inputRefs = ref<HTMLInputElement[]>([])
const isCompleted = computed(() => verificationCode.value.every(digit => digit !== ''))
const codeStatus: any = useState("codeStatus", () => false)
const phoneNumber: any = useState("phoneNumber", () => '')
const error = ref(false)
const success = ref(false)
const formStatus: any = useState("formStatus", () => null)
const token: any = useState("token", () => '')
const userData: any = useState("userData", () => [])
const cartItems: any = useState("cartItems", () => [])
const cartFulltem: any = useState("cartFulltem", () => {})
const verifyLoading = ref(false)
const mobileCart: any = useState("mobileCart", () => null)
const codeSent = ref(false) // Track if the code has been sent

const verificationMethod = ref('')

const countdown = ref(0)
const countdownInterval = ref<NodeJS.Timeout | null>(null)

const handleInput = async (index: number) => {
  // Ensure single character
  if (verificationCode.value[index]?.length > 1) {
    verificationCode.value[index] = verificationCode.value[index].slice(0, 1)
  }
  
  // Auto focus next input
  if (verificationCode.value[index] && index < 5) {
    inputRefs.value[index + 1]?.focus()
  }

  // Check if all digits are filled
  if (isCompleted.value) {
    verifyLoading.value = true
    const verified = await $fetch('/api/register/verifyCode', {
      method: 'post',
      body: {
        phoneNumber: `+2${phoneNumber.value}`,
        code: verificationCode.value.join('')
      }
    })
    
    if (!verified.valid) {
      error.value = true
      success.value = false
      codeStatus.value = false
      verificationCode.value = Array(6).fill('')
      inputRefs.value[0]?.focus()
    } else {
      codeStatus.value = true
      success.value = true
      error.value = false

      const loginResponse = await $fetch('/api/login', {
        method: 'post',
        body: {
          username: formStatus.value.username,
          password: formStatus.value.password
        }
      }) as any

      const responses: any = await $fetch('/api/register/customVerify', {
        method: 'post',
        body: {
          user_id: loginResponse.user.data.ID
        }
      })
  

      if (loginResponse.response.login) {
        token.value = loginResponse.user.data.user_pass
        localStorage.setItem("token", loginResponse.user.data.user_pass)
        const orders = ref()
        const completeOrder = localStorage.getItem("completeOrder")
        orders.value = completeOrder ? JSON.parse(completeOrder) : null
        localStorage.removeItem("visitor_data")
        localStorage.removeItem("visitor_id")


        const { items } = await fetchHock(`orders/${Number(loginResponse.user.data.ID)}?status=pending-cart`)
        const { items: usersData } = await fetchHock(`shipping/${Number(loginResponse.user.data.ID)}`)

        userData.value = usersData.value
        localStorage.setItem("userData", JSON.stringify(usersData.value))

        cartItems.value = items?.value?.single[0]?.items
        cartFulltem.value = items?.value?.single[0]


        const transformedItems = orders.value.map((item: any) => ({
          product_id: item.product_id,
          quantity: item.quantity,
          variation_id: item.variation_id
        }))

        if (orders.value.length !== 0) {
          const createDefaultOrder = ref({
            customer_id: loginResponse.user.data.ID,
            status: 'pending-cart',
            line_items: transformedItems
          })

          const responsee = await $fetch('/api/orders/create', {
            method: 'post',
            body: createDefaultOrder.value
          }) as any

          cartItems.value = responsee.line_items
          cartFulltem.value = responsee
        }
        // Redirect to the home page
      }
      // Close modal
        closeModal()
    }
    verifyLoading.value = false
  }
}

const handleKeydown = (event: KeyboardEvent, index: number) => {
  // Handle backspace
  if (event.key === 'Backspace' && !verificationCode.value[index] && index > 0) {
    verificationCode.value[index - 1] = ''
    inputRefs.value[index - 1]?.focus()
  }
}

const closeModal = () => {
  router.push(mobileCart.value ? '/cart/mobile/address' : '/checkout')
  showVerificationModal.value = false
  verificationCode.value = Array(6).fill('')
}

const sendVerificationCode = async () => {
  try {
    if (verificationMethod.value === 'whatsapp') {
      await $fetch('/api/register/whatsAppVerify', {
        method: 'post',
        body: {
          phoneNumber: `+2${phoneNumber.value}`
        }
      })
    } else {
      await $fetch('/api/register/smsVerify', {
        method: 'post',
        body: {
          phoneNumber: `+2${phoneNumber.value}`
        }
      })
    }
  } catch (error) {
    console.error('Failed to send verification code:', error)
  }
}

const startCountdown = () => {
  countdown.value = 120 // 2 minutes in seconds
  if (countdownInterval.value) clearInterval(countdownInterval.value)
  
  countdownInterval.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      if (countdownInterval.value) clearInterval(countdownInterval.value)
    }
  }, 1000)
}

const handleSendCode = async () => {
  try {
    await sendVerificationCode()
    codeSent.value = true // Set to true after sending the code
    startCountdown()
  } catch (error) {
    console.error('Failed to send code:', error)
  }
}

// Clean up interval when component is unmounted
onBeforeUnmount(() => {
  if (countdownInterval.value) clearInterval(countdownInterval.value)
})

onMounted(() => {
  // Focus first input when modal opens
  inputRefs.value[0]?.focus()
})
</script>

<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

input {
  background: transparent;
}

.custom-color-for-modal .fixed.transition-opacity {
  background-color: #1D3C34D9 !important;
}
</style>