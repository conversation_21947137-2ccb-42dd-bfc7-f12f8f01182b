<template>
  <div>
    <div class="mt-[60px]">
      <div class="px-[20px] max-w-[1280px] mx-auto">
        <div class="bg-cartColor rounded-2xl p-10 text-black">
          <div class="flex gap-20 justify-between items-center">
            <div class="font-secondaryFont text-sm sm:text-lg text-primary rtl:font-notoSans">
              {{ locale === 'en' ? pagesContent?.checkout?.page_banner_desc : pagesContent?.checkout?.page_banner_desc_ar }}
            </div>
            <UButton
              class="
                rounded-full
                flex
                items-center
                justify-center
                font-bold
                sm:text-xl
                py-3
                sm:px-20
                px-14
                font-thirdFont
                text-thirdColor
                uppercase
                bg-orangeColor
                border-2
                rtl:font-notoSans
                border-orangeColor
                hover:bg-primary
                hover:border-primary
                transition-all
                duration-500
                ease-in-out"
              :to="localePath(`/${pagesContent?.checkout?.banner_link_slug}`)"
            >
              {{ locale === 'en' ? pagesContent?.checkout?.banner_link_text : pagesContent?.checkout?.banner_link_text_ar }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-[60px] mb-[60px]">
      <Checkout />
    </div>
  </div>
</template>
<script setup lang="ts">
const pagesContent: any = useState("pagesContent", () => {})
const { locale } = useI18n()
const localePath = useLocalePath()

</script>
<style>
</style>
