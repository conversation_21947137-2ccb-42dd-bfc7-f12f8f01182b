<template>
  <div>
    <div class="px-[20px] max-w-[1280px] mx-auto hidden md:block">
      <div class="bg-cartColor rounded-[12px] flex justify-between items-center flex-col-reverse md:flex-row gap-5 md:gap-0 mt-10 p-8">
        <div class="text-black text-xl font-secondaryFont md:w-[60%] rtl:font-notoSans">
          {{ locale === 'en' ? pagesContent['tips-and-tricks']?.page_banner_desc : pagesContent['tips-and-tricks']?.page_banner_desc_ar }}
        </div>
        <div class="flex gap-3 items-center">
          <a v-for="(item, index) in footer?.bottom_footer?.socia_media || []"
             :key="index"
             :href="item?.url"
             target="_blank"
             class="bg-orangeColor w-[45px] h-[45px] flex items-center justify-center rounded-full cursor-pointer">
            <div>
              <img :src="item?.icon" :alt="item?.platform || 'Social media'" class="w-[25px] h-[25px]" />
            </div>
          </a>
        </div>
      </div>
    </div>
    <Blogs @updatePage="updatePage" @loadMore="loadMore" />
  </div>
</template>

<script setup lang="ts">
import type { PagesContent } from '~/types/pagesContent'
import type { Footer } from '~/types/footer'

// Blog post interface
interface BlogPost {
  id: number;
  title_ar?: string;
  content_ar?: string;
  title: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  custom_large_crop?: string;
  link?: string;
  date?: string;
}

const { locale } = useI18n()

const pagesContent = useState<PagesContent>("pagesContent", () => ({} as PagesContent))
const footer = useState<Footer>("footer", () => ({} as Footer))

const page = useState<number>("page", () => 1)
const loadingBlogs = useState<boolean>('loadingBlogs', () => false)
const perPage = useState<number>("perPage", () => 6)
const paginationPages = useState<number>("paginationPages", () => 1)
// This state is used by the Blogs component internally
useState<number>("currentNumber", () => 1)
const blogs = useState<BlogPost[]>("blogs", () => [])
const isLoadMore = useState<boolean>('isLoadMore', () => false)
const loadingLoadMoreButton = useState<boolean>("loadingLoadMoreButton", () => false)

/**
 * Fetches blog posts data from the API
 */
const fetchData = async () => {
  loadingBlogs.value = true
  try {
    const { items } = await fetchHock(`newBranch/blogs?page=${page.value}&per_page=${perPage.value}`)

    if (items.value) {
      paginationPages.value = Number(items.value.pages) || 1

      // Check if we've loaded all available posts
      if (Number(perPage.value) === Number(items.value.posts)) {
        isLoadMore.value = true
      }

      blogs.value = items.value.data || []
    }
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    blogs.value = []
  } finally {
    loadingBlogs.value = false
  }
}

// Initial data fetch
await fetchData()

/**
 * Updates the current page and fetches new data
 * @param newPage The page number to navigate to
 */
const updatePage = async (newPage: number) => {
  page.value = newPage
  await fetchData()
}

/**
 * Loads more blog posts by increasing the per_page parameter
 * Uses a mutex pattern to prevent multiple simultaneous calls
 */
const loadMore = async () => {
  // If already loading, don't proceed with another request
  if (loadingLoadMoreButton.value) {
    return;
  }

  loadingLoadMoreButton.value = true;
  perPage.value = perPage.value + 6; // Increase by 6 posts each time
  page.value = 1;

  try {
    const { items } = await fetchHock(`newBranch/blogs?page=${page.value}&per_page=${perPage.value}`);

    if (items?.value) {
      paginationPages.value = Number(items.value.pages) || 1;

      // Check if we've loaded all available posts
      if (Number(perPage.value) >= Number(items.value.posts)) {
        isLoadMore.value = true;
      }

      blogs.value = items.value.data || [];
    }
  } catch (error) {
    console.error('Error loading more blog posts:', error);
    blogs.value = [];
  } finally {
    // Add a small delay before setting loading to false
    // This prevents rapid successive calls
    setTimeout(() => {
      loadingLoadMoreButton.value = false;
    }, 500);
  }
}
</script>