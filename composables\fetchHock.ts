// Function to get array data from database
export const fetchHock = async (endPoint: string, autoFetch: boolean = true, cacheConfig: { enabled?: boolean, ttl?: number, backgroundRefresh?: boolean } = {}) => {
  // variables
  const items = ref<any>([])
  const loading = ref<boolean>(false)
  const errors = ref<any>()
  const { getData } = useCache()

  // Reusable function to get data from database
  const fetchData = async () => {
    // Empty items when function trigger
    items.value = []

    try {
      const fetchFn = async () => {
        const { data, error, pending } = await useFetch(`/api/${endPoint}`, {
          lazy: false,
        })
        if (error.value !== null) {
          throw error.value
        }
        return data.value
      }

      const data = await getData(endPoint, undefined, fetchFn, cacheConfig)
      items.value = data
      loading.value = false
    } catch (error: any) {
      // To catch Error
      console.error(error)
      errors.value = error?.data || error
    }
  }

  if (autoFetch) {
    await fetchData()
  }

  return { items, loading, errors, fetchData }
}