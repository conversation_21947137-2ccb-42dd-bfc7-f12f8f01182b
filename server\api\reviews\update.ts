export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    
    const url = `${process.env.WP_API_URL}update_review`;
    const headers = {
      'Authorization': `Basic ${Buffer.from(`endpoint:oGDZ 7oDT B8ZV Jsqd yM7O KlJb`).toString('base64')}`,
      'Content-Type': 'application/json',
    }

    const response = await fetch(url, {
      method: 'PUT',
      headers: headers,
      body: JSON.stringify(body)
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error(error)
    return {
      error: (error as Error).message,
    }
  }
})
