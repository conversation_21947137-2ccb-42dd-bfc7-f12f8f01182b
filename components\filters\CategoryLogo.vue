<template>
  <div class="px-[20px] max-w-[1280px] mx-auto logoSwipper relative">
    <div
      class="sm:w-[95%] md:w-[90%] mx-auto"
      :class="{ 'less-than': filterData.brands.length <= 7 }"
    >
      <div class="top-slider-arrows hidden sm:block">
        <div
          class="
            swiper-button-prev-outside
            cursor-pointer
            absolute
            top-[40%]
            left-[22px]
          "
          v-if="filterData.brands.length >= 7"
          @click="swiperInstance.slideNext()"
        >
          <Icon class="cursor-pointer" name="carbon:chevron-left" size="30" />
        </div>
        <div
          class="
            swiper-button-next-outside
            cursor-pointer
            absolute
            top-[40%]
            right-[22px]
          "
          v-if="filterData.brands.length >= 7"
          @click="swiperInstance.slidePrev()"
        >
          <Icon class="cursor-pointer" name="carbon:chevron-right" size="30" />
        </div>
      </div>
      <Swiper
        @swiper="onSwiper"
        :modules="[SwiperAutoplay, SwiperEffectCreative, SwiperPagination]"
        :space-between="20"
        :autoplay="{
          delay: 9000,
          disableOnInteraction: false,
        }"
        :breakpoints="{
          310: {
              slidesPerView: 2,
          },
          500: {
              slidesPerView: 3,
          },
          768: {
              slidesPerView: 4,
          },
          900: {
              slidesPerView: 5,
          },
          1060: {
              slidesPerView: 7,
          },
          1600: {
              slidesPerView: 7,
          },
          1920: {
              slidesPerView: 7,
          },
          2560: {
              slidesPerView: 7,
          },
          3200: {
              slidesPerView: 7,
          },
        }"
      >
        <SwiperSlide
          v-for="(item, index) in filterData.brands ? filterData.brands : items"
          :key="index"
        >
          <div class="mx-auto">
            <button
              draggable="false"
              class="bg-white uppercase truncate mx-auto text-center w-[120px] h-[120px] p-5 rounded-full flex items-center justify-between border"
              :class="{
                'border-primary': brandId !== item.brand_id,
                '!border-orangeColor': brandId == item.brand_id,
                'opacity-50': !item.status
              }"
              @click="selectBrand(item.brand_id)"
              :disabled="!item.status"
            >
              <img v-if="!loadingCategory" :src="item.brand_logo ? item.brand_logo : item" alt="Logo">
              <el-skeleton animated v-else>
                <template #template>
                  <el-skeleton-item variant="image" />
                </template>
              </el-skeleton>
            </button>
          </div>
        </SwiperSlide>
      </Swiper>
    </div>
  </div>
</template>

<script setup lang="ts">
import logo1 from '~/assets/images/home/<USER>/1.png'
import logo2 from '~/assets/images/home/<USER>/2.png'
import logo3 from '~/assets/images/home/<USER>/3.png'
import logo4 from '~/assets/images/home/<USER>/4.png'
import logo5 from '~/assets/images/home/<USER>/5.png'
import logo6 from '~/assets/images/home/<USER>/6.png'

const emit = defineEmits(['onSelect'])

const filterData: any = useState('filterData', () => {})
const brandId =  useState<any>('brandId', () => '')
const loadingCategory: any = useState<boolean>('loadingCategory', () => false)

const selectBrand = (item: string) => {
  // If the brand is already selected, deselect it
  if (brandId.value === item) {
    brandId.value = ''
  } else {
    // Otherwise, select the new brand
    brandId.value = item
  }
  // Emit an event to fetch filtered items
  emit('onSelect')
}

const items = [
  logo1,
  logo2,
  logo3,
  logo4,
  logo5,
  logo6,
  logo1,
  logo2,
  logo3,
  logo4,
  logo5,
  logo6,
]

// Swipper settings
const swiperInstance = ref()
const onSwiper = (swiper: any) => (swiperInstance.value = swiper)

onMounted(async () => {
  await nextTick(); // Wait for the DOM to update

  // Find all elements with the class 'swiper-slide'
  const swiperSlides = document.querySelectorAll('.logoSwipper');

  // Attach event listeners to each swiper-slide element
  swiperSlides.forEach(slide => {
    slide.addEventListener('mouseover', onMouseOver)
    slide.addEventListener('mouseleave', onMouseLeave)
    slide.addEventListener('touchstart', onMouseOver)
    slide.addEventListener('touchend', onMouseLeave)
  })
})

function onMouseOver(event: any) {
  swiperInstance.value.autoplay.stop()
}

function onMouseLeave(event: any) {
  swiperInstance.value.autoplay.start()
}
</script>
<style>
.less-than .swiper-wrapper {
  justify-content: center !important;
}
</style>