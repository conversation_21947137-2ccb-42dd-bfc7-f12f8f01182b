import config from '../../../../config.json'

export default defineEventHandler(async (event) => {
   try {
    const queryId: any = event.context.params.id

    const queryObject = getQuery(event)

    const queryString = Object.entries(queryObject).map(([key, value]) => {
       // Check if the value is not undefined, not null, and not an array
       if (value!== undefined && value!== null &&!Array.isArray(value)) {
         // If the value is defined, encoded, and not an array, encode both the key and the value
         // Convert value to a string if it's an object
         const stringValue = typeof value === 'object'? JSON.stringify(value) : value;
         return `${encodeURIComponent(key)}=${encodeURIComponent(stringValue)}`;
       }
       // If the value is undefined, null, or an array, return an empty string
       return '';
     }).filter(Boolean).join('&'); // Filter out empty strings and join the array of strings into a single string with '&' as the separator

    // const url = `${process.env.WC_API_URL}products/${queryId}`
    const url = `${process.env.WC_API_URL}get_attrs?category=${queryId}&${queryString}`;
    const method = 'GET'
    
    const headers = {
      'Authorization': `Basic ${Buffer.from(`ck_da29de315a5e4c5d1873adfd87a3d30db0fce742:cs_0088f2bd53cb7d47c8758217a122f4c24e1c4130`).toString('base64')}`,
      'Content-Type': 'application/json', // or 'text/plain', depending on your API
    }

      const response = await fetch(url, {
        method: method,
        headers: headers,
      })
  
      const data = await response.json()
      return data
  } catch (error) {
     console.error(error)
     return {
       error: (error as Error).message,
     }
  }
 })
